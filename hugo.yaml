baseURL: https://whitesky.cloud
languageCode: en-us
title: whitesky cloud platform
theme: hugo-fresh

googleAnalytics: # Put in your tracking code without quotes like this: UA-XXX for universal tracking or G-XXX analytics v4 tracking
# Disables warnings
disableKinds:
- taxonomy
markup:
  goldmark:
    renderer:
      unsafe: true # Allows you to write raw html in your md files

params:
  # Open graph allows easy social sharing. If you don't want it you can set it to false or just delete the variable
  openGraph: true
  # Used as meta data; describe your site to make Google Bots happy
  description:
  # Preloader ensures images are loaded before displaying to the user. If you don't want it uncomment to set it to false
  # preloader: false
  navbarlogo:
    image: logo-blue-blacktext-small.png
    link: /
    width: 160
    height: 28
  font:
    name: "Open Sans"
    sizes: [400,600]
  hero:
    # Main hero title
    title: The most complete cloud stack, delivered as-a-service
    # Hero subtitle (optional)
    subtitle: Built on Linux and the KVM hypervisor. Every component — from the virtual machine orchestration layer and the software defined storage to the billing engine — is engineered and maintained by whitesky.
    # Button text
    buttontext: Try now
    # Where the main hero button links to
    buttonlink: "#"
    # Hero image (from static/images/___)
    image: cloud-worker-small.jpg
    # Footer logos (from static/images/logos/clients/*.svg)
    # urls are optional
    clientlogos:
    - logo: america-movil-gray
      url: https://www.americamovil.com/
    - logo: circlesco-logo-2
      url: https://circles.co
    - logo: exalate
      url: https://exalate.com
    - logo: leal
      url: https://www.lealgroup.mu
    - logo: varity
      url: https://www.varity.nl
  # Customizable navbar. For a dropdown, add a "sublinks" list.
  navbar:
  - title: Features
    url: /
  - title: Pricing
    url: /
  - title: Dropdown
    sublinks:
    - title: Dropdown item
      url: /
    - title: Dropdown item
      url: /
    - title: Dropdown item
      url: /
  - title: Log in
    url: /
  - title: Try now
    url: /
    button: true
  # sidebar:
  #   # Logo (from static/images/logos/___.svg)
  #   logo: fresh-square
  #   sections:
  #   - title: User
  #     icon: user
  #     links:
  #     - text: Profile
  #       url: /
  #     - text: Account
  #       url: /
  #     - text: Settings
  #       url: /
  #   - title: Messages
  #     icon: envelope
  #     links:
  #     - text: Inbox
  #       url: /
  #     - text: Compose
  #       url: /
  #   - title: Images
  #     icon: image
  #     links:
  #     - text: Library
  #       url: /
  #     - text: Upload
  #       url: /
  #   - title: Settings
  #     icon: cog
  #     links:
  #     - text: User settings
  #       url: /
  #     - text: App settings
  #       url: /
  section1:
    title: The alternative providing sovereignty with comfort
    subtitle: It feels like cloud, but it runs on your hardware in your (colo) datacenter.
    tiles:
    - title: Complete Cloud Environment
      icon: fa-cloud
      text: The European answer to VMware, Nutanix, OpenStack and others — built for flexibility, sovereignty, performance and cooperation.
      #url: /
      #buttonText: Free trial
    - title: Seamless Transition
      icon: fa-right-left
      text: Migrate from legacy systems or US-based hyperscalers with minimal disruption — whitesky.cloud offers a smooth, sovereign path forward.
      url: /
      buttonText: Get started
    - title: Customized Deployment
      icon: fa-tools
      text: Unlike one-size-fits-all hyperscalers, we tailor deployments to your specific needs — ensuring full alignment with your organization and compliance requirements.
      url: /
      buttonText: Get started
  section2:
    title: A Vanilla Cloud Stack
    subtitle: We believe in choice — not lock-in. Our platform earns loyalty through performance, not constraints.
    features:
    - title: Cloudspaces
      text: "<u>Fully isolated virtual environments</u> where you deploy and manage <u>virtual machines</u>, <u>VGPUs</u>, <u>networking</u>, <u>load balancers</u>, <u>reverse proxies</u>, <u>backups</u>, <u>DNS</u>, <u>SSL certificates</u>, <u>vTPM</u>, <u>secure boot</u> and storage — all from a single interface. Cloudspaces give you granular control, anti-affinity policies, and the ability to attach both out-of-the-box software-defined and direct NVMe storage to your workloads."
      # Icon (Font Awesome icon class)
      icon: fa-cloud
    - title: Objectspaces
      text: Scalable, S3-compatible object storage with versioning and object locking built in. Objectspaces allow you to store, protect, and serve unstructured data reliably — whether you're backing up virtual machines, hosting static websites, or integrating with cloud-native apps. A single deployment can scale up to 360PB.
      icon: fa-box
    - title: Containerspaces
      text: Fully managed Kubernetes clusters with built-in multi-site and geo-redundancy capabilities. Containerspaces let you run containerized workloads close to your users, across federated whitesky.cloud locations — with seamless integration into the same portal, billing, and networking as your virtual machines and storage.
      icon: fa-cubes
    - title: Billing system
      text: Built-in billing and invoicing system designed for MSPs, SaaS providers, and internal IT teams. Whether you're reselling cloud capacity or allocating internal costs, the billing engine supports usage-based metering, customer invoicing, reseller onboarding, and federation with other whitesky.cloud providers — all integrated into the platform.
      icon: fa-receipt
  section3:
    title: One platform
    subtitle: To rule them all
    image: illustrations/mockups/app-mockup.png
    buttonText: Get started
    buttonLink: "#"
  section4:
    title: Our Clients love us!
    subtitle: Lorem ipsum sit dolor amet is a dummy text used by typography industry
    clients:
    - name: Irma Walters
      quote: Lorem ipsum dolor sit amet, elit deleniti dissentias quo eu, hinc minim appetere te usu, ea case duis scribentur has. Duo te consequat elaboraret, has quando suavitate at.
      job: Accountant
      img: 1
    - name: John Bradley
      quote: Lorem ipsum dolor sit amet, elit deleniti dissentias quo eu, hinc minim appetere te usu, ea case duis scribentur has. Duo te consequat elaboraret, has quando suavitate at.
      job: Financial Analyst
      img: 2
    - name: Gary Blackman
      quote: Lorem ipsum dolor sit amet, elit deleniti dissentias quo eu, hinc minim appetere te usu, ea case duis scribentur has. Duo te consequat elaboraret, has quando suavitate at.
      job: HR Manager
      img: 3
  section5:
    title: Drop us a line or two
    subtitle: We'd love to hear from you
    buttonText: Send Message
    # action: https://formspree.io/f/<form_id>
    # method: POST
  footer:
    # Logo (from static/images/logos/___)
    logo: fresh-white-alt.svg
    # Social Media Title
    socialmediatitle: Follow Us
    # Social media links (GitHub, Twitter, etc.). All are optional.
    socialmedia:
    - link: https://github.com/StefMa
      # Icons are from Font Awesome
      icon: github
    - link: https://dribbble.com/#
      icon: dribbble
    - link: https://facebook.com/#
      icon: facebook
    - link: https://twitter.com/StefMa91
      icon: twitter
    - link: https://bitbucket.org/#
      icon: bitbucket
    bulmalogo: true
    quicklinks:
      column1:
        title: "Product"
        links:
        - text: Discover features
          link: /
        - text: Why choose our product?
          link: /
        - text: Compare features
          link: /
        - text: Our roadmap
          link: /
        - text: AGB
          link: /agb
      column2:
        title: "Docs"
        links:
        - text: Get started
          link: /
        - text: User guides
          link: /
        - text: Admin guide
          link: /
        - text: Developers
          link: /
      column3:
        title: "Blog"
        links:
        - text: Latest news
          link: /blog/first
        - text: Tech articles
          link: /blog/second
