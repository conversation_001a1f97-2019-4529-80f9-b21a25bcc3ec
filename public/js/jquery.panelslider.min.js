(function(e){"use strict";function r(r){var i=r.data("ps-options");if(e("body").hasClass(i.bodyClass)||n)return;r.trigger("psBeforeOpen"),n=!0,r.addClass("ps-active-panel"),e("body").addClass(i.bodyClass).one(t,function(e){n=!1,r.trigger("psOpen"),typeof i.onOpen=="function"&&i.onOpen()})}var t=["transitionend","webkitTransitionEnd","oTransitionEnd","MSTransitionEnd"].join(" "),n=!1;e.panelslider=function(e,t){e.panelslider(t)},e.panelslider.close=function(r){var i=e(".ps-active-panel"),s=i.data("ps-options");if(!i.length||n)return;i.trigger("psBeforeClose"),n=!0,i.removeClass("ps-active-panel"),e("body").removeClass(s.bodyClass).one(t,function(e){n=!1,i.trigger("psClose"),r&&setTimeout(function(){r()},0)})},e(document).on("click keyup",function(t){var n=e(".ps-active-panel");if(t.type=="keyup"&&t.keyCode!=27)return;n.length&&n.data("ps-options").clickClose&&e.panelslider.close()}),e(document).on("click",".ps-active-panel",function(e){e.stopPropagation()}),e.fn.panelslider=function(t){var n={bodyClass:"ps-active",clickClose:!0,onOpen:null},i=e(this.attr("href"));return i.data("ps-options",e.extend({},n,t)),this.click(function(t){var n=e(".ps-active-panel");n.length?n[0]==i[0]?e.panelslider.close():e.panelslider.close(function(){r(i)}):r(i),t.preventDefault(),t.stopPropagation()}),this}})(jQuery);