{{- $inServerMode := "" }}
{{- if gt (int (index (split hugo.Version ".") 1)) "120" }}
  {{ $inServerMode = hugo.IsServer }}
{{- else }}
  {{ $inServerMode = .Site.IsServer }}
{{- end }}
{{- $sass         := "style.sass" }}
{{- $cssTarget    := "css/style.css" }}
{{- $cssOpts      := cond ($inServerMode) (dict "targetPath" $cssTarget "enableSourceMap" true) (dict "targetPath" $cssTarget "outputStyle" "compressed") }}
{{- $fontName     := .Site.Params.font.name | default "Open Sans" }}
{{- $fontFace     := replace $fontName " " "+" }}
{{- $fontSizes    := delimit (.Site.Params.font.sizes | default (slice 300 400 600 700)) "," }}
{{- $fontUrl      := printf "https://fonts.googleapis.com/css?family=%s:%s" $fontFace $fontSizes }}
<link
  rel="stylesheet"
  href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css"
  integrity="sha512-Avb2QiuDEEvB4bZJYdft2mNjVShBftLdPG8FJ0V7irTLQ8Uo0qcPxh4Plq7G5tGm0rU+1SPhVotteLpBERwTkw=="
  crossorigin="anonymous"
  referrerpolicy="no-referrer"
/>
<link rel="icon" type="image/png" href="{{ "images/favicon.png" | relURL }}" />
<link href="{{ $fontUrl }}" rel="stylesheet">
{{- if $inServerMode }}
{{- $css := resources.Get $sass | toCSS $cssOpts }}
<link rel="stylesheet" type="text/css" href="{{ $css.RelPermalink }}">
{{- else }}
{{- $css := resources.Get $sass | toCSS $cssOpts | minify | fingerprint }}
<link rel="stylesheet" type="text/css" href="{{ $css.RelPermalink }}" integrity="{{ $css.Data.Integrity }}">
{{- end }}
<link rel="stylesheet" type="text/css" href="{{ "css/icons.css" | relURL }}">
