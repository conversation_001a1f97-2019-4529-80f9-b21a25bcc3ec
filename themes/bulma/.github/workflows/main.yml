name: End-to-end tests
on: [push]
jobs:
  cypress-run:
    runs-on: ubuntu-20.04
    steps:
      - name: Checkout
        uses: actions/checkout@v2
      - uses: ruby/setup-ruby@v1
        with:
          ruby-version: 2.7
      - name: Install dependencies
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          gem install jekyll bundler
      - name: Cypress run
        uses: cypress-io/github-action@v2
        with:
          working-directory: docs
          install-command: npm install
          build: npm run bulma-sass
          start: jekyll serve --host 127.0.0.1 --port 4000
          wait-on: "http://127.0.0.1:4000"
