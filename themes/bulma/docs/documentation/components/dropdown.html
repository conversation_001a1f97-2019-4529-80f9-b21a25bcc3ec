---
title: Dropdown
layout: documentation
doc-tab: components
doc-subtab: dropdown
breadcrumb:
- home
- documentation
- components
- components-dropdown
meta:
  colors: false
  sizes: false
  variables: true
---

{% capture dropdown_example %}
<div class="dropdown is-active">
  <div class="dropdown-trigger">
    <button class="button" aria-haspopup="true" aria-controls="dropdown-menu">
      <span>Dropdown button</span>
      <span class="icon is-small">
        <i class="fas fa-angle-down" aria-hidden="true"></i>
      </span>
    </button>
  </div>
  <div class="dropdown-menu" id="dropdown-menu" role="menu">
    <div class="dropdown-content">
      <a href="#" class="dropdown-item">
        Dropdown item
      </a>
      <a class="dropdown-item">
        Other dropdown item
      </a>
      <a href="#" class="dropdown-item is-active">
        Active dropdown item
      </a>
      <a href="#" class="dropdown-item">
        Other dropdown item
      </a>
      <hr class="dropdown-divider">
      <a href="#" class="dropdown-item">
        With a divider
      </a>
    </div>
  </div>
</div>
{% endcapture %}

{% capture dropdown_content_example %}
<div class="dropdown is-active">
  <div class="dropdown-trigger">
    <button class="button" aria-haspopup="true" aria-controls="dropdown-menu2">
      <span>Content</span>
      <span class="icon is-small">
        <i class="fas fa-angle-down" aria-hidden="true"></i>
      </span>
    </button>
  </div>
  <div class="dropdown-menu" id="dropdown-menu2" role="menu">
    <div class="dropdown-content">
      <div class="dropdown-item">
        <p>You can insert <strong>any type of content</strong> within the dropdown menu.</p>
      </div>
      <hr class="dropdown-divider">
      <div class="dropdown-item">
        <p>You simply need to use a <code>&lt;div&gt;</code> instead.</p>
      </div>
      <hr class="dropdown-divider">
      <a href="#" class="dropdown-item">
        This is a link
      </a>
    </div>
  </div>
</div>
{% endcapture %}

{% capture dropdown_click_example %}
<div class="dropdown">
  <div class="dropdown-trigger">
    <button class="button" aria-haspopup="true" aria-controls="dropdown-menu3">
      <span>Click me</span>
      <span class="icon is-small">
        <i class="fas fa-angle-down" aria-hidden="true"></i>
      </span>
    </button>
  </div>
  <div class="dropdown-menu" id="dropdown-menu3" role="menu">
    <div class="dropdown-content">
      <a href="#" class="dropdown-item">
        Overview
      </a>
      <a href="#" class="dropdown-item">
        Modifiers
      </a>
      <a href="#" class="dropdown-item">
        Grid
      </a>
      <a href="#" class="dropdown-item">
        Form
      </a>
      <a href="#" class="dropdown-item">
        Elements
      </a>
      <a href="#" class="dropdown-item">
        Components
      </a>
      <a href="#" class="dropdown-item">
        Layout
      </a>
      <hr class="dropdown-divider">
      <a href="#" class="dropdown-item">
        More
      </a>
    </div>
  </div>
</div>
{% endcapture %}

{% capture dropdown_info_example %}
<div class="dropdown is-hoverable">
  <div class="dropdown-trigger">
    <button class="button" aria-haspopup="true" aria-controls="dropdown-menu4">
      <span>Hover me</span>
      <span class="icon is-small">
        <i class="fas fa-angle-down" aria-hidden="true"></i>
      </span>
    </button>
  </div>
  <div class="dropdown-menu" id="dropdown-menu4" role="menu">
    <div class="dropdown-content">
      <div class="dropdown-item">
        <p>You can insert <strong>any type of content</strong> within the dropdown menu.</p>
      </div>
    </div>
  </div>
</div>
{% endcapture %}

{% capture dropdown_left_example %}
<div class="dropdown is-active">
  <div class="dropdown-trigger">
    <button class="button" aria-haspopup="true" aria-controls="dropdown-menu5">
      <span>Left aligned</span>
      <span class="icon is-small">
        <i class="fas fa-angle-down" aria-hidden="true"></i>
      </span>
    </button>
  </div>
  <div class="dropdown-menu" id="dropdown-menu5" role="menu">
    <div class="dropdown-content">
      <div class="dropdown-item">
        <p>The dropdown is <strong>left-aligned</strong> by default.</p>
      </div>
    </div>
  </div>
</div>
{% endcapture %}

{% capture dropdown_right_example %}
<div class="dropdown is-right is-active">
  <div class="dropdown-trigger">
    <button class="button" aria-haspopup="true" aria-controls="dropdown-menu6">
      <span>Right aligned</span>
      <span class="icon is-small">
        <i class="fas fa-angle-down" aria-hidden="true"></i>
      </span>
    </button>
  </div>
  <div class="dropdown-menu" id="dropdown-menu6" role="menu">
    <div class="dropdown-content">
      <div class="dropdown-item">
        <p>Add the <code>is-right</code> modifier for a <strong>right-aligned</strong> dropdown.</p>
      </div>
    </div>
  </div>
</div>
{% endcapture %}

{% capture dropdown_up_example %}
<div class="dropdown is-up">
  <div class="dropdown-trigger">
    <button class="button" aria-haspopup="true" aria-controls="dropdown-menu7">
      <span>Dropup button</span>
      <span class="icon is-small">
        <i class="fas fa-angle-up" aria-hidden="true"></i>
      </span>
    </button>
  </div>
  <div class="dropdown-menu" id="dropdown-menu7" role="menu">
    <div class="dropdown-content">
      <div class="dropdown-item">
        <p>You can add the <code>is-up</code> modifier to have a dropdown menu that appears above the dropdown button.</p>
      </div>
    </div>
  </div>
</div>
{% endcapture %}

<div class="content">
  <p>
    The <code>dropdown</code> component is a container for a dropdown button and a dropdown menu.
  </p>
  <ul>
    <li>
      <code>dropdown</code> the <strong>main</strong> container
      <ul>
        <li>
          <code>dropdown-trigger</code> the container for a <code>button</code>
        </li>
        <li>
          <code>dropdown-menu</code> the toggable menu, <strong>hidden</strong> by default
          <ul>
            <li>
              <code>dropdown-content</code> the dropdown <strong>box</strong>, with a white background and a shadow
              <ul>
                <li>
                  <code>dropdown-item</code> each <strong>single item</strong> of the dropdown, which can either be a <code>a</code> or a <code>div</code>
                </li>
                <li>
                  <code>dropdown-divider</code> a <strong>horizontal line</strong> to separate dropdown items
                </li>
              </ul>
            </li>
          </ul>
        </li>
      </ul>
    </li>
  </ul>
</div>

{% include elements/snippet.html content=dropdown_example %}

{% include elements/anchor.html name="Dropdown content" %}

<div class="content">
  <p>
    While the <code>dropdown-item</code> can be used as an anchor link <code>&lt;a&gt;</code>, you can also use a <code>&lt;div&gt;</code> and insert almost <strong>any type of content</strong>.
  </p>
</div>

{% include elements/snippet.html content=dropdown_content_example %}

{% include elements/anchor.html name="Hoverable or Toggable" %}

<div class="content">
  <p>
    The <code>dropdown</code> component has <strong>2 additional modifiers</strong>
  </p>
  <ul>
    <li>
      <code>is-hoverable</code>: the dropdown will show up when <strong>hovering</strong> the <code>dropdown-trigger</code>
    </li>
    <li>
      <code>is-active</code>: the dropdown will show up <strong>all the time</strong>
    </li>
  </ul>
</div>

<div class="message is-success">
  <p class="message-body">
    While the CSS <code>:hover</code> implementation works perfectly, the <code>is-active</code> class is available for users who want to control the display of the dropdown with <strong>JavaScript</strong>.
  </p>
</div>

{% include elements/snippet.html content=dropdown_click_example more=true %}

{% include elements/snippet.html content=dropdown_info_example more=true %}

{% include elements/anchor.html name="Right aligned" %}

<div class="content">
  <p>
    You can add the <code>is-right</code> modifier to have a <strong>right-aligned</strong> dropdown.
  </p>
</div>

{% include elements/snippet.html content=dropdown_left_example more=true %}

{% include elements/snippet.html content=dropdown_right_example more=true %}

{% include elements/anchor.html name="Dropup" %}

<div class="content">
  <p>
    You can add the <code>is-up</code> modifier to have a dropdown menu that appears above the dropdown button.
  </p>
</div>

{% include elements/snippet.html content=dropdown_up_example more=true %}

{% include components/variables.html type='component' %}
