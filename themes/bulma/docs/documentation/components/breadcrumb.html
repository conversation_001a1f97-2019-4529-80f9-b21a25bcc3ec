---
title: Breadcrumb
layout: documentation
doc-tab: components
doc-subtab: breadcrumb
breadcrumb:
- home
- documentation
- components
- components-breadcrumb
meta:
  variables: true
  colors: false
  sizes: true
---

{% capture breadcrumb_example %}
<nav class="breadcrumb" aria-label="breadcrumbs">
  <ul>
    <li><a href="#">Bulma</a></li>
    <li><a href="#">Documentation</a></li>
    <li><a href="#">Components</a></li>
    <li class="is-active"><a href="#" aria-current="page">Breadcrumb</a></li>
  </ul>
</nav>
{% endcapture %}

{% capture breadcrumb_centered_example %}
<nav class="breadcrumb is-centered" aria-label="breadcrumbs">
  <ul>
    <li><a href="#">Bulma</a></li>
    <li><a href="#">Documentation</a></li>
    <li><a href="#">Components</a></li>
    <li class="is-active"><a href="#" aria-current="page">Breadcrumb</a></li>
  </ul>
</nav>
{% endcapture %}

{% capture breadcrumb_right_example %}
<nav class="breadcrumb is-right" aria-label="breadcrumbs">
  <ul>
    <li><a href="#">Bulma</a></li>
    <li><a href="#">Documentation</a></li>
    <li><a href="#">Components</a></li>
    <li class="is-active"><a href="#" aria-current="page">Breadcrumb</a></li>
  </ul>
</nav>
{% endcapture %}

{% capture breadcrumb_icons_example %}
<nav class="breadcrumb" aria-label="breadcrumbs">
  <ul>
    <li>
      <a href="#">
        <span class="icon is-small">
          <i class="fas fa-home" aria-hidden="true"></i>
        </span>
        <span>Bulma</span>
      </a>
    </li>
    <li>
      <a href="#">
        <span class="icon is-small">
          <i class="fas fa-book" aria-hidden="true"></i>
        </span>
        <span>Documentation</span>
      </a>
    </li>
    <li>
      <a href="#">
        <span class="icon is-small">
          <i class="fas fa-puzzle-piece" aria-hidden="true"></i>
        </span>
        <span>Components</span>
      </a>
    </li>
    <li class="is-active">
      <a href="#">
        <span class="icon is-small">
          <i class="fas fa-thumbs-up" aria-hidden="true"></i>
        </span>
        <span>Breadcrumb</span>
      </a>
    </li>
  </ul>
</nav>
{% endcapture %}

{% capture breadcrumb_small_example %}
<nav class="breadcrumb is-small" aria-label="breadcrumbs">
  <ul>
    <li><a href="#">Bulma</a></li>
    <li><a href="#">Documentation</a></li>
    <li><a href="#">Components</a></li>
    <li class="is-active"><a href="#" aria-current="page">Breadcrumb</a></li>
  </ul>
</nav>
{% endcapture %}

{% capture breadcrumb_medium_example %}
<nav class="breadcrumb is-medium" aria-label="breadcrumbs">
  <ul>
    <li><a href="#">Bulma</a></li>
    <li><a href="#">Documentation</a></li>
    <li><a href="#">Components</a></li>
    <li class="is-active"><a href="#" aria-current="page">Breadcrumb</a></li>
  </ul>
</nav>
{% endcapture %}

{% capture breadcrumb_large_example %}
<nav class="breadcrumb is-large" aria-label="breadcrumbs">
  <ul>
    <li><a href="#">Bulma</a></li>
    <li><a href="#">Documentation</a></li>
    <li><a href="#">Components</a></li>
    <li class="is-active"><a href="#" aria-current="page">Breadcrumb</a></li>
  </ul>
</nav>
{% endcapture %}

{% capture breadcrumb_arrow_example %}
<nav class="breadcrumb has-arrow-separator" aria-label="breadcrumbs">
  <ul>
    <li><a href="#">Bulma</a></li>
    <li><a href="#">Documentation</a></li>
    <li><a href="#">Components</a></li>
    <li class="is-active"><a href="#" aria-current="page">Breadcrumb</a></li>
  </ul>
</nav>
{% endcapture %}

{% capture breadcrumb_bullet_example %}
<nav class="breadcrumb has-bullet-separator" aria-label="breadcrumbs">
  <ul>
    <li><a href="#">Bulma</a></li>
    <li><a href="#">Documentation</a></li>
    <li><a href="#">Components</a></li>
    <li class="is-active"><a href="#" aria-current="page">Breadcrumb</a></li>
  </ul>
</nav>
{% endcapture %}

{% capture breadcrumb_dot_example %}
<nav class="breadcrumb has-dot-separator" aria-label="breadcrumbs">
  <ul>
    <li><a href="#">Bulma</a></li>
    <li><a href="#">Documentation</a></li>
    <li><a href="#">Components</a></li>
    <li class="is-active"><a href="#" aria-current="page">Breadcrumb</a></li>
  </ul>
</nav>
{% endcapture %}

{% capture breadcrumb_succeeds_example %}
<nav class="breadcrumb has-succeeds-separator" aria-label="breadcrumbs">
  <ul>
    <li><a href="#">Bulma</a></li>
    <li><a href="#">Documentation</a></li>
    <li><a href="#">Components</a></li>
    <li class="is-active"><a href="#" aria-current="page">Breadcrumb</a></li>
  </ul>
</nav>
{% endcapture %}

<div class="content">
  <p>
    The Bulma <strong>breadcrumb</strong> is a simple navigation component that only requires a <code>breadcrumb</code> container and a <code>ul</code> list. The dividers are automatically created in the content of the <code>::before</code> pseudo-element of <code>li</code> tags.
  </p>
  <p>You can inform the current page using the <code>is-active</code> modifier in a <code>li</code> tag. It will disable the navigation of inner links.</p>
</div>

<hr>
{% include elements/snippet.html content=breadcrumb_example horizontal=true clipped=true %}

{% include elements/anchor.html name="Alignment" %}

<div class="content">
  <p>For alternative alignments, use the <code>is-centered</code> and <code>is-right</code> modifiers on the <code>breadcrumb</code> container.</p>
</div>

{% include elements/snippet.html content=breadcrumb_centered_example horizontal=true clipped=true %}

{% include elements/snippet.html content=breadcrumb_right_example horizontal=true clipped=true %}

{% include elements/anchor.html name="Icons" %}

<div class="content">
  <p>You can use any of the <a href="https://fontawesome.com/" target="_blank">Font Awesome</a> <strong>icons</strong>.</p>
</div>

{% include elements/snippet.html content=breadcrumb_icons_example horizontal=true clipped=true %}

{% include elements/anchor.html name="Alternative separators" %}

<div class="content">
  <p>You can choose between <strong>4 additional separators</strong>: <code>has-arrow-separator</code> <code>has-bullet-separator</code> <code>has-dot-separator</code> and <code>has-succeeds-separator</code>.</p>
</div>

{% include elements/snippet.html content=breadcrumb_arrow_example horizontal=true clipped=true %}

{% include elements/snippet.html content=breadcrumb_bullet_example horizontal=true clipped=true %}

{% include elements/snippet.html content=breadcrumb_dot_example horizontal=true clipped=true %}

{% include elements/snippet.html content=breadcrumb_succeeds_example horizontal=true clipped=true %}

{% include elements/anchor.html name="Sizes" %}

<div class="content">
  <p>You can choose between <strong>3 additional sizes</strong>: <code>is-small</code> <code>is-medium</code> and <code>is-large</code>.</p>
</div>

{% include elements/snippet.html content=breadcrumb_small_example horizontal=true clipped=true %}

{% include elements/snippet.html content=breadcrumb_medium_example horizontal=true clipped=true %}

{% include elements/snippet.html content=breadcrumb_large_example horizontal=true clipped=true %}

{% include components/variables.html type='component' %}
