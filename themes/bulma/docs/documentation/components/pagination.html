---
title: Pagination
layout: documentation
doc-tab: components
doc-subtab: pagination
breadcrumb:
- home
- documentation
- components
- components-pagination
meta:
  colors: false
  sizes: true
  variables: true
---

{% capture pagination_example %}
<nav class="pagination" role="navigation" aria-label="pagination">
  <a class="pagination-previous">Previous</a>
  <a class="pagination-next">Next page</a>
  <ul class="pagination-list">
    <li>
      <a class="pagination-link" aria-label="Goto page 1">1</a>
    </li>
    <li>
      <span class="pagination-ellipsis">&hellip;</span>
    </li>
    <li>
      <a class="pagination-link" aria-label="Goto page 45">45</a>
    </li>
    <li>
      <a class="pagination-link is-current" aria-label="Page 46" aria-current="page">46</a>
    </li>
    <li>
      <a class="pagination-link" aria-label="Goto page 47">47</a>
    </li>
    <li>
      <span class="pagination-ellipsis">&hellip;</span>
    </li>
    <li>
      <a class="pagination-link" aria-label="Goto page 86">86</a>
    </li>
  </ul>
</nav>
{% endcapture %}

{% capture pagination_options_example %}
<nav class="pagination" role="navigation" aria-label="pagination">
  <a class="pagination-previous is-disabled" title="This is the first page">Previous</a>
  <a class="pagination-next">Next page</a>
  <ul class="pagination-list">
    <li>
      <a class="pagination-link is-current" aria-label="Page 1" aria-current="page">1</a>
    </li>
    <li>
      <a class="pagination-link" aria-label="Goto page 2">2</a>
    </li>
    <li>
      <a class="pagination-link" aria-label="Goto page 3">3</a>
    </li>
  </ul>
</nav>
{% endcapture %}

{% capture pagination_centered_example %}
<nav class="pagination is-centered" role="navigation" aria-label="pagination">
  <a class="pagination-previous">Previous</a>
  <a class="pagination-next">Next page</a>
  <ul class="pagination-list">
    <li><a class="pagination-link" aria-label="Goto page 1">1</a></li>
    <li><span class="pagination-ellipsis">&hellip;</span></li>
    <li><a class="pagination-link" aria-label="Goto page 45">45</a></li>
    <li><a class="pagination-link is-current" aria-label="Page 46" aria-current="page">46</a></li>
    <li><a class="pagination-link" aria-label="Goto page 47">47</a></li>
    <li><span class="pagination-ellipsis">&hellip;</span></li>
    <li><a class="pagination-link" aria-label="Goto page 86">86</a></li>
  </ul>
</nav>
{% endcapture %}

{% capture pagination_right_example %}
<nav class="pagination is-right" role="navigation" aria-label="pagination">
  <a class="pagination-previous">Previous</a>
  <a class="pagination-next">Next page</a>
  <ul class="pagination-list">
    <li><a class="pagination-link" aria-label="Goto page 1">1</a></li>
    <li><span class="pagination-ellipsis">&hellip;</span></li>
    <li><a class="pagination-link" aria-label="Goto page 45">45</a></li>
    <li><a class="pagination-link is-current" aria-label="Page 46" aria-current="page">46</a></li>
    <li><a class="pagination-link" aria-label="Goto page 47">47</a></li>
    <li><span class="pagination-ellipsis">&hellip;</span></li>
    <li><a class="pagination-link" aria-label="Goto page 86">86</a></li>
  </ul>
</nav>
{% endcapture %}

{% capture pagination_rounded_example %}
<nav class="pagination is-rounded" role="navigation" aria-label="pagination">
  <a class="pagination-previous">Previous</a>
  <a class="pagination-next">Next page</a>
  <ul class="pagination-list">
    <li><a class="pagination-link" aria-label="Goto page 1">1</a></li>
    <li><span class="pagination-ellipsis">&hellip;</span></li>
    <li><a class="pagination-link" aria-label="Goto page 45">45</a></li>
    <li><a class="pagination-link is-current" aria-label="Page 46" aria-current="page">46</a></li>
    <li><a class="pagination-link" aria-label="Goto page 47">47</a></li>
    <li><span class="pagination-ellipsis">&hellip;</span></li>
    <li><a class="pagination-link" aria-label="Goto page 86">86</a></li>
  </ul>
</nav>
{% endcapture %}

{% capture pagination_small_example %}
<nav class="pagination is-small" role="navigation" aria-label="pagination">
  <a class="pagination-previous">Previous</a>
  <a class="pagination-next">Next page</a>
  <ul class="pagination-list">
    <li><a class="pagination-link" aria-label="Goto page 1">1</a></li>
    <li><span class="pagination-ellipsis">&hellip;</span></li>
    <li><a class="pagination-link" aria-label="Goto page 45">45</a></li>
    <li><a class="pagination-link is-current" aria-label="Page 46" aria-current="page">46</a></li>
    <li><a class="pagination-link" aria-label="Goto page 47">47</a></li>
    <li><span class="pagination-ellipsis">&hellip;</span></li>
    <li><a class="pagination-link" aria-label="Goto page 86">86</a></li>
  </ul>
</nav>
{% endcapture %}

{% capture pagination_medium_example %}
<nav class="pagination is-medium" role="navigation" aria-label="pagination">
  <a class="pagination-previous">Previous</a>
  <a class="pagination-next">Next page</a>
  <ul class="pagination-list">
    <li><a class="pagination-link" aria-label="Goto page 1">1</a></li>
    <li><span class="pagination-ellipsis">&hellip;</span></li>
    <li><a class="pagination-link" aria-label="Goto page 45">45</a></li>
    <li><a class="pagination-link is-current" aria-label="Page 46" aria-current="page">46</a></li>
    <li><a class="pagination-link" aria-label="Goto page 47">47</a></li>
    <li><span class="pagination-ellipsis">&hellip;</span></li>
    <li><a class="pagination-link" aria-label="Goto page 86">86</a></li>
  </ul>
</nav>
{% endcapture %}

{% capture pagination_large_example %}
<nav class="pagination is-large" role="navigation" aria-label="pagination">
  <a class="pagination-previous">Previous</a>
  <a class="pagination-next">Next page</a>
  <ul class="pagination-list">
    <li><a class="pagination-link" aria-label="Goto page 1">1</a></li>
    <li><span class="pagination-ellipsis">&hellip;</span></li>
    <li><a class="pagination-link" aria-label="Goto page 45">45</a></li>
    <li><a class="pagination-link is-current" aria-label="Page 46" aria-current="page">46</a></li>
    <li><a class="pagination-link" aria-label="Goto page 47">47</a></li>
    <li><span class="pagination-ellipsis">&hellip;</span></li>
    <li><a class="pagination-link" aria-label="Goto page 86">86</a></li>
  </ul>
</nav>
{% endcapture %}

<div class="content">
  <p>
    The pagination component consists of several elements:
  </p>
  <ul>
    <li>
      <code>pagination-previous</code> and <code>pagination-next</code> for incremental navigation
    </li>
    <li>
      <code>pagination-list</code> which displays page items:
      <ul>
        <li>
          <code>pagination-link</code> for the page numbers
        </li>
        <li>
          <code>pagination-ellipsis</code> for range separators
        </li>
      </ul>
    </li>
  </ul>
  <p>
    All elements are optional so you can compose your pagination as you wish.
  </p>
</div>

{% include elements/snippet.html content=pagination_example horizontal=true more=true %}

<div class="content">
  <p>
    You can disable some links if they are not active, or change the amount of page numbers available.
  </p>
</div>

{% include elements/snippet.html content=pagination_options_example horizontal=true more=true %}

<div class="content">
  <p>
    By default on <strong>tablet</strong>, the list is located on the left, and the previous/next buttons on the right. But you can change the <strong>order</strong> of these elements by using the <code>is-centered</code> and <code>is-right</code> modifiers.
  </p>
</div>

{% include elements/snippet.html content=pagination_centered_example horizontal=true more=true %}

{% include elements/snippet.html content=pagination_right_example horizontal=true more=true %}

{% include elements/anchor.html name="Styles" %}

<p class="content">
  Add the <code>is-rounded</code> modifier to have rounded pagination items.
</p>

{% include elements/snippet.html content=pagination_rounded_example horizontal=true more=true %}

{% include elements/anchor.html name="Sizes" %}

<p class="content">
  The pagination comes in <strong>3 additional sizes</strong>.<br>
  You only need to append the <strong>modifier</strong> <code>is-small</code>, <code>is-medium</code>, or <code>is-large</code> to the <code>pagination</code> component.
</p>

{% include elements/snippet.html content=pagination_small_example horizontal=true more=true %}

{% include elements/snippet.html content=pagination_medium_example horizontal=true more=true %}

{% include elements/snippet.html content=pagination_large_example horizontal=true more=true %}

{% include components/variables.html type='component' %}
