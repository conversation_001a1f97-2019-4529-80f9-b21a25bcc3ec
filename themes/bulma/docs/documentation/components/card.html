---
title: Card
layout: documentation
doc-tab: components
doc-subtab: card
breadcrumb:
- home
- documentation
- components
- components-card
meta:
  variables: true
  colors: false
  sizes: false
---

{% capture card_example %}
<div class="card">
  <div class="card-image">
    <figure class="image is-4by3">
      <img src="{{site.url}}/images/placeholders/1280x960.png" alt="Placeholder image">
    </figure>
  </div>
  <div class="card-content">
    <div class="media">
      <div class="media-left">
        <figure class="image is-48x48">
          <img src="{{site.url}}/images/placeholders/96x96.png" alt="Placeholder image">
        </figure>
      </div>
      <div class="media-content">
        <p class="title is-4"><PERSON></p>
        <p class="subtitle is-6">@johnsmith</p>
      </div>
    </div>

    <div class="content">
      Lorem ipsum dolor sit amet, consectetur adipiscing elit.
      Phasellus nec iaculis mauris. <a>@bulmaio</a>.
      <a href="#">#css</a> <a href="#">#responsive</a>
      <br>
      <time datetime="2016-1-1">11:09 PM - 1 Jan 2016</time>
    </div>
  </div>
</div>
{% endcapture %}

{% capture card_header_example %}
<div class="card">
  <header class="card-header">
    <p class="card-header-title">
      Component
    </p>
    <button class="card-header-icon" aria-label="more options">
      <span class="icon">
        <i class="fas fa-angle-down" aria-hidden="true"></i>
      </span>
    </button>
  </header>
  <div class="card-content">
    <div class="content">
      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus nec iaculis mauris.
      <a href="#">@bulmaio</a>. <a href="#">#css</a> <a href="#">#responsive</a>
      <br>
      <time datetime="2016-1-1">11:09 PM - 1 Jan 2016</time>
    </div>
  </div>
  <footer class="card-footer">
    <a href="#" class="card-footer-item">Save</a>
    <a href="#" class="card-footer-item">Edit</a>
    <a href="#" class="card-footer-item">Delete</a>
  </footer>
</div>
{% endcapture %}

{% capture card_title_example %}
<div class="card">
  <div class="card-content">
    <p class="title">
      “There are two hard things in computer science: cache invalidation, naming things, and off-by-one errors.”
    </p>
    <p class="subtitle">
      Jeff Atwood
    </p>
  </div>
  <footer class="card-footer">
    <p class="card-footer-item">
      <span>
        View on <a href="https://twitter.com/codinghorror/status/506010907021828096">Twitter</a>
      </span>
    </p>
    <p class="card-footer-item">
      <span>
        Share on <a href="#">Facebook</a>
      </span>
    </p>
  </footer>
</div>
{% endcapture %}

<div class="content">
  <p>The <strong>card</strong> component comprises several elements that you can mix and match:</p>
  <ul>
    <li>
      <code>card</code>: the main container
      <ul>
        <li>
          <code>card-header</code>: a horizontal bar with a shadow
          <ul>
            <li>
              <code>card-header-title</code>: a left-aligned bold text
            </li>
            <li>
              <code>card-header-icon</code>: a placeholder for an icon
            </li>
          </ul>
        </li>
        <li>
          <code>card-image</code>: a fullwidth container for a responsive image
        </li>
        <li>
          <code>card-content</code>: a multi-purpose container for <em>any</em> other element
        </li>
        <li>
          <code>card-footer</code>: a horizontal list of controls
          <ul>
            <li>
              <code>card-footer-item</code>: a repeatable list item
            </li>
          </ul>
        </li>
      </ul>
    </li>
  </ul>
</div>

<div class="content">
  <p>
    You can center the <code>card-header-title</code> by appending the <code>is-centered</code> modifier.
  </p>
</div>

{% include elements/snippet.html content=card_example size="1-2" %}

<!--  -->
{% include elements/anchor.html name="Card parts" %}

<div class="content">
  <p>The <code>card-header</code> can have a <strong>title</strong> and a Bulma <code>icon</code>:</p>
</div>

{% capture card_image %}
<div class="card">
  <header class="card-header">
    <p class="card-header-title">
      Card header
    </p>
    <button class="card-header-icon" aria-label="more options">
      <span class="icon">
        <i class="fas fa-angle-down" aria-hidden="true"></i>
      </span>
    </button>
  </header>
</div>
{% endcapture %}

{% include elements/snippet.html content=card_image size="1-2" %}

<div class="content">
  <p>The <code>card-image</code> is a container for a Bulma <code>image</code> element:</p>
</div>

{% capture card_image %}
<div class="card">
  <div class="card-image">
    <figure class="image is-4by3">
      <img src="{{site.url}}/images/placeholders/1280x960.png" alt="Placeholder image">
    </figure>
  </div>
</div>
{% endcapture %}

{% include elements/snippet.html content=card_image size="1-2" %}

<div class="content">
  <p>The <code>card-content</code> is the main part, ideal for <strong>text content</strong>, thanks to its padding:</p>
</div>

{% capture card_image %}
<div class="card">
  <div class="card-content">
    <div class="content">
      Lorem ipsum leo risus, porta ac consectetur ac, vestibulum at eros. Donec id elit non mi porta gravida at eget metus. Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Cras mattis consectetur purus sit amet fermentum.
    </div>
  </div>
</div>
{% endcapture %}

{% include elements/snippet.html content=card_image size="1-2" %}

<div class="content">
  <p>The <code>card-footer</code> acts as a list of for several <code>card-footer-item</code> elements:</p>
</div>

{% capture card_footer %}
<div class="card">
  <footer class="card-footer">
    <a href="#" class="card-footer-item">Save</a>
    <a href="#" class="card-footer-item">Edit</a>
    <a href="#" class="card-footer-item">Delete</a>
  </footer>
</div>
{% endcapture %}

{% include elements/snippet.html content=card_footer size="1-2" %}

{% include elements/anchor.html name="Examples" %}

{% include elements/snippet.html content=card_header_example size="1-2" %}
{% include elements/snippet.html content=card_title_example size="1-2" %}

{% include components/variables.html type='component' %}
