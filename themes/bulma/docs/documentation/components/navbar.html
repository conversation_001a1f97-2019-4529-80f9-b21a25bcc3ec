---
title: Navbar
layout: documentation
doc-tab: components
doc-subtab: navbar
breadcrumb:
- home
- documentation
- components
- components-navbar
meta:
  colors: true
  sizes: false
  variables: true
---

{% capture navbar_basic_example %}
<nav class="navbar" role="navigation" aria-label="main navigation">
  <div class="navbar-brand">
    <a class="navbar-item" href="{{ site.url }}">
      <img src="{{ site.url }}/images/bulma-logo.png" width="112" height="28">
    </a>

    <a role="button" class="navbar-burger" aria-label="menu" aria-expanded="false" data-target="navbarBasicExample">
      <span aria-hidden="true"></span>
      <span aria-hidden="true"></span>
      <span aria-hidden="true"></span>
    </a>
  </div>

  <div id="navbarBasicExample" class="navbar-menu">
    <div class="navbar-start">
      <a class="navbar-item">
        Home
      </a>

      <a class="navbar-item">
        Documentation
      </a>

      <div class="navbar-item has-dropdown is-hoverable">
        <a class="navbar-link">
          More
        </a>

        <div class="navbar-dropdown">
          <a class="navbar-item">
            About
          </a>
          <a class="navbar-item">
            Jobs
          </a>
          <a class="navbar-item">
            Contact
          </a>
          <hr class="navbar-divider">
          <a class="navbar-item">
            Report an issue
          </a>
        </div>
      </div>
    </div>

    <div class="navbar-end">
      <div class="navbar-item">
        <div class="buttons">
          <a class="button is-primary">
            <strong>Sign up</strong>
          </a>
          <a class="button is-light">
            Log in
          </a>
        </div>
      </div>
    </div>
  </div>
</nav>
{% endcapture %}

{% capture navbar_example %}
{% include examples/navbar.html id="Default" %}
{% endcapture %}

{% capture navbar_brand_example %}
<nav class="navbar" role="navigation" aria-label="main navigation">
  <div class="navbar-brand">
    <!-- navbar items, navbar burger... -->
  </div>
</nav>
{% endcapture %}

{% capture navbar_burger_example %}
<a role="button" class="navbar-burger" aria-label="menu" aria-expanded="false">
  <span aria-hidden="true"></span>
  <span aria-hidden="true"></span>
  <span aria-hidden="true"></span>
</a>
{% endcapture %}

{% capture navbar_brand_items_example %}
<nav class="navbar" role="navigation" aria-label="main navigation">
  <div class="navbar-brand">
    <a class="navbar-item" href="{{ site.url }}">
      <img src="{{ site.url }}/images/bulma-logo.png" alt="{{ site.data.meta.title }}" width="112" height="28">
    </a>

    <a role="button" class="navbar-burger" aria-label="menu" aria-expanded="false">
      <span aria-hidden="true"></span>
      <span aria-hidden="true"></span>
      <span aria-hidden="true"></span>
    </a>
  </div>
</nav>
{% endcapture %}

{% capture navbar_menu_example %}
<nav class="navbar" role="navigation" aria-label="main navigation">
  <div class="navbar-brand">
    <!-- navbar items, navbar burger... -->
  </div>
  <div class="navbar-menu">
    <!-- navbar start, navbar end -->
  </div>
</nav>
{% endcapture %}

{% capture navbar_menu_active_example %}
<div class="navbar-menu">
  <!-- hidden on mobile -->
</div>

<div class="navbar-menu is-active">
  <!-- shown on mobile -->
</div>
{% endcapture %}

{% capture navbar_start_end_example %}
<div class="navbar-menu">
  <div class="navbar-start">
    <!-- navbar items -->
  </div>

  <div class="navbar-end">
    <!-- navbar items -->
  </div>
</div>
{% endcapture %}

{% capture navbar_item_link_example %}
<a class="navbar-item">
  Home
</a>
{% endcapture %}

{% capture navbar_item_brand_example %}
<a class="navbar-item">
  <img src="{{ site.url }}/images/bulma-logo.png" width="112" height="28" alt="Bulma">
</a>
{% endcapture %}

{% capture navbar_item_dropdown_example %}
<div class="navbar-item has-dropdown">
  <a class="navbar-link">
    Docs
  </a>

  <div class="navbar-dropdown">
    <!-- Other navbar items -->
  </div>
</div>
{% endcapture %}

{% capture navbar_item_dropdown_bis_example %}
<div class="navbar-dropdown">
  <a class="navbar-item">
    Overview
  </a>
</div>
{% endcapture %}

{% capture navbar_item_other_example %}
<div class="navbar-item">
  <div class="field is-grouped">
    <p class="control">
      <a class="button">
        <span class="icon">
          <i class="fas fa-twitter" aria-hidden="true"></i>
        </span>
        <span>Tweet</span>
      </a>
    </p>
    <p class="control">
      <a class="button is-primary">
        <span class="icon">
          <i class="fas fa-download" aria-hidden="true"></i>
        </span>
        <span>Download</span>
      </a>
    </p>
  </div>
</div>
{% endcapture %}

{% capture navbar_transparent_example %}
{% include examples/navbar.html transparent=true boxed=true id="TransparentExample" %}
{% endcapture %}

{% capture navbar_dropdown_example %}
<nav class="navbar" role="navigation" aria-label="dropdown navigation">
  <div class="navbar-item has-dropdown">
    <a class="navbar-link">
      Docs
    </a>

    <div class="navbar-dropdown">
      <a class="navbar-item">
        Overview
      </a>
      <a class="navbar-item">
        Elements
      </a>
      <a class="navbar-item">
        Components
      </a>
      <hr class="navbar-divider">
      <div class="navbar-item">
        Version {{ site.data.meta.version }}
      </div>
    </div>
  </div>
</nav>
{% endcapture %}

{% capture navbar_dropdown_hover_snippet %}
<div class="navbar-item has-dropdown is-hoverable">
  <!-- navbar-link, navbar-dropdown etc. -->
</div>
{% endcapture %}

{% capture navbar_dropdown_hover_example %}
<nav class="navbar" role="navigation" aria-label="dropdown navigation">
  <div class="navbar-item has-dropdown is-hoverable">
    <a class="navbar-link">
      Docs
    </a>

    <div class="navbar-dropdown">
      <a class="navbar-item">
        Overview
      </a>
      <a class="navbar-item">
        Elements
      </a>
      <a class="navbar-item">
        Components
      </a>
      <hr class="navbar-divider">
      <div class="navbar-item">
        Version {{ site.data.meta.version }}
      </div>
    </div>
  </div>
</nav>
{% endcapture %}

{% capture navbar_dropdown_active_snippet %}
<div class="navbar-item has-dropdown is-active">
  <!-- navbar-link, navbar-dropdown etc. -->
</div>
{% endcapture %}

{% capture navbar_dropdown_active_example %}
<nav class="navbar" role="navigation" aria-label="dropdown navigation">
  <div class="navbar-item has-dropdown is-active">
    <a class="navbar-link">
      Docs
    </a>

    <div class="navbar-dropdown">
      <a class="navbar-item">
        Overview
      </a>
      <a class="navbar-item">
        Elements
      </a>
      <a class="navbar-item">
        Components
      </a>
      <hr class="navbar-divider">
      <div class="navbar-item">
        Version {{ site.data.meta.version }}
      </div>
    </div>
  </div>
</nav>
{% endcapture %}

{% capture navbar_dropdown_right_snippet %}
<div class="navbar-dropdown is-right">
  <!-- navbar-item, navbar-divider etc. -->
</div>
{% endcapture %}

{% capture navbar_dropdown_right_example %}
<nav class="navbar" role="navigation" aria-label="dropdown navigation">
  <div class="navbar-menu">
    <div class="navbar-start">
      <div class="navbar-item has-dropdown is-active">
        <a class="navbar-link">
          Left
        </a>

        <div class="navbar-dropdown">
          <a class="navbar-item">
            Overview
          </a>
          <a class="navbar-item">
            Elements
          </a>
          <a class="navbar-item">
            Components
          </a>
          <hr class="navbar-divider">
          <div class="navbar-item">
            Version {{ site.data.meta.version }}
          </div>
        </div>
      </div>
    </div>

    <div class="navbar-end">
      <div class="navbar-item has-dropdown is-active">
        <a class="navbar-link">
          Right
        </a>

        <div class="navbar-dropdown is-right">
          <a class="navbar-item">
            Overview
          </a>
          <a class="navbar-item">
            Elements
          </a>
          <a class="navbar-item">
            Components
          </a>
          <hr class="navbar-divider">
          <div class="navbar-item">
            Version {{ site.data.meta.version }}
          </div>
        </div>
      </div>
    </div>
  </div>
</nav>

<section class="hero is-primary">
  <div class="hero-body">
    <p class="title">
      Documentation
    </p>
    <p class="subtitle">
      Everything you need to <strong>create a website</strong> with Bulma
    </p>
  </div>
</section>
{% endcapture %}

{% capture navbar_dropup_snippet %}
<div class="navbar-item has-dropdown has-dropdown-up is-hoverable">
  <a class="navbar-link" href="{{ site.url }}/documentation/overview/start/">
    Docs
  </a>
  <div class="navbar-dropdown">
    <a class="navbar-item" href="{{ site.url }}/documentation/overview/start/">
      Overview
    </a>
  </div>
</div>
{% endcapture %}

{% capture navbar_dropup_example %}
<section class="hero is-primary">
  <div class="hero-body">
    <p class="title">
      Documentation
    </p>
    <p class="subtitle">
      Everything you need to <strong>create a website</strong> with Bulma
    </p>
  </div>
</section>

<nav class="navbar" role="navigation" aria-label="dropdown navigation">
  <div class="navbar-menu">
    <div class="navbar-start">
      <div class="navbar-item has-dropdown has-dropdown-up is-active">
        <a class="navbar-link">
          Dropup
        </a>

        <div class="navbar-dropdown">
          <a class="navbar-item">
            Overview
          </a>
          <a class="navbar-item">
            Elements
          </a>
          <a class="navbar-item">
            Components
          </a>
          <hr class="navbar-divider">
          <div class="navbar-item">
            Version {{ site.data.meta.version }}
          </div>
        </div>
      </div>
    </div>
  </div>
</nav>
{% endcapture %}

{% capture navbar_dropdown_default_example %}
<nav class="navbar" role="navigation" aria-label="dropdown navigation">
  <a class="navbar-item">
    <img src="{{ site.url }}/images/bulma-logo.png" alt="{{ site.data.meta.title }}" width="112" height="28">
  </a>

  <div class="navbar-item has-dropdown is-active">
    <a class="navbar-link">
      Docs
    </a>

    <div class="navbar-dropdown">
      <a class="navbar-item">
        Overview
      </a>
      <a class="navbar-item">
        Elements
      </a>
      <a class="navbar-item">
        Components
      </a>
      <hr class="navbar-divider">
      <div class="navbar-item">
        Version {{ site.data.meta.version }}
      </div>
    </div>
  </div>
</nav>

<section class="hero is-primary">
  <div class="hero-body">
    <p class="title">
      Documentation
    </p>
    <p class="subtitle">
      Everything you need to <strong>create a website</strong> with Bulma
    </p>
  </div>
</section>
{% endcapture %}

{% capture navbar_dropdown_boxed_example %}
<nav class="navbar is-transparent" role="navigation" aria-label="dropdown navigation">
  <a class="navbar-item">
    <img src="{{ site.url }}/images/bulma-logo.png" alt="{{ site.data.meta.title }}" width="112" height="28">
  </a>

  <div class="navbar-item has-dropdown is-active">
    <a class="navbar-link">
      Docs
    </a>

    <div class="navbar-dropdown is-boxed">
      <a class="navbar-item">
        Overview
      </a>
      <a class="navbar-item">
        Elements
      </a>
      <a class="navbar-item">
        Components
      </a>
      <hr class="navbar-divider">
      <div class="navbar-item">
        Version {{ site.data.meta.version }}
      </div>
    </div>
  </div>
</nav>

<section class="hero">
  <div class="hero-body">
    <p class="title">
      Documentation
    </p>
    <p class="subtitle">
      Everything you need to <strong>create a website</strong> with Bulma
    </p>
  </div>
</section>
{% endcapture %}

{% capture navbar_dropdown_item_active_example %}
<nav class="navbar" role="navigation" aria-label="dropdown navigation">
  <a class="navbar-item">
    <img src="{{ site.url }}/images/bulma-logo.png" alt="{{ site.data.meta.title }}" width="112" height="28">
  </a>

  <div class="navbar-item has-dropdown is-active">
    <a class="navbar-link">
      Docs
    </a>

    <div class="navbar-dropdown">
      <a class="navbar-item">
        Overview
      </a>
      <a class="navbar-item is-active">
        Elements
      </a>
      <a class="navbar-item">
        Components
      </a>
      <hr class="navbar-divider">
      <div class="navbar-item">
        Version {{ site.data.meta.version }}
      </div>
    </div>
  </div>
</nav>

<section class="hero is-primary">
  <div class="hero-body">
    <p class="title">
      Documentation
    </p>
    <p class="subtitle">
      Everything you need to <strong>create a website</strong> with Bulma
    </p>
  </div>
</section>
{% endcapture %}

{% capture navbar_divider_example %}
<hr class="navbar-divider">
{% endcapture %}

{% capture navbar_js_html %}
<a role="button" class="navbar-burger" data-target="navMenu" aria-label="menu" aria-expanded="false">
  <span aria-hidden="true"></span>
  <span aria-hidden="true"></span>
  <span aria-hidden="true"></span>
</a>

<div class="navbar-menu" id="navMenu">
  <!-- navbar-start, navbar-end... -->
</div>
{% endcapture %}

{% capture navbar_js_code %}
document.addEventListener('DOMContentLoaded', () => {

  // Get all "navbar-burger" elements
  const $navbarBurgers = Array.prototype.slice.call(document.querySelectorAll('.navbar-burger'), 0);

  // Add a click event on each of them
  $navbarBurgers.forEach( el => {
    el.addEventListener('click', () => {

      // Get the target from the "data-target" attribute
      const target = el.dataset.target;
      const $target = document.getElementById(target);

      // Toggle the "is-active" class on both the "navbar-burger" and the "navbar-menu"
      el.classList.toggle('is-active');
      $target.classList.toggle('is-active');

    });
  });

});
{% endcapture %}

{% capture navbar_jquery_code %}
$(document).ready(function() {

  // Check for click events on the navbar burger icon
  $(".navbar-burger").click(function() {

      // Toggle the "is-active" class on both the "navbar-burger" and the "navbar-menu"
      $(".navbar-burger").toggleClass("is-active");
      $(".navbar-menu").toggleClass("is-active");

  });
});
{% endcapture %}

{% capture navbar_color_markup %}
<nav class="navbar is-primary">
  <!-- navbar brand, navbar menu... -->
</nav>
{% endcapture %}

<div class="content">
  <p>
    The <code>navbar</code> component is a responsive and versatile horizontal navigation bar with the following structure:
  </p>
  <ul>
    <li>
      <code>navbar</code> the <strong>main</strong> container
      <ul>
        <li>
          <code>navbar-brand</code> the <strong>left side</strong>, <strong class="has-text-success">always visible</strong>, which usually contains the <strong>logo</strong> and optionally some links or icons
          <ul>
            <li>
              <code>navbar-burger</code> the <strong>hamburger</strong> icon, which toggles the navbar menu on touch devices
            </li>
          </ul>
        </li>
        <li>
          <code>navbar-menu</code> the <strong>right side</strong>, hidden on touch devices, visible on desktop
          <ul>
            <li>
              <code>navbar-start</code> the <strong>left part</strong> of the menu, which appears next to the navbar brand on desktop
            </li>
            <li>
              <code>navbar-end</code> the <strong>right part</strong> of the menu, which appears at the end of the navbar
              <ul>
                <li>
                  <code>navbar-item</code> each <strong>single item</strong> of the navbar, which can either be an <code>a</code> or a <code>div</code>
                  <ul>
                    <li>
                      <code>navbar-link</code> a <strong>link</strong> as the sibling of a dropdown, with an arrow
                    </li>
                    <li>
                      <code>navbar-dropdown</code> the <strong>dropdown menu</strong>, which can include navbar items and dividers
                      <ul>
                        <li>
                          <code>navbar-divider</code> a <strong>horizontal line</strong> to separate navbar items
                        </li>
                      </ul>
                    </li>
                  </ul>
                </li>
              </ul>
            </li>
          </ul>
        </li>
      </ul>
    </li>
  </ul>
</div>

{% include elements/anchor.html name="Basic Navbar" %}

<div class="content">
  <p>
    To <strong>get started quickly</strong>, here is what a complete basic navbar looks like:
  </p>
</div>

<div class="bd-example is-paddingless">
  {{ navbar_basic_example }}
</div>

{% highlight html %}{{ navbar_basic_example }}{% endhighlight %}

{% include elements/anchor.html name="Navbar brand" %}

<div class="content">
  <p>
    The <code>navbar-brand</code> is the left side of the navbar. It can contain:
  </p>
  <ul>
    <li>
      a number of <code>navbar-item</code>
    </li>
    <li>
      the <code>navbar-burger</code> as last child
    </li>
  </ul>
</div>

{% highlight html %}{{navbar_brand_example}}{% endhighlight %}

<div class="content">
  <p>
    The navbar brand is <strong>always visible</strong>: on both touch devices {% include bp/touch.html %} and desktop {% include bp/desktop.html %}. As a result, it is recommended to only use a few navbar items to avoid <strong>overflowing</strong> horizontally on small devices.
  </p>
</div>

<div class="bd-example is-paddingless">
  {{navbar_brand_items_example}}
</div>

{% highlight html %}{{navbar_brand_items_example}}{% endhighlight %}

<div class="content">
  <p>
    On desktop {% include bp/desktop.html %}, the navbar brand will only take up the space it needs.
  </p>
</div>

{% include elements/anchor.html name="Navbar burger" %}

<div class="content">
  <p>
    The <code>navbar-burger</code> is a hamburger menu that only appears on <strong>touch devices</strong>. It has to appear as the last child of <code>navbar-brand</code>. It has to contain three empty <code>span</code> tags in order to visualize the hamburger lines or the cross (when active).
  </p>
</div>

<div class="example is-paddingless">
  <a role="button" class="navbar-burger" aria-label="menu" aria-expanded="false" style="display: flex;">
    <span aria-hidden="true"></span>
    <span aria-hidden="true"></span>
    <span aria-hidden="true"></span>
  </a>
</div>

{% highlight html %}{{ navbar_burger_example }}{% endhighlight %}

<div class="content">
  <p>
    You can add the modifier class <code>is-active</code> to turn it into a cross.
  </p>
</div>

<div class="example is-paddingless">
  <a role="button" class="navbar-burger is-active" aria-label="menu" aria-expanded="false" style="display: flex;">
    <span aria-hidden="true"></span>
    <span aria-hidden="true"></span>
    <span aria-hidden="true"></span>
  </a>
</div>

{% include elements/anchor.html name="Navbar menu" %}

<div class="content">
  <p>
    The <code>navbar-menu</code> is the <strong>counterpart</strong> of the navbar brand. As such, it must appear as a direct child of <code>navbar</code>, as a sibling of <code>navbar-brand</code>.
  </p>
</div>

{% highlight html %}{{navbar_menu_example}}{% endhighlight %}

<div class="content">
  <p>
    The <code>navbar-menu</code> is <strong>hidden on touch devices</strong> {% include bp/touch.html %}. You need to add the modifier class <code>is-active</code> to display it.
  </p>
</div>

{% highlight html %}{{navbar_menu_active_example}}{% endhighlight %}

<div class="content">
  <p>
    On desktop {% include bp/desktop.html %}, the <code>navbar-menu</code> will <strong>fill up the space</strong> available in the navbar, leaving the navbar brand just the space it needs. It needs, however, two elements as direct children:
  </p>
  <ul>
    <li>
      <code>navbar-start</code>
    </li>
    <li>
      <code>navbar-end</code>
    </li>
  </ul>
</div>

<hr>

<div id="navbarJsExample" class="message is-info">
  <h4 class="message-header">Javascript toggle</h4>
  <div class="message-body">
    <div class="content">
      <p>
        The Bulma package <strong>does not come with any JavaScript</strong>.
        <br>
        Here is however an implementation example, which toggles the class <code>is-active</code> on both the <code>navbar-burger</code> and the targeted <code>navbar-menu</code>, in Vanilla Javascript.
      </p>

      {% highlight html %}{{ navbar_js_html }}{% endhighlight %}

      {% highlight javascript %}{{ navbar_js_code }}{% endhighlight %}

      <p>
        And here is another implementation example, which again toggles the class <code>is-active</code> on both the <code>navbar-burger</code> and the targeted <code>navbar-menu</code>, but this time in jQuery.
      </p>

      {% highlight javascript %}{{ navbar_jquery_code }}{% endhighlight %}

      <p>
        Remember, these are just implementation examples. The Bulma package <strong>does not come with any JavaScript</strong>.
      </p>
    </div>
  </div>
</div>

{% include elements/anchor.html name="Navbar start and navbar end" %}

<div class="content">
  <p>
    The <code>navbar-start</code> and <code>navbar-end</code> are the two direct and only children of the <code>navbar-menu</code>.
  </p>
  <p>
    On desktop {% include bp/desktop.html %}:
  </p>
  <ul>
    <li>
      <code>navbar-start</code> will appear on the <strong>left</strong>
    </li>
    <li>
      <code>navbar-end</code> will appear on the <strong>right</strong>
    </li>
  </ul>
  <p>
    Each of them can contain any number of <code>navbar-item</code>.
  </p>
</div>

{% highlight html %}{{navbar_start_end_example}}{% endhighlight %}

{% include elements/anchor.html name="Navbar item" %}

<div class="content">
  <p>
    A <code>navbar-item</code> is a repeatable element that can be:
  </p>
  <ul class="bd-spaced">
    <li>
      a navigation <strong>link</strong>
      {% highlight html %}{{ navbar_item_link_example }}{% endhighlight %}
    </li>
    <li>
      a container for the <strong>brand logo</strong>
      {% highlight html %}{{ navbar_item_brand_example }}{% endhighlight %}
    </li>
    <li>
      the <strong>parent</strong> of a dropdown menu
      {% highlight html %}{{ navbar_item_dropdown_example }}{% endhighlight %}
    </li>
    <li>
      a child of a <strong>navbar dropdown</strong>
      {% highlight html %}{{ navbar_item_dropdown_bis_example }}{% endhighlight %}
    </li>
    <li>
      a container for almost <strong>anything</strong> you want, like a <code>field</code>
      <div class="bd-highlight-full">
        {% highlight html %}{{ navbar_item_other_example }}{% endhighlight %}
      </div>
    </li>
  </ul>
  <p>
    It can either be an anchor tag <code>&lt;a&gt;</code> or a <code>&lt;div&gt;</code>, as a <strong>direct child</strong> of either:
  </p>
  <ul>
    <li>
      <code>navbar</code>
    </li>
    <li>
      <code>navbar-brand</code>
    </li>
    <li>
      <code>navbar-start</code>
    </li>
    <li>
      <code>navbar-end</code>
    </li>
    <li>
      <code>navbar-dropdown</code>
    </li>
  </ul>
  <p>
    You can add the following <strong>modifier</strong> classes:
  </p>
  <ul>
    <li>
      <code>is-expanded</code> to turn it into a full-width element
    </li>
    <li>
      <code>is-tab</code> to add a bottom border on hover and show the bottom border using <code>is-active</code>
    </li>
  </ul>
</div>

{% include elements/anchor.html name="Transparent navbar" %}

<div class="content">
  <p>
    To seamlessly integrate the navbar in any visual context, you can add the <code>is-transparent</code> modifier on the <code>navbar</code> component. This will remove any hover or active background from the navbar items.
  </p>
</div>

{% include elements/snippet.html content=navbar_transparent_example paddingless=true horizontal=true more=true %}

{% include elements/anchor.html name="Fixed navbar" %}

<div class="content">
  <p>
    You can now <strong>fix</strong> the navbar to either the <strong>top</strong> or <strong>bottom</strong> of the page. This is a 2-step process:
  </p>
  <ul class="bd-spaced">
    <li>
      Add either <code>is-fixed-top</code> or <code>is-fixed-bottom</code> to the <code>navbar</code> component
      {% highlight html %}<nav class="navbar is-fixed-top">{% endhighlight %}
    </li>
    <li>
      Add the corresponding <code>has-navbar-fixed-top</code> or <code>has-navbar-fixed-bottom</code> modifier to either <code>&lt;html&gt;</code> or <code>&lt;body&gt;</code> element to provide the appropriate padding to the page
      {% highlight html %}<html class="has-navbar-fixed-top">{% endhighlight %}
    </li>
  </ul>
</div>

<h4 class="title is-5">Try it out!</h4>

<div id="navbarToggles" class="buttons">
  <a id="navbarFixBottom" class="button is-link">
    <span><span id="navbarFixBottomText">Show</span> <strong>bottom</strong> navbar</span>
  </a>
</div>

{% include elements/anchor.html name="Dropdown menu" %}

<div class="content">
  <p>
    To create a <strong>dropdown menu</strong>, you will need <strong>4</strong> elements:
  </p>
  <ul>
    <li>
      <code>navbar-item</code> with the <code>has-dropdown</code> modifier
    </li>
    <li>
      <code>navbar-link</code> which contains the dropdown arrow
    </li>
    <li>
      <code>navbar-dropdown</code> which can contain instances of <code>navbar-item</code> and <code>navbar-divider</code>
    </li>
  </ul>
</div>

<div class="columns">
  <div class="column">
    <div class="bd-example is-paddingless">
      {{ navbar_dropdown_example }}
    </div>
  </div>

  <div class="column">
    {% highlight html %}{{ navbar_dropdown_example }}{% endhighlight %}
  </div>
</div>

<h4 class="title is-4">
  Show/hide the dropdown with either <strong>CSS</strong> or <strong>JavaScript</strong>
</h4>

<div class="content">
  <p>
    The <code>navbar-dropdown</code> is visible on touch devices {% include bp/touch.html %} but hidden on desktop {% include bp/desktop.html %}. <em>How</em> the dropdown is displayed on desktop depends on the parent's class.
  </p>
  <p>
    The <code>navbar-item</code> with the <code>has-dropdown</code> modifier, has <strong>2 additional modifiers</strong>
  </p>
  <ul>
    <li>
      <code>is-hoverable</code>: the dropdown will show up when <strong>hovering</strong> the parent <code>navbar-item</code>
    </li>
    <li>
      <code>is-active</code>: the dropdown will show up <strong>all the time</strong>
    </li>
  </ul>
</div>

<div class="message is-success">
  <p class="message-body">
    While the CSS <code>:hover</code> implementation works perfectly, the <code>is-active</code> class is available for users who want to control the display of the dropdown with <strong>JavaScript</strong>.
  </p>
</div>

{% highlight html %}{{ navbar_dropdown_hover_snippet }}{% endhighlight %}

<div class="columns">
  <div class="column">
    <div class="bd-example is-paddingless">
      {{ navbar_dropdown_hover_example }}
    </div>
  </div>

  <div class="column">
    {% highlight html %}{{ navbar_dropdown_hover_example }}{% endhighlight %}
  </div>
</div>

{% highlight html %}{{ navbar_dropdown_active_snippet }}{% endhighlight %}

<div class="columns">
  <div class="column">
    <div class="bd-example is-paddingless">
      {{ navbar_dropdown_active_example }}
    </div>
  </div>

  <div class="column">
    {% highlight html %}{{ navbar_dropdown_active_example }}{% endhighlight %}
  </div>
</div>

<h4 class="title is-4">
  Right dropdown
</h4>

<div class="content">
  <p>
    If your parent <code>navbar-item</code> is on the right side, you can position the dropdown to start from the <strong>right</strong> with the <code>is-right</code> modifier.
  </p>
</div>

{% highlight html %}{{ navbar_dropdown_right_snippet }}{% endhighlight %}

<div class="columns">
  <div class="column">
    <div class="bd-example is-paddingless">
      {{ navbar_dropdown_right_example }}
    </div>
  </div>

  <div class="column">
    {% highlight html %}{{ navbar_dropdown_right_example }}{% endhighlight %}
  </div>
</div>

<h4 class="title is-4">
  Dropup
</h4>

<div class="content">
  <p>
    If you're using a navbar at the bottom, like the <a href="#fixed-navbar">fixed bottom navbar</a>, you might want to use a <strong>dropup menu</strong>. Simply add the <code>has-dropdown</code> and <code>has-dropdown-up</code> modifiers to the parent <code>navbar-item</code>.
  </p>
</div>

{% highlight html %}{{ navbar_dropup_snippet }}{% endhighlight %}

<div class="columns">
  <div class="column">
    <div class="bd-example is-paddingless">
      {{ navbar_dropup_example }}
    </div>
  </div>

  <div class="column">
    {% highlight html %}{{ navbar_dropup_example }}{% endhighlight %}
  </div>
</div>


{% assign vernum = site.data.meta.version | downcase | remove: "." | plus: 0 %}

{% if vernum >= 72 %}

<h4 class="title is-4">
  Dropdown without arrow
</h4>

<div class="content">
  <p>
    You can remove the arrow in the items of the navbar by adding the <code>is-arrowless</code> modifier to them.
  </p>
</div>

{% highlight html %}
<div class="navbar-item has-dropdown is-hoverable">
  <a class="navbar-link is-arrowless">
    Docs
  </a>
  <!-- navbar-dropdowns -->
</div>
{% endhighlight %}

{% capture navbar_dropup_without_arrow_example %}
<div class="navbar-item has-dropdown is-hoverable">
  <a class="navbar-link is-arrowless">
    Link without arrow
  </a>
  <div class="navbar-dropdown">
    <a class="navbar-item">
      Overview
    </a>
    <a class="navbar-item">
      Elements
    </a>
    <a class="navbar-item">
      Components
    </a>
    <hr class="navbar-divider">
    <div class="navbar-item">
      Version {{ site.data.meta.version }}
    </div>
  </div>
</div>
{% endcapture %}

<div class="columns">
  <div class="column">
    <div class="bd-example is-paddingless">
      {{ navbar_dropup_without_arrow_example }}
    </div>
  </div>

  <div class="column">
    {% highlight html %}{{ navbar_dropup_without_arrow_example }}{% endhighlight %}
  </div>
</div>

{% endif %}


<h4 class="title is-4">
  Styles for the dropdown menu
</h4>

<div class="content">
  <p>
    By default, the <code>navbar-dropdown</code> has:
  </p>
  <ul>
    <li>
      a grey <code>border-top</code>
    </li>
    <li>
      a <code>border-radius</code> at both bottom corners
    </li>
  </ul>
</div>

<div class="columns">
  <div class="column">
    <div class="bd-example is-paddingless">
      {{ navbar_dropdown_default_example }}
    </div>
  </div>

  <div class="column">
    {% highlight html %}{{ navbar_dropdown_default_example }}{% endhighlight %}
  </div>
</div>

<div class="content">
  <p>
    When having a <a href="#transparent-navbar">transparent navbar</a>, it is preferable to use the boxed version of the dropdown, by using the <code>is-boxed</code> modifier.
  </p>
  <ul>
    <li>
      the grey border is <strong>removed</strong>
    </li>
    <li>
      a slight <strong>inner shadow</strong> is added
    </li>
    <li>
      all corners are <strong>rounded</strong>
    </li>
    <li>
      the hover/active state is <strong>animated</strong>
    </li>
  </ul>
</div>

<div class="columns">
  <div class="column">
    <div class="bd-example is-paddingless">
      {{ navbar_dropdown_boxed_example }}
    </div>
  </div>

  <div class="column">
    {% highlight html %}{{ navbar_dropdown_boxed_example }}{% endhighlight %}
  </div>
</div>

<h4 class="title is-4">
  Active dropdown navbar item
</h4>

<div class="columns">
  <div class="column">
    <div class="bd-example is-paddingless">
      {{ navbar_dropdown_item_active_example }}
    </div>
  </div>

  <div class="column">
    {% highlight html %}{{ navbar_dropdown_item_active_example }}{% endhighlight %}
  </div>
</div>

<h4 class="title is-4">
  Dropdown divider
</h4>

<div class="content">
  <p>
    You can add a <code>navbar-divider</code> to display a <strong>horizontal rule</strong> in a <code>navbar-dropdown</code>.
  </p>
</div>

{% highlight html %}{{ navbar_divider_example }}{% endhighlight %}

{% include elements/anchor.html name="Colors" %}

<div class="tags has-addons">
  <span class="tag">New!</span>
  <span class="tag is-info">0.5.2</span>
</div>

<div class="content">
  <p>
    You can change the background color of the <code>navbar</code> by using one of the <strong>9 color modifiers:</strong>
  </p>
  <ul>
    <li><code>is-primary</code></li>
    <li><code>is-link</code></li>
    <li><code>is-info</code></li>
    <li><code>is-success</code></li>
    <li><code>is-warning</code></li>
    <li><code>is-danger</code></li>
    <li><code>is-black</code></li>
    <li><code>is-dark</code></li>
    <li><code>is-light</code></li>
    <li><code>is-white</code></li>
  </ul>
</div>

{% highlight html %}{{ navbar_color_markup }}{% endhighlight %}

<div class="bd-example is-paddingless">
  {% include examples/navbar-color.html color="primary" %}
</div>

<div class="bd-example is-paddingless">
  {% include examples/navbar-color.html color="link" %}
</div>

<div class="bd-example is-paddingless">
  {% include examples/navbar-color.html color="info" %}
</div>

<div class="bd-example is-paddingless">
  {% include examples/navbar-color.html color="success" %}
</div>

<div class="bd-example is-paddingless">
  {% include examples/navbar-color.html color="warning" light=true %}
</div>

<div class="bd-example is-paddingless">
  {% include examples/navbar-color.html color="danger" %}
</div>

<div class="bd-example is-paddingless">
  {% include examples/navbar-color.html color="black" %}
</div>

<div class="bd-example is-paddingless">
  {% include examples/navbar-color.html color="dark" %}
</div>

<div class="bd-example is-paddingless">
  {% include examples/navbar-color.html color="light" light=true %}
</div>

<div class="bd-example is-paddingless">
  {% include examples/navbar-color.html color="white" light=true %}
</div>

{% include elements/anchor.html name="Navbar Helper Classes" %}

<table class="table is-bordered">
  <tbody>
    <tr>
      <th>Type</th>
      <th>Name</th>
      <th>Description</th>
    </tr>
    <tr>
      <th rowspan="1">Spacing</th>
      <td><code>is-spaced</code></td>
      <td>Sets <strong>Top</strong> and <strong>Bottom</strong> paddings with <strong>1rem</strong>,
          <br> <strong>Left</strong> and <strong>Right</strong> paddings with <strong>2rem</strong>
      </td>
    </tr>
    <tr>
      <th rowspan="1">Shading</th>
      <td><code>has-shadow</code></td>
      <td>Adds a small amount of box-shadow around the navbar</td>
    </tr>
  </tbody>
</table>


{% include components/variables.html type='component' %}
