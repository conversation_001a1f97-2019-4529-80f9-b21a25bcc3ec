---
layout: documentation
doc-tab: components
doc-subtab: nav
---

{% include subnav/subnav-components.html %}

<section class="section">
  <div class="container">

    <div class="message is-danger">
      <div class="message-body">
        <p>This component has been <strong>deprecated</strong>.</p>
      </div>
    </div>

    <div class="message is-info">
      <div class="message-body">
        <p>While both <code>.nav</code> and <code>.navbar</code> currently co-exist to ensure backwards compatibility, the <code>.nav</code> will probably be deleted in an upcoming update, so you should start using <a href="{{ site.url }}/documentation/components/navbar/">the new navbar</a> instead.</p>
      </div>
    </div>

    <h1 class="title">Nav</h1>
    <h2 class="subtitle">
      A responsive horizontal <strong>nav bar</strong> that can contain links, tabs, buttons, icons, and a logo
    </h2>

    <hr>

    <div class="content">
      <p>
        The <code>nav</code> container can have <strong>3 parts</strong>:
      </p>
      <ul>
        <li><code>nav-left</code></li>
        <li><code>nav-center</code></li>
        <li><code>nav-right</code></li>
      </ul>
      <p>
        Each nav item needs to be wrapped in a <code>nav-item</code> element.
      </p>
      <p>
        For responsiveness, <strong>2 additional</strong> classes are available:
      </p>
      <ul>
        <li><code>nav-toggle</code> for the hamburger menu on mobile</li>
        <li><code>nav-menu</code> for menu that is collapsable on mobile (you can combine it with <code>nav-right</code>)</li>
        <li>toggle <code>is-active</code> on <code>nav-toggle</code> and <code>nav-menu</code> when <code>nav-toggle</code> was clicked</li>
      </ul>
    </div>

{% capture nav_example %}
<nav class="nav">
  <div class="nav-left">
    <a class="nav-item">
      <img src="{{ site.url }}/images/bulma-logo.png" alt="Bulma logo">
    </a>
  </div>

  <div class="nav-center">
    <a class="nav-item">
      <span class="icon">
        <i class="fab fa-github"></i>
      </span>
    </a>
    <a class="nav-item">
      <span class="icon">
        <i class="fab fa-twitter"></i>
      </span>
    </a>
  </div>

  <!-- This "nav-toggle" hamburger menu is only visible on mobile -->
  <!-- You need JavaScript to toggle the "is-active" class on "nav-menu" -->
  <span class="nav-toggle">
    <span></span>
    <span></span>
    <span></span>
  </span>

  <!-- This "nav-menu" is hidden on mobile -->
  <!-- Add the modifier "is-active" to display it on mobile -->
  <div class="nav-right nav-menu">
    <a class="nav-item">
      Home
    </a>
    <a class="nav-item">
      Documentation
    </a>
    <a class="nav-item">
      Blog
    </a>

    <div class="nav-item">
      <div class="field is-grouped">
        <p class="control">
          <a class="button" >
            <span class="icon">
              <i class="fab fa-twitter"></i>
            </span>
            <span>Tweet</span>
          </a>
        </p>
        <p class="control">
          <a class="button is-primary">
            <span class="icon">
              <i class="fas fa-download"></i>
            </span>
            <span>Download</span>
          </a>
        </p>
      </div>
    </div>
  </div>
</nav>
{% endcapture %}

<div class="bd-example is-paddingless">
{{nav_example}}
</div>

{% highlight html %}
{{nav_example}}
{% endhighlight %}

    <hr>

    <h3 class="title">Modifiers</h3>

    <div class="content">
      <ul>
        <li>the <code>nav</code> container can have a <strong>shadow</strong> by adding the <code>has-shadow</code> modifier</li>
        <li>the <code>nav-item</code> can become <strong>active</strong> by adding the <code>is-active</code> modifier</li>
        <li>the <code>nav-item</code> can become a <strong>tab</strong> by adding the <code>is-tab</code> modifier</li>
      </ul>
      <p>
        To optimise the space on desktop, but also allow the mobile view to be usable, you can <strong>duplicate</strong> nav items in both <code>nav-left</code> and <code>nav-right</code>, and show/hide them with <a href="{{site.url}}/documentation/modifiers/responsive-helpers/">responsive helpers</a>.
      </p>
    </div>
  </div>

{% capture nav_tabs_example %}
<nav class="nav has-shadow">
  <div class="container">
    <div class="nav-left">
      <a class="nav-item">
        <img src="{{ site.url }}/images/bulma-logo.png" alt="Bulma logo">
      </a>
      <a class="nav-item is-tab is-hidden-mobile is-active">Home</a>
      <a class="nav-item is-tab is-hidden-mobile">Features</a>
      <a class="nav-item is-tab is-hidden-mobile">Pricing</a>
      <a class="nav-item is-tab is-hidden-mobile">About</a>
    </div>
    <span class="nav-toggle">
      <span></span>
      <span></span>
      <span></span>
    </span>
    <div class="nav-right nav-menu">
      <a class="nav-item is-tab is-hidden-tablet is-active">Home</a>
      <a class="nav-item is-tab is-hidden-tablet">Features</a>
      <a class="nav-item is-tab is-hidden-tablet">Pricing</a>
      <a class="nav-item is-tab is-hidden-tablet">About</a>
      <a class="nav-item is-tab">
        <figure class="image is-16x16" style="margin-right: 8px;">
          <img src="{{site.url}}/images/jgthms.png">
        </figure>
        Profile
      </a>
      <a class="nav-item is-tab">Log out</a>
    </div>
  </div>
</nav>
{% endcapture %}

<div class="bd-example is-paddingless">
{{nav_tabs_example}}
</div>

{% highlight html %}
{{nav_tabs_example}}
{% endhighlight %}

</section>
