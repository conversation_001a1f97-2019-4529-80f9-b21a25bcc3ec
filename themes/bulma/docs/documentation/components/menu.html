---
title: Menu
layout: documentation
doc-tab: components
doc-subtab: menu
breadcrumb:
- home
- documentation
- components
- components-menu
meta:
  colors: false
  sizes: false
  variables: true
---

{% capture menu_example %}
<aside class="menu">
  <p class="menu-label">
    General
  </p>
  <ul class="menu-list">
    <li><a>Dashboard</a></li>
    <li><a>Customers</a></li>
  </ul>
  <p class="menu-label">
    Administration
  </p>
  <ul class="menu-list">
    <li><a>Team Settings</a></li>
    <li>
      <a class="is-active">Manage Your Team</a>
      <ul>
        <li><a>Members</a></li>
        <li><a>Plugins</a></li>
        <li><a>Add a member</a></li>
      </ul>
    </li>
    <li><a>Invitations</a></li>
    <li><a>Cloud Storage Environment Settings</a></li>
    <li><a>Authentication</a></li>
  </ul>
  <p class="menu-label">
    Transactions
  </p>
  <ul class="menu-list">
    <li><a>Payments</a></li>
    <li><a>Transfers</a></li>
    <li><a>Balance</a></li>
  </ul>
</aside>
{% endcapture %}

<div class="content">
  <p>
    The Bulma <code>menu</code> is a vertical navigation component that comprises:
  </p>
  <ul>
    <li>
      the <code>menu</code> container
    </li>
    <li>
      informative <code>menu-label</code> labels
    </li>
    <li>
      interactive <code>menu-list</code> lists that can be nested up to 2 levels
    </li>
  </ul>
</div>

{% include elements/snippet.html content=menu_example size="one-third" %}

{% include components/variables.html type='component' %}
