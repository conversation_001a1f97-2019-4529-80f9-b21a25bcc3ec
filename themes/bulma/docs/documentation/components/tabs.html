---
title: Tabs
layout: documentation
doc-tab: components
doc-subtab: tabs
breadcrumb:
- home
- documentation
- components
- components-tabs
meta:
  colors: false
  sizes: true
  variables: true
---

{% capture tabs_example %}
<div class="tabs">
  <ul>
    <li class="is-active"><a>Pictures</a></li>
    <li><a>Music</a></li>
    <li><a>Videos</a></li>
    <li><a>Documents</a></li>
  </ul>
</div>
{% endcapture %}

{% capture tabs_centered_example %}
<div class="tabs is-centered">
  <ul>
    <li class="is-active"><a>Pictures</a></li>
    <li><a>Music</a></li>
    <li><a>Videos</a></li>
    <li><a>Documents</a></li>
  </ul>
</div>
{% endcapture %}

{% capture tabs_right_example %}
<div class="tabs is-right">
  <ul>
    <li class="is-active"><a>Pictures</a></li>
    <li><a>Music</a></li>
    <li><a>Videos</a></li>
    <li><a>Documents</a></li>
  </ul>
</div>
{% endcapture %}

{% capture tabs_icons_example %}
<div class="tabs is-centered">
  <ul>
    <li class="is-active">
      <a>
        <span class="icon is-small"><i class="fas fa-image" aria-hidden="true"></i></span>
        <span>Pictures</span>
      </a>
    </li>
    <li>
      <a>
        <span class="icon is-small"><i class="fas fa-music" aria-hidden="true"></i></span>
        <span>Music</span>
      </a>
    </li>
    <li>
      <a>
        <span class="icon is-small"><i class="fas fa-film" aria-hidden="true"></i></span>
        <span>Videos</span>
      </a>
    </li>
    <li>
      <a>
        <span class="icon is-small"><i class="far fa-file-alt" aria-hidden="true"></i></span>
        <span>Documents</span>
      </a>
    </li>
  </ul>
</div>
{% endcapture %}

{% capture tabs_small_example %}
<div class="tabs is-small">
  <ul>
    <li class="is-active"><a>Pictures</a></li>
    <li><a>Music</a></li>
    <li><a>Videos</a></li>
    <li><a>Documents</a></li>
  </ul>
</div>
{% endcapture %}

{% capture tabs_medium_example %}
<div class="tabs is-medium">
  <ul>
    <li class="is-active"><a>Pictures</a></li>
    <li><a>Music</a></li>
    <li><a>Videos</a></li>
    <li><a>Documents</a></li>
  </ul>
</div>
{% endcapture %}

{% capture tabs_large_example %}
<div class="tabs is-large">
  <ul>
    <li class="is-active"><a>Pictures</a></li>
    <li><a>Music</a></li>
    <li><a>Videos</a></li>
    <li><a>Documents</a></li>
  </ul>
</div>
{% endcapture %}

{% capture tabs_boxed_example %}
<div class="tabs is-boxed">
  <ul>
    <li class="is-active">
      <a>
        <span class="icon is-small"><i class="fas fa-image" aria-hidden="true"></i></span>
        <span>Pictures</span>
      </a>
    </li>
    <li>
      <a>
        <span class="icon is-small"><i class="fas fa-music" aria-hidden="true"></i></span>
        <span>Music</span>
      </a>
    </li>
    <li>
      <a>
        <span class="icon is-small"><i class="fas fa-film" aria-hidden="true"></i></span>
        <span>Videos</span>
      </a>
    </li>
    <li>
      <a>
        <span class="icon is-small"><i class="far fa-file-alt" aria-hidden="true"></i></span>
        <span>Documents</span>
      </a>
    </li>
  </ul>
</div>
{% endcapture %}

{% capture tabs_toggle_example %}
<div class="tabs is-toggle">
  <ul>
    <li class="is-active">
      <a>
        <span class="icon is-small"><i class="fas fa-image" aria-hidden="true"></i></span>
        <span>Pictures</span>
      </a>
    </li>
    <li>
      <a>
        <span class="icon is-small"><i class="fas fa-music" aria-hidden="true"></i></span>
        <span>Music</span>
      </a>
    </li>
    <li>
      <a>
        <span class="icon is-small"><i class="fas fa-film" aria-hidden="true"></i></span>
        <span>Videos</span>
      </a>
    </li>
    <li>
      <a>
        <span class="icon is-small"><i class="far fa-file-alt" aria-hidden="true"></i></span>
        <span>Documents</span>
      </a>
    </li>
  </ul>
</div>
{% endcapture %}

{% capture tabs_toggle_rounded_example %}
<div class="tabs is-toggle is-toggle-rounded">
  <ul>
    <li class="is-active">
      <a>
        <span class="icon is-small"><i class="fas fa-image"></i></span>
        <span>Pictures</span>
      </a>
    </li>
    <li>
      <a>
        <span class="icon is-small"><i class="fas fa-music"></i></span>
        <span>Music</span>
      </a>
    </li>
    <li>
      <a>
        <span class="icon is-small"><i class="fas fa-film"></i></span>
        <span>Videos</span>
      </a>
    </li>
    <li>
      <a>
        <span class="icon is-small"><i class="fas fa-file-alt"></i></span>
        <span>Documents</span>
      </a>
    </li>
  </ul>
</div>
{% endcapture %}

{% capture tabs_fullwidth_example %}
<div class="tabs is-fullwidth">
  <ul>
    <li>
      <a>
        <span class="icon"><i class="fas fa-angle-left" aria-hidden="true"></i></span>
        <span>Left</span>
      </a>
    </li>
    <li>
      <a>
        <span class="icon"><i class="fas fa-angle-up" aria-hidden="true"></i></span>
        <span>Up</span>
      </a>
    </li>
    <li>
      <a>
        <span>Right</span>
        <span class="icon"><i class="fas fa-angle-right" aria-hidden="true"></i></span>
      </a>
    </li>
  </ul>
</div>
{% endcapture %}

{% capture tabs_centered_boxed_example %}
<div class="tabs is-centered is-boxed">
  <ul>
    <li class="is-active">
      <a>
        <span class="icon is-small"><i class="fas fa-image" aria-hidden="true"></i></span>
        <span>Pictures</span>
      </a>
    </li>
    <li>
      <a>
        <span class="icon is-small"><i class="fas fa-music" aria-hidden="true"></i></span>
        <span>Music</span>
      </a>
    </li>
    <li>
      <a>
        <span class="icon is-small"><i class="fas fa-film" aria-hidden="true"></i></span>
        <span>Videos</span>
      </a>
    </li>
    <li>
      <a>
        <span class="icon is-small"><i class="far fa-file-alt" aria-hidden="true"></i></span>
        <span>Documents</span>
      </a>
    </li>
  </ul>
</div>
{% endcapture %}

{% capture tabs_toggle_fullwidth_example %}
<div class="tabs is-toggle is-fullwidth">
  <ul>
    <li class="is-active">
      <a>
        <span class="icon is-small"><i class="fas fa-image" aria-hidden="true"></i></span>
        <span>Pictures</span>
      </a>
    </li>
    <li>
      <a>
        <span class="icon is-small"><i class="fas fa-music" aria-hidden="true"></i></span>
        <span>Music</span>
      </a>
    </li>
    <li>
      <a>
        <span class="icon is-small"><i class="fas fa-film" aria-hidden="true"></i></span>
        <span>Videos</span>
      </a>
    </li>
    <li>
      <a>
        <span class="icon is-small"><i class="far fa-file-alt" aria-hidden="true"></i></span>
        <span>Documents</span>
      </a>
    </li>
  </ul>
</div>
{% endcapture %}

{% capture tabs_centered_boxed_medium_example %}
<div class="tabs is-centered is-boxed is-medium">
  <ul>
    <li class="is-active">
      <a>
        <span class="icon is-small"><i class="fas fa-image" aria-hidden="true"></i></span>
        <span>Pictures</span>
      </a>
    </li>
    <li>
      <a>
        <span class="icon is-small"><i class="fas fa-music" aria-hidden="true"></i></span>
        <span>Music</span>
      </a>
    </li>
    <li>
      <a>
        <span class="icon is-small"><i class="fas fa-film" aria-hidden="true"></i></span>
        <span>Videos</span>
      </a>
    </li>
    <li>
      <a>
        <span class="icon is-small"><i class="far fa-file-alt" aria-hidden="true"></i></span>
        <span>Documents</span>
      </a>
    </li>
  </ul>
</div>
{% endcapture %}

{% capture tabs_toggle_fullwidth_large_example %}
<div class="tabs is-toggle is-fullwidth is-large">
  <ul>
    <li class="is-active">
      <a>
        <span class="icon"><i class="fas fa-image" aria-hidden="true"></i></span>
        <span>Pictures</span>
      </a>
    </li>
    <li>
      <a>
        <span class="icon"><i class="fas fa-music" aria-hidden="true"></i></span>
        <span>Music</span>
      </a>
    </li>
    <li>
      <a>
        <span class="icon"><i class="fas fa-film" aria-hidden="true"></i></span>
        <span>Videos</span>
      </a>
    </li>
    <li>
      <a>
        <span class="icon"><i class="far fa-file-alt" aria-hidden="true"></i></span>
        <span>Documents</span>
      </a>
    </li>
  </ul>
</div>
{% endcapture %}

<div class="content">
  <p>
    The Bulma <code>tabs</code> are a straightforward navigation component that come in a variety of versions. They only require the following structure:
  </p>
  <ul>
    <li>
      a <code>tabs</code> container
    </li>
    <li>
      a <code>&lt;ul&gt;</code> HTML element
    </li>
    <li>
      a list of <code>&lt;li&gt;</code> HTML element
    </li>
    <li>
      <code>&lt;a&gt;</code> HTML anchor elements for each link
    </li>
  </ul>
  <p>
    The <strong>default</strong> tabs style has a single border at the bottom.
  </p>
</div>

{% include elements/snippet.html content=tabs_example horizontal=true %}

{% include elements/anchor.html name="Alignment" %}

<div class="content">
  <p>
    To align the tabs list, use the <code>is-centered</code> or <code>is-right</code> modifier on the <code>.tabs</code> container:
  </p>
</div>

{% include elements/snippet.html content=tabs_centered_example horizontal=true more=true %}

{% include elements/snippet.html content=tabs_right_example horizontal=true more=true %}

{% include elements/anchor.html name="Icons" %}

<div class="content">
  <p>You can use any of the <a href="http://fontawesome.io/">Font Awesome</a> <strong>icons</strong>.</p>
</div>

{% include elements/snippet.html content=tabs_icons_example horizontal=true more=true %}

{% include elements/anchor.html name="Sizes" %}
<div class="content">
  <p>You can choose between <strong>3 additional sizes</strong>: <code>is-small</code> <code>is-medium</code> and <code>is-large</code>.</p>
</div>

{% include elements/snippet.html content=tabs_small_example horizontal=true more=true %}

{% include elements/snippet.html content=tabs_medium_example horizontal=true more=true %}

{% include elements/snippet.html content=tabs_large_example horizontal=true more=true %}

{% include elements/anchor.html name="Styles" %}

<div class="content">
  If you want a more classic style with <strong>borders</strong>, just append the <code>is-boxed</code> modifier.
</div>

{% include elements/snippet.html content=tabs_boxed_example horizontal=true more=true %}

<p class="content">
  If you want <strong>mutually exclusive</strong> tabs (like radio buttons where clicking one deselects all other ones), use the <code>is-toggle</code> modifier.
</p>

{% include elements/snippet.html content=tabs_toggle_example horizontal=true more=true %}

<p class="content">
  If you use both <code>is-toggle</code> and <code>is-toggle-rounded</code>, the first and last items will be <strong>rounded</strong>.
</p>

{% include elements/snippet.html content=tabs_toggle_rounded_example horizontal=true more=true %}

<p class="content">
  If you want the tabs to take up the <strong>whole width</strong> available, use <code>is-fullwidth</code>.
</p>

{% include elements/snippet.html content=tabs_fullwidth_example horizontal=true more=true %}

{% include elements/anchor.html name="Combining" %}

<div class="content">
  <p>You can <strong>combine</strong> different modifiers. For example, you can have <strong>centered boxed</strong> tabs, or <strong>fullwidth toggle</strong> ones.</p>
</div>

{% include elements/snippet.html content=tabs_centered_boxed_example horizontal=true more=true %}

{% include elements/snippet.html content=tabs_toggle_fullwidth_example horizontal=true more=true %}

{% include elements/snippet.html content=tabs_centered_boxed_medium_example horizontal=true more=true %}

{% include elements/snippet.html content=tabs_toggle_fullwidth_large_example horizontal=true more=true %}

{% include components/variables.html type='component' %}
