---
title: Message
layout: documentation
doc-tab: components
doc-subtab: message
breadcrumb:
- home
- documentation
- components
- components-message
meta:
  colors: true
  sizes: true
  variables: true
---

{% capture message_example %}
<article class="message">
  <div class="message-header">
    <p>Hello World</p>
    <button class="delete" aria-label="delete"></button>
  </div>
  <div class="message-body">
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. <strong>Pellentesque risus mi</strong>, tempus quis placerat ut, porta nec nulla. Vestibulum rhoncus ac ex sit amet fringilla. Nullam gravida purus diam, et dictum <a>felis venenatis</a> efficitur. Aenean ac <em>eleifend lacus</em>, in mollis lectus. Donec sodales, arcu et sollicitudin porttitor, tortor urna tempor ligula, id porttitor mi magna a neque. Donec dui urna, vehicula et sem eget, facilisis sodales sem.
  </div>
</article>
{% endcapture %}

{% capture message_dark_example %}
<article class="message is-dark">
  <div class="message-header">
    <p>Dark</p>
    <button class="delete" aria-label="delete"></button>
  </div>
  <div class="message-body">
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. <strong>Pellentesque risus mi</strong>, tempus quis placerat ut, porta nec nulla. Vestibulum rhoncus ac ex sit amet fringilla. Nullam gravida purus diam, et dictum <a>felis venenatis</a> efficitur. Aenean ac <em>eleifend lacus</em>, in mollis lectus. Donec sodales, arcu et sollicitudin porttitor, tortor urna tempor ligula, id porttitor mi magna a neque. Donec dui urna, vehicula et sem eget, facilisis sodales sem.
  </div>
</article>
{% endcapture %}

{% capture message_small %}
<article class="message is-small">
  <div class="message-header">
    <p>Small message</p>
    <button class="delete is-small" aria-label="delete"></button>
  </div>
  <div class="message-body">
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. <strong>Pellentesque risus mi</strong>, tempus quis placerat ut, porta nec nulla.Nullam gravida purus diam, et dictum <a>felis venenatis</a> efficitur. Aenean ac <em>eleifend lacus</em>, in mollis lectus.
  </div>
</article>
{% endcapture %}

{% capture message_normal %}
<article class="message">
  <div class="message-header">
    <p>Normal message</p>
    <button class="delete" aria-label="delete"></button>
  </div>
  <div class="message-body">
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. <strong>Pellentesque risus mi</strong>, tempus quis placerat ut, porta nec nulla.Nullam gravida purus diam, et dictum <a>felis venenatis</a> efficitur. Aenean ac <em>eleifend lacus</em>, in mollis lectus.
  </div>
</article>
{% endcapture %}

{% capture message_medium %}
<article class="message is-medium">
  <div class="message-header">
    <p>Medium message</p>
    <button class="delete is-medium" aria-label="delete"></button>
  </div>
  <div class="message-body">
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. <strong>Pellentesque risus mi</strong>, tempus quis placerat ut, porta nec nulla.Nullam gravida purus diam, et dictum <a>felis venenatis</a> efficitur. Aenean ac <em>eleifend lacus</em>, in mollis lectus.
  </div>
</article>
{% endcapture %}

{% capture message_large %}
<article class="message is-large">
  <div class="message-header">
    <p>Large message</p>
    <button class="delete is-large" aria-label="delete"></button>
  </div>
  <div class="message-body">
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. <strong>Pellentesque risus mi</strong>, tempus quis placerat ut, porta nec nulla.Nullam gravida purus diam, et dictum <a>felis venenatis</a> efficitur. Aenean ac <em>eleifend lacus</em>, in mollis lectus.
  </div>
</article>
{% endcapture %}

{% capture message_body_example %}
<article class="message">
  <div class="message-body">
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. <strong>Pellentesque risus mi</strong>, tempus quis placerat ut, porta nec nulla. Vestibulum rhoncus ac ex sit amet fringilla. Nullam gravida purus diam, et dictum <a>felis venenatis</a> efficitur. Aenean ac <em>eleifend lacus</em>, in mollis lectus. Donec sodales, arcu et sollicitudin porttitor, tortor urna tempor ligula, id porttitor mi magna a neque. Donec dui urna, vehicula et sem eget, facilisis sodales sem.
  </div>
</article>
{% endcapture %}

{% capture message_body_dark_example %}
<article class="message is-dark">
  <div class="message-body">
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. <strong>Pellentesque risus mi</strong>, tempus quis placerat ut, porta nec nulla. Vestibulum rhoncus ac ex sit amet fringilla. Nullam gravida purus diam, et dictum <a>felis venenatis</a> efficitur. Aenean ac <em>eleifend lacus</em>, in mollis lectus. Donec sodales, arcu et sollicitudin porttitor, tortor urna tempor ligula, id porttitor mi magna a neque. Donec dui urna, vehicula et sem eget, facilisis sodales sem.
  </div>
</article>
{% endcapture %}

<div class="content">
  <p>
    The Bulma <code>message</code> is a multi-part component:
  </p>
  <ul>
    <li>
      the <code>message</code> container
    </li>
    <li>
      the optional <code>message-header</code> that can hold a title and a <code>delete</code> element
    </li>
    <li>
      the <code>message-body</code> for the longer body of the message
    </li>
  </ul>
</div>

{% include elements/snippet.html content=message_example %}

{% include elements/anchor.html name="Colors" %}

<div class="content">
  <p>
    The message component is available in all the <strong>different colors</strong> defined by the <a href="{{ site.data.links.by_id.customize-variables.path }}"><code>$colors</code> Sass map</a>.
  </p>
</div>

{% include elements/snippet.html content=message_dark_example more=true %}

{% for color in site.data.colors.justColors %}
{% capture foobar %}
<article class="message is-{{ color }}">
  <div class="message-header">
    <p>{{ color | capitalize }}</p>
    <button class="delete" aria-label="delete"></button>
  </div>
  <div class="message-body">
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. <strong>Pellentesque risus mi</strong>, tempus quis placerat ut, porta nec nulla. Vestibulum rhoncus ac ex sit amet fringilla. Nullam gravida purus diam, et dictum <a>felis venenatis</a> efficitur. Aenean ac <em>eleifend lacus</em>, in mollis lectus. Donec sodales, arcu et sollicitudin porttitor, tortor urna tempor ligula, id porttitor mi magna a neque. Donec dui urna, vehicula et sem eget, facilisis sodales sem.
  </div>
</article>
{% endcapture %}
{% include elements/snippet.html content=foobar more=true %}
{% endfor %}

{% include elements/anchor.html name="Message body only" %}

<div class="content">
  <p>
    You can remove the <code>message-header</code> if you don't need it, which will add a left border to the <code>message-body</code>:
  </p>
</div>

{% include elements/snippet.html content=message_body_example %}

{% include elements/snippet.html content=message_body_dark_example %}

{% for color in site.data.colors.justColors %}
{% capture foobar %}
<article class="message is-{{ color }}">
  <div class="message-body">
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. <strong>Pellentesque risus mi</strong>, tempus quis placerat ut, porta nec nulla. Vestibulum rhoncus ac ex sit amet fringilla. Nullam gravida purus diam, et dictum <a>felis venenatis</a> efficitur. Aenean ac <em>eleifend lacus</em>, in mollis lectus. Donec sodales, arcu et sollicitudin porttitor, tortor urna tempor ligula, id porttitor mi magna a neque. Donec dui urna, vehicula et sem eget, facilisis sodales sem.
  </div>
</article>
{% endcapture %}
{% include elements/snippet.html content=foobar %}
{% endfor %}

{% include elements/anchor.html name="Sizes" %}

<div class="content">
  <p>
    You can add one of <strong>3 size modifiers</strong> to the <code>message</code> component:
  </p>
</div>

{% include elements/snippet.html content=message_small %}

{% include elements/snippet.html content=message_normal %}

{% include elements/snippet.html content=message_medium %}

{% include elements/snippet.html content=message_large %}

{% include components/variables.html type='component' %}
