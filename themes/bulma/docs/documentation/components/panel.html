---
title: Panel
layout: documentation
doc-tab: components
doc-subtab: panel
breadcrumb:
- home
- documentation
- components
- components-panel
meta:
  colors: true
  sizes: false
  variables: true
---

{% capture panel_example %}
<nav class="panel">
  <p class="panel-heading">
    Repositories
  </p>
  <div class="panel-block">
    <p class="control has-icons-left">
      <input class="input" type="text" placeholder="Search">
      <span class="icon is-left">
        <i class="fas fa-search" aria-hidden="true"></i>
      </span>
    </p>
  </div>
  <p class="panel-tabs">
    <a class="is-active">All</a>
    <a>Public</a>
    <a>Private</a>
    <a>Sources</a>
    <a>Forks</a>
  </p>
  <a class="panel-block is-active">
    <span class="panel-icon">
      <i class="fas fa-book" aria-hidden="true"></i>
    </span>
    bulma
  </a>
  <a class="panel-block">
    <span class="panel-icon">
      <i class="fas fa-book" aria-hidden="true"></i>
    </span>
    marksheet
  </a>
  <a class="panel-block">
    <span class="panel-icon">
      <i class="fas fa-book" aria-hidden="true"></i>
    </span>
    minireset.css
  </a>
  <a class="panel-block">
    <span class="panel-icon">
      <i class="fas fa-book" aria-hidden="true"></i>
    </span>
    jgthms.github.io
  </a>
  <a class="panel-block">
    <span class="panel-icon">
      <i class="fas fa-code-branch" aria-hidden="true"></i>
    </span>
    daniellowtw/infboard
  </a>
  <a class="panel-block">
    <span class="panel-icon">
      <i class="fas fa-code-branch" aria-hidden="true"></i>
    </span>
    mojs
  </a>
  <label class="panel-block">
    <input type="checkbox">
    remember me
  </label>
  <div class="panel-block">
    <button class="button is-link is-outlined is-fullwidth">
      Reset all filters
    </button>
  </div>
</nav>
{% endcapture %}

<div class="content">
  <p>
    The <code>panel</code> is a container for several types:
  </p>
  <ul>
    <li>
      <code>panel-heading</code> as the first child
    </li>
    <li>
      <code>panel-tabs</code> for navigation
    </li>
    <li>
      <code>panel-block</code> which can contain other elements, like:
      <ul>
        <li><code>control</code></li>
        <li><code>input</code></li>
        <li><code>button</code></li>
        <li><code>panel-icon</code></li>
      </ul>
    </li>
  </ul>
  <p>
    The <code>panel-block</code> can be an anchor tag <code>&lt;a&gt;</code> or a label <code>&lt;label&gt;</code> with a checkbox inside.
  </p>
</div>

{% include elements/snippet.html content=panel_example more=true %}

{% include elements/anchor.html name="Colors" %}

<div class="content">
  <p>
    The panel component is available in all the <strong>different colors</strong> defined by the <a href="{{ site.data.links.by_id.customize-variables.path }}"><code>$colors</code> Sass map</a>.     Simply append one of the color modifiers.
  </p>
  <p>
    For example, to use your primary color, use <code>"panel is-primary"</code> as a class.
  </p>
</div>

{% for color in site.data.colors.justColors %}
{% capture foobar %}
<article class="panel is-{{ color }}">
  <p class="panel-heading">
    {{ color | capitalize }}
  </p>
  <p class="panel-tabs">
    <a class="is-active">All</a>
    <a>Public</a>
    <a>Private</a>
    <a>Sources</a>
    <a>Forks</a>
  </p>
  <div class="panel-block">
    <p class="control has-icons-left">
      <input class="input is-{{ color }}" type="text" placeholder="Search">
      <span class="icon is-left">
        <i class="fas fa-search" aria-hidden="true"></i>
      </span>
    </p>
  </div>
  <a class="panel-block is-active">
    <span class="panel-icon">
      <i class="fas fa-book" aria-hidden="true"></i>
    </span>
    bulma
  </a>
  <a class="panel-block">
    <span class="panel-icon">
      <i class="fas fa-book" aria-hidden="true"></i>
    </span>
    marksheet
  </a>
  <a class="panel-block">
    <span class="panel-icon">
      <i class="fas fa-book" aria-hidden="true"></i>
    </span>
    minireset.css
  </a>
  <a class="panel-block">
    <span class="panel-icon">
      <i class="fas fa-book" aria-hidden="true"></i>
    </span>
    jgthms.github.io
  </a>
</article>
{% endcapture %}
{% include elements/snippet.html content=foobar more=true %}
{% endfor %}

{% include components/variables.html type='component' %}
