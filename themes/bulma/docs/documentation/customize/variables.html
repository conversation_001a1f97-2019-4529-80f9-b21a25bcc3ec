---
title: Variables
layout: documentation
doc-tab: customize
doc-subtab: variables
breadcrumb:
- home
- documentation
- customize
- customize-variables
---

{% assign initial_variables = site.data.variables.utilities.initial-variables %}
{% assign initial_vars      = initial_variables.by_name %}
{% assign derived_variables = site.data.variables.utilities.derived-variables %}
{% assign derived_vars      = derived_variables.by_name %}

<div class="content">
  <p><PERSON><PERSON><PERSON> has <strong>two</strong> variable files divided into <strong>4</strong> sections:</p>

  <ol>
    <li>
      <strong>Initial variables</strong>: where you define variables by <strong>literal value</strong>, like:
      <ul>
        <li><strong>colors</strong>: <code>{{ initial_vars['$blue'].name }}: {{ initial_vars['$blue'].value }}</code></li>
        <li><strong>font sizes</strong>: <code>{{ initial_vars['$size-1'].name }}: {{ initial_vars['$size-1'].value }}</code></li>
        <li><strong>dimensions</strong>: <code>{{ initial_vars['$gap'].name }}: {{ initial_vars['$gap'].value }}</code></li>
        <li><strong>other values</strong>: <code>{{ initial_vars['$easing'].name }}: {{ initial_vars['$easing'].value }}</code> or <code>{{ initial_vars['$radius-large'].name }}: {{ initial_vars['$radius-large'].value }}</code></li>
      </ul>
    </li>
    <li>
      <strong>Derived variables</strong> where variables are <strong>calculated</strong> from the values set in the previous file. For example, you can have:
      <ul>
        <li>
          <strong>Primary colors</strong> derived from the initial variables:
          <ul>
            <li><code>{{ derived_vars['$primary'].name }}: {{ derived_vars['$primary'].value }}</code></li>
            <li><code>{{ derived_vars['$link'].name }}: {{ derived_vars['$link'].value }}</code></li>
            <li><code>{{ derived_vars['$info'].name }}: {{ derived_vars['$info'].value }}</code></li>
            <li><code>{{ derived_vars['$success'].name }}: {{ derived_vars['$success'].value }}</code></li>
            <li><code>{{ derived_vars['$warning'].name }}: {{ derived_vars['$warning'].value }}</code></li>
            <li><code>{{ derived_vars['$danger'].name }}: {{ derived_vars['$danger'].value }}</code></li>
            <li><code>{{ derived_vars['$dark'].name }}: {{ derived_vars['$dark'].value }}</code></li>
            <li><code>{{ derived_vars['$text'].name }}: {{ derived_vars['$text'].value }}</code></li>
          </ul>
        </li>
        <li><code>{{ derived_vars['$background'].name }}: {{ derived_vars['$background'].value }}</code>: a general background color</li>
        <li><code>{{ derived_vars['$link'].name }}: {{ derived_vars['$link'].value }}</code>: the links use the primary color</li>
        <li><code>{{ derived_vars['$family-primary'].name }}: {{ derived_vars['$family-primary'].value }}</code>: the primary font family is the sans-serif one</li>
        <li>
          <strong>Lists and maps</strong> which are collections of already defined variables:
          <ul>
            <li><code>{{ derived_vars['$colors'].name }}: {{ derived_vars['$colors'].value }}</code></li>
            <li><code>{{ derived_vars['$shades'].name }}: {{ derived_vars['$shades'].value }}</code></li>
            <li><code>{{ derived_vars['$sizes'].name }}: {{ derived_vars['$sizes'].value }}</code></li>
          </ul>
        </li>
      </ul>
    </li>
  </ol>

  <p>
    To <strong>override</strong> any of these variables, just set them <em>before</em> importing Bulma.
  </p>
</div>

{% include elements/anchor.html name="Initial variables" %}

<div class="content">
  <p>
    These are <a href="https://github.com/jgthms/bulma/blob/master/sass/{{ initial_variables.file_path }}" target="_blank" rel="nofollow">initial variables</a> with a <strong>literal</strong> value.
  </p>
</div>

<div class="table-container">
  <table class="table is-bordered">
    <tbody>
    {% for variable_name in initial_variables.list %}
      {% assign variable = initial_vars[variable_name] %}
      {% include elements/variable-row.html variable=variable hide_computed =true%}
    {% endfor %}
    <tbody>
  </table>
</div>

{% capture custom_message %}
  These are <a href="https://github.com/jgthms/bulma/blob/master/sass/{{ derived_variables.file_path }}" target="_blank" rel="nofollow">variables</a> with a value that <strong>references</strong> another variable.
{% endcapture %}

{%
  include components/variables.html
    anchor_name    = 'Derived variables'
    data           = derived_variables
    custom_message = custom_message
    table_class    = 'is-bordered'
%}

{% capture custom_message %}
  You can use the following <a href="{{ site.data.variables.base.generic.file_url }}" target="_blank">generic variables</a> for general <strong>customization</strong>. Simply set one or multiple of these variables <em>before</em> importing Bulma. <a href="{{ site.url }}/documentation/overview/customize/">Learn how</a>.
{% endcapture %}

{%
  include components/variables.html
    anchor_name    = 'Generic variables'
    tab            = 'base'
    subtab         = 'generic'
    custom_message = custom_message
    table_class    = 'is-bordered'
%}
