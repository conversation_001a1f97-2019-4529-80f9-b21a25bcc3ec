---
title: With sass-cli
layout: documentation
doc-tab: customize
doc-subtab: sass-cli
breadcrumb:
- home
- documentation
- customize
- customize-sass-cli
---

{% capture gem_install %}
gem install sass
{% endcapture %}

{% capture gem_test %}
Sass 3.5.3 (Bleeding Edge)
{% endcapture %}

{% capture step_1 %}
  <div class="content">
    <p>
      In your terminal, create a new folder called <code>mybulma</code>, navigate to it, then install the <strong>Sass gem</strong>:
    </p>
  </div>

  {% highlight bash %}{{ gem_install }}{% endhighlight %}

  <div class="content">
    <p>
     To try it out, run <code>sass -v</code> and you should see the following:
    </p>
  </div>

  {% highlight bash %}{{ gem_test }}{% endhighlight %}

  <div class="message is-info">
    <div class="message-body">
      <p>
        If you are on <strong>Windows</strong> and use <strong>Chocolatey</strong>, you can try <code>choco install sass</code>.
      </p>
      <p>
        If you are on <strong>Mac</strong> and use <strong>Homebrew</strong>, you can try <code>brew install sass/sass/sass</code>.
      </p>
    </div>
  </div>
{% endcapture %}

{% capture step_2 %}
  <div class="content">
    <p>
      Get the latest version of Bulma:
    </p>
    <p>
      <a class="button is-primary" href="{{ site.data.meta.download }}">
        <strong>Download</strong>
      </a>
    </p>
    <p>
      Unzip it and put the <code>bulma-{{ site.data.meta.version }}</code> folder inside your <code>mybulma</code> folder.
    </p>
  </div>
{% endcapture %}

{% capture build_sass %}
sass --sourcemap=none sass/mystyles.scss:css/mystyles.css
{% endcapture %}

{% capture watch_sass %}
sass --watch --sourcemap=none sass/mystyles.scss:css/mystyles.css
{% endcapture %}

{% capture step_5 %}
  <div class="content">
    <p>
      In your terminal, type the following command:
    </p>
  </div>

  {% highlight bash %}{{ build_sass }}{% endhighlight %}

  <div class="content">
    <p>
      <strong>Reload</strong> the page and it should be styled like this:
    </p>
  </div>

  {%
    include components/figure.html
    path="customize/custom-bulma-02-default"
    extension="png"
    alt="Bulma default styles"
    width="600"
    height="300"
    caption="Bulma's default styles"
  %}

  <div class="content">
    <p>
      To <strong>watch for changes</strong>, just launch the following command:
    </p>
  </div>

  {% highlight bash %}{{ watch_sass }}{% endhighlight %}
{% endcapture %}

{% include components/step.html
  title='1. Install Sass'
  content=step_1
%}

<hr>

{% include components/step.html
  title='2. Download Bulma'
  content=step_2
%}

<hr>

{% assign bulma_path = site.data.meta.version | prepend: "../bulma-" | append: "/bulma.sass" %}

{% include steps/create-sass-file.html
  number="3"
  path=bulma_path
%}

<hr>

{% include steps/create-html-page.html
  number="4"
%}

<hr>

{% include components/step.html
  title='5. Build the CSS file'
  content=step_5
%}

<hr>

{% include steps/add-custom-styles.html
  number="6"
%}
