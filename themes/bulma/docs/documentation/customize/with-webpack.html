---
title: With webpack
layout: documentation
doc-tab: customize
doc-subtab: webpack
breadcrumb:
- home
- documentation
- customize
- customize-webpack
---

{% capture dependencies %}
npm install bulma --save-dev
npm install css-loader --save-dev
npm install extract-text-webpack-plugin@next --save-dev
npm install mini-css-extract-plugin --save-dev
npm install node-sass --save-dev
npm install sass-loader --save-dev
npm install style-loader --save-dev
npm install webpack --save-dev
npm install webpack-cli --save-dev
{% endcapture %}

{% capture package %}
{
  "name": "mybulma",
  "version": "1.0.0",
  "main": "webpack.config.js",
  "license": "MIT",
  "devDependencies": {
    "bulma": "^0.7.2",
    "css-loader": "^1.0.0",
    "extract-text-webpack-plugin": "^4.0.0-beta.0",
    "node-sass": "^4.9.2",
    "sass-loader": "^7.0.3",
    "style-loader": "^0.21.0",
    "webpack": "^4.16.0",
    "webpack-cli": "^3.0.8"
  }
}
{% endcapture %}

{% capture step_2 %}
  <div class="content">
    <p>
      Install the packages required to parse and build your CSS:
    </p>
  </div>

  <!--email_off-->{% highlight bash %}{{ dependencies }}{% endhighlight %}<!--/email_off-->

  <div class="content">
    <p>
      Your <code>package.json</code> should look like this at this point.
    </p>
  </div>

  {% highlight bash %}{{ package }}{% endhighlight %}
{% endcapture %}

{% capture config %}
const path = require('path');
const ExtractTextPlugin = require("extract-text-webpack-plugin");

module.exports = {
  entry: './src/index.js',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'js/bundle.js'
  },
  module: {
    rules: [{
      test: /\.scss$/,
      use: ExtractTextPlugin.extract({
        fallback: 'style-loader',
        use: [
          'css-loader',
          'sass-loader'
        ]
      })
    }]
  },
  plugins: [
    new ExtractTextPlugin('css/mystyles.css'),
  ]
};
{% endcapture %}

{% capture configv4 %}
const path = require('path');
const MiniCssExtractPlugin = require('mini-css-extract-plugin')

module.exports = {
  entry: './src/index.js',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'js/bundle.js'
  },
  module: {
    rules: [{
      test: /\.scss$/,
      use: [
          MiniCssExtractPlugin.loader,
          {
            loader: 'css-loader'
          },
          {
            loader: 'sass-loader',
            options: {
              sourceMap: true,
              // options...
            }
          }
        ]
    }]
  },
  plugins: [
    new MiniCssExtractPlugin({
      filename: 'css/mystyles.css'
    }),
  ]
};
{% endcapture %}

{% capture step_3 %}
  <div class="content">
    <p>
      Create a <code>webpack.config.js</code> file:
    </p>
  </div>

  <div class="bd-highlight-full">
    {% highlight js %}{{ config }}{% endhighlight %}
  </div>

  <div class="content">
    <p>
      This setup takes the <code>src</code> folder as input, and outputs in the <code>dist</code> folder.
    </p>
  </div>
{% endcapture %}

{% capture step_3_5 %}
  <div class="content">
    <p>
      Create a <code>webpack.config.js</code> file:
    </p>
  </div>

  <div class="bd-highlight-full">
    {% highlight js %}{{ configv4 }}{% endhighlight %}
  </div>

  <div class="content">
    <p>
      This setup takes the <code>src</code> folder as input, and outputs in the <code>dist</code> folder.
    </p>
  </div>
{% endcapture %}

{% capture require_css %}
require('./mystyles.scss');
{% endcapture %}

{% capture step_4 %}
  <div class="content">
    <p>
      Create a <code>src</code> folder in which you add a file called <code>index.js</code> with the following content:
    </p>
  </div>

  {% highlight js %}{{ require_css }}{% endhighlight %}
{% endcapture %}

{% capture step_6 %}
  <div class="content">
    <p>
      Create a <code>dist</code> folder in which you add a <code>css</code> folder, and a <code>js</code> folder. Leave these last two folders empty. Their content will be generated by the webpack build.
    </p>
  </div>
{% endcapture %}

{% capture webpack_script %}
"scripts": {
  "build": "webpack --mode production"
},
{% endcapture %}

{% capture npm_build %}
npm run build
{% endcapture %}

{% capture npm_build_success %}
Rendering Complete, saving .css file...
Wrote CSS to /path/to/mybulma/css/mystyles.css
{% endcapture %}

{% capture step_8 %}
  <div class="content">
    <p>
      In <code>package.json</code>, add the following:
    </p>
  </div>

  {% highlight html %}{{ webpack_script }}{% endhighlight %}

  <div class="content">
    <p>
      To test it out, go in your <strong>terminal</strong> and run the following command:
    </p>
  </div>

  {% highlight bash %}{{ npm_build }}{% endhighlight %}
{% endcapture %}

{% include steps/create-package.html
  number="1"
  entry="webpack.config.js"
%}

<hr>

{% include components/step.html
  title="2. Install the dev dependencies"
  content=step_2
%}

<hr>

{% include components/step.html
  title="3. Create a webpack config (Webpack <= 3)"
  content=step_3
%}

<hr>

{% include components/step.html
  title="3.5. Create a webpack config (Webpack 4)"
  content=step_3_5
%}

<hr>

{% include components/step.html
  title="4. Create a <code>src</code> folder"
  content=step_4
%}

<hr>

{% include steps/create-sass-file.html
  number="5"
  path='~bulma/bulma'
  bis=true
%}

<hr>

{% include components/step.html
  title="6. Create a <code>dist</code> folder"
  content=step_6
%}

<hr>

{% include steps/create-html-page.html
  number="7"
  dist=true
%}

<hr>

{% include components/step.html
  title="8. Add node scripts to build your bundle"
  content=step_8
%}

<hr>

{% include steps/add-custom-styles.html
  number="9"
  build=true
%}

