---
title: Concepts
layout: documentation
doc-tab: customize
doc-subtab: concepts
breadcrumb:
- home
- documentation
- customize
- customize-concepts
---

{% assign variables_link = site.data.links.by_id['customize-variables'] %}

<div class="content">
  <p>
    Bulma is highly customizable thanks to <strong>419 Sass variables</strong> living across <strong>28 files</strong>.
  </p>

  <p>
    These variables exist at 4 levels:
  </p>

  <ul>
    <li>
      <strong><a href="{{ site.url }}{{ variables_link.path }}#initial-variables">initial variables</a></strong>: global variables with <strong>literal</strong> values
    </li>
    <li>
      <strong><a href="{{ site.url }}{{ variables_link.path }}#derived-variables">derived variables</a></strong>: global variables with values that reference other variables, or are computed
    </li>
    <li>
      <strong><a href="{{ site.url }}{{ variables_link.path }}#generic-variables">generic variables</a></strong>: for the HTML elements which carry no CSS class
    </li>
    <li>
      <strong>element/component variables</strong>: variables that are specific to a Bulma element/component
    </li>
  </ul>

  <p>
    Since these variables carry the <code>!default</code> flag, they can be assigned a <strong>new value</strong> either before or after having been imported.
  </p>
</div>

{% include elements/anchor.html name="Strategy" %}

{% assign node_sass_link = site.data.links.by_id['customize-node-sass'] %}
{% assign sass_cli_link = site.data.links.by_id['customize-sass-cli'] %}
{% assign webpack_link = site.data.links.by_id['customize-webpack'] %}

<div class="content">
  <p>
    To customize Bulma, you will need to:
  </p>

  <ul>
    <li>
      <strong>install</strong> (or download) Bulma
    </li>
    <li>
      have a working <strong>Sass setup</strong>
    </li>
    <li>
      create your own <code>.scss</code> or <code>.sass</code> file
    </li>
  </ul>

  <p>
    This can be achieved with any of the following:
  </p>

  <ul>
    <li>
     <a href="{{ site.url }}{{ node_sass_link.path }}">node-sass</a>
    </li>
    <li>
      the <a href="{{ site.url }}{{ sass_cli_link.path }}">Sass CLI</a>
    </li>
    <li>
      <a href="{{ site.url }}{{ webpack_link.path }}">webpack</a>
    </li>
  </ul>
</div>
