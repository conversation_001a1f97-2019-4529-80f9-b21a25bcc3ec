---
title: Textarea
layout: documentation
doc-tab: form
doc-subtab: textarea
breadcrumb:
- home
- documentation
- form
- form-textarea
meta:
  colors: true
  sizes: true
  variables: false
---

{% capture textarea_example %}
<textarea class="textarea" placeholder="e.g. Hello world"></textarea>
{% endcapture %}

{% capture textarea_rows_example %}
<textarea class="textarea" placeholder="10 lines of textarea" rows="10"></textarea>
{% endcapture %}

{% capture colors_example %}
<div class="field">
  <div class="control">
    <textarea class="textarea is-primary" placeholder="Primary textarea"></textarea>
  </div>
</div>
<div class="field">
  <div class="control">
    <textarea class="textarea is-info" placeholder="Info textarea"></textarea>
  </div>
</div>
<div class="field">
  <div class="control">
    <textarea class="textarea is-success" placeholder="Success textarea"></textarea>
  </div>
</div>
<div class="field">
  <div class="control">
    <textarea class="textarea is-warning" placeholder="Warning textarea"></textarea>
  </div>
</div>
<div class="field">
  <div class="control">
    <textarea class="textarea is-danger" placeholder="Danger textarea"></textarea>
  </div>
</div>
{% endcapture %}

{% capture sizes_example %}
<div class="field">
  <div class="control">
    <textarea class="textarea is-small" placeholder="Small textarea"></textarea>
  </div>
</div>
<div class="field">
  <div class="control">
    <textarea class="textarea" placeholder="Normal textarea"></textarea>
  </div>
</div>
<div class="field">
  <div class="control">
    <textarea class="textarea is-medium" placeholder="Medium textarea"></textarea>
  </div>
</div>
<div class="field">
  <div class="control">
    <textarea class="textarea is-large" placeholder="Large textarea"></textarea>
  </div>
</div>
{% endcapture %}

{% capture normal_example %}
<div class="control">
  <textarea class="textarea" placeholder="Normal textarea"></textarea>
</div>
{% endcapture %}

{% capture hover_example %}
<div class="control">
  <textarea class="textarea is-hovered" placeholder="Hovered textarea"></textarea>
</div>
{% endcapture %}

{% capture focus_example %}
<div class="control">
  <textarea class="textarea is-focused" placeholder="Focused textarea"></textarea>
</div>
{% endcapture %}

{% capture loading_example %}
<div class="control is-loading">
  <textarea class="textarea" placeholder="Loading textarea"></textarea>
</div>
{% endcapture %}

{% capture loading_sizes_example %}
<div class="field">
  <div class="control is-small is-loading">
    <textarea class="textarea is-small" placeholder="Small loading textarea"></textarea>
  </div>
</div>
<div class="field">
  <div class="control is-loading">
    <textarea class="textarea" placeholder="Normal loading textarea"></textarea>
  </div>
</div>
<div class="field">
  <div class="control is-medium is-loading">
    <textarea class="textarea is-medium" placeholder="Medium loading textarea"></textarea>
  </div>
</div>
<div class="field">
  <div class="control is-large is-loading">
    <textarea class="textarea is-large" placeholder="Large loading textarea"></textarea>
  </div>
</div>
{% endcapture %}

{% capture disabled_example %}
<div class="control">
  <textarea class="textarea" placeholder="Disabled textarea" disabled></textarea>
</div>
{% endcapture %}

{% capture readonly_example %}
<div class="control">
  <textarea class="textarea" readonly>This content is readonly</textarea>
</div>
{% endcapture %}

<div class="content">
  <p>
    The Bulma <code>textarea</code> CSS class is the multiline version of the <a href="{{ site.data.links.by_id.form-input.path }}"><code>input</code> element</a>:
  </p>
</div>

{% include elements/snippet.html content=textarea_example %}

{% capture fixedsize_example %}
<div class="control">
  <textarea class="textarea has-fixed-size" placeholder="Fixed size textarea"></textarea>
</div>
{% endcapture %}

<div class="content">
  <p>
    You can set the height of the textarea using the <code>rows</code> HTML attribute.
  </p>
</div>

{% include elements/snippet.html content=textarea_rows_example %}

{% include elements/anchor.html name="Colors" %}

<div class="content">
  <p>
    The <code>textarea</code> element is available in several colors:
  </p>
</div>

{% for color in site.data.colors.justColors %}
{% capture foobar %}
<textarea class="textarea is-{{ color }}" placeholder="{{ color | capitalize }} textarea"></textarea>
{% endcapture %}
{% include elements/snippet.html content=foobar %}
{% endfor %}

{% include elements/anchor.html name="Sizes" %}

<div class="content">
  <p>
    The <code>textarea</code> element comes in <strong>4 different sizes:</strong>
  </p>
</div>

{% include elements/snippet.html content=sizes_example %}

{% include elements/anchor.html name="States" %}

<div class="content">
  <p>
    Bulma styles the different <strong>states</strong> of the <code>textarea</code> element. Each state is available as a pseudo-class and a CSS class:
  </p>
  <ul>
    <li>
      <code>:hover</code> and <code>is-hovered</code>
    </li>
    <li>
      <code>:focus</code> and <code>is-focused</code>
    </li>
    <li>
      <code>:active</code> and <code>is-active</code>
    </li>
  </ul>
  <p>
    This allows you to obtain the style of a certain state without having to trigger it.
  </p>
</div>

<h4 class="subtitle">Normal</h4>

{% include elements/snippet.html content=normal_example %}

<h4 class="subtitle">Hover</h4>

{% include elements/snippet.html content=hover_example %}

<h4 class="subtitle">Focus</h4>

{% include elements/snippet.html content=focus_example %}

<h4 class="subtitle">Loading</h4>

{% include elements/snippet.html content=loading_example %}

<div class="columns">
  <div class="column is-half">
    <div class="content">
      <p>
        You can resize the loading spinner by appending <code>is-small</code>, <code>is-medium</code> or <code>is-large</code> to the <code>control</code> container.
      </p>
    </div>
    {{loading_sizes_example}}
  </div>
  <div class="column is-half bd-highlight-full">
    {% highlight html %}{{loading_sizes_example}}{% endhighlight %}
  </div>
</div>

<h4 class="subtitle">Disabled</h4>

{% include elements/snippet.html content=disabled_example %}

<h4 class="subtitle">Readonly</h4>

<div class="content">
  <p>
    If you use the <code>readonly</code> HTML attribute, the textarea will look similar to a normal one, but is not editable and has no shadow.
  </p>
</div>

{% include elements/snippet.html content=readonly_example %}

<h4 class="subtitle">Fixed Size</h4>

<div class="content">
  <p>
    You can disable a <code>textarea</code> resizing by appending the <code>has-fixed-size</code> modifier:
  </p>
</div>

{% include elements/snippet.html content=fixedsize_example %}
