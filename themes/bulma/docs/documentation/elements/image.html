---
title: Image
layout: documentation
doc-tab: elements
doc-subtab: image
dimensions:
- 16
- 24
- 32
- 48
- 64
- 96
- 128
breadcrumb:
- home
- documentation
- elements
- elements-image
meta:
  colors: false
  sizes: false
  variables: true
---

{% capture image %}
<figure class="image is-128x128">
  <img src="{{site.url}}/images/placeholders/128x128.png">
</figure>
{% endcapture %}

{% capture rounded_image %}
<figure class="image is-128x128">
  <img class="is-rounded" src="{{site.url}}/images/placeholders/128x128.png">
</figure>
{% endcapture %}

{% capture retina_image %}
<figure class="image is-128x128">
  <img src="{{site.url}}/images/placeholders/256x256.png">
</figure>
{% endcapture %}

{% capture iframe_ratio %}
<figure class="image is-16by9">
  <iframe class="has-ratio" width="640" height="360" src="https://www.youtube.com/embed/YE7VzlLtp-4" frameborder="0" allowfullscreen></iframe>
</figure>
{% endcapture %}

<div class="content">
  <p>Because images can take a few seconds to load (or not at all), use the <code>image</code> container to specify a <strong>precisely sized</strong> container so that your layout isn't broken because of image loading or image errors.</p>
</div>

{% include elements/snippet.html content=image %}

{% include elements/anchor.html name="Fixed square images" %}

<div class="content">
  <p>There are <strong>7</strong> dimensions to choose from, useful for <strong>avatars</strong>:</p>
</div>

<table class="table is-bordered">
  <tbody>
    {% for dimension in page.dimensions %}
      <tr>
        <td><code>image is-{{ dimension }}x{{ dimension }}</code></td>
        <td>
          <figure class="image is-{{ dimension }}x{{ dimension }}"><img src="{{site.url}}/images/placeholders/{{ dimension }}x{{ dimension }}.png"></figure>
        </td>
        <td>{{ dimension }}x{{ dimension }}px</td>
      </tr>
    {% endfor %}
  </tbody>
</table>

{% include elements/anchor.html name="Rounded images" %}

<div class="content">
  <p>You can also make rounded images, using <code>is-rounded</code> class:</p>
</div>

{% include elements/snippet.html content=rounded_image %}

{% include elements/anchor.html name="Retina images" %}

<div class="content">
  <p>Because the image is fixed in size, you can use an image that is <strong>four times as big</strong>. So for example, in a <code>128x128</code> container, you can use a <code>256x256</code> image, but resized to <strong>128x128</strong> pixels.</p>
</div>

{% include elements/snippet.html content=retina_image %}

{% include elements/anchor.html name="Responsive images with ratios" %}

<div class="content">
  <p>If you don't know the exact dimensions but know the <strong>ratio</strong> instead, you can use one of the <strong>16 ratio modifiers</strong>, which include <a href="https://en.wikipedia.org/wiki/Aspect_ratio_%28image%29#Still_photography">common aspect ratios in still photography</a>:</p>
</div>

<table id="images" class="table is-bordered">
  <tbody>
    <tr>
      <td><code>image is-square</code></td>
      <td style="width: 10rem;"><figure class="image is-square"><img src="{{site.url}}/images/placeholders/480x480.png"></figure></td>
      <td>Square (or 1 by 1)</td>
    </tr>
    <tr>
      <td><code>image is-1by1</code></td>
      <td style="width: 10rem;"><figure class="image is-1by1"><img src="{{site.url}}/images/placeholders/480x480.png"></figure></td>
      <td>1 by 1</td>
    </tr>
    <tr>
      <td><code>image is-5by4</code></td>
      <td style="width: 10rem;"><figure class="image is-5by4"><img src="{{site.url}}/images/placeholders/600x480.png"></figure></td>
      <td>5 by 4</td>
    </tr>
    <tr>
      <td><code>image is-4by3</code></td>
      <td style="width: 10rem;"><figure class="image is-4by3"><img src="{{site.url}}/images/placeholders/640x480.png"></figure></td>
      <td>4 by 3</td>
    </tr>
    <tr>
      <td><code>image is-3by2</code></td>
      <td style="width: 10rem;"><figure class="image is-3by2"><img src="{{site.url}}/images/placeholders/480x320.png"></figure></td>
      <td>3 by 2</td>
    </tr>
    <tr>
      <td><code>image is-5by3</code></td>
      <td style="width: 10rem;"><figure class="image is-5by3"><img src="{{site.url}}/images/placeholders/800x480.png"></figure></td>
      <td>5 by 3</td>
    </tr>
    <tr>
      <td><code>image is-16by9</code></td>
      <td style="width: 10rem;"><figure class="image is-16by9"><img src="{{site.url}}/images/placeholders/640x360.png"></figure></td>
      <td>16 by 9</td>
    </tr>
    <tr>
      <td><code>image is-2by1</code></td>
      <td style="width: 10rem;"><figure class="image is-2by1"><img src="{{site.url}}/images/placeholders/640x320.png"></figure></td>
      <td>2 by 1</td>
    </tr>
    <tr>
      <td><code>image is-3by1</code></td>
      <td style="width: 10rem;"><figure class="image is-3by1"><img src="{{site.url}}/images/placeholders/720x240.png"></figure></td>
      <td>3 by 1</td>
  </tr>
    <tr>
      <td><code>image is-4by5</code></td>
      <td style="width: 10rem;"><figure class="image is-4by5"><img src="{{site.url}}/images/placeholders/480x600.png"></figure></td>
      <td>4 by 5</td>
  </tr>
    <tr>
      <td><code>image is-3by4</code></td>
      <td style="width: 10rem;"><figure class="image is-3by4"><img src="{{site.url}}/images/placeholders/480x640.png"></figure></td>
      <td>3 by 4</td>
  </tr>
  <tr>
      <td><code>image is-2by3</code></td>
      <td style="width: 10rem;"><figure class="image is-2by3"><img src="{{site.url}}/images/placeholders/320x480.png"></figure></td>
      <td>2 by 3</td>
  </tr>
    <tr>
      <td><code>image is-3by5</code></td>
      <td style="width: 10rem;"><figure class="image is-3by5"><img src="{{site.url}}/images/placeholders/480x800.png"></figure></td>
      <td>3 by 5</td>
    </tr>
  <tr>
      <td><code>image is-9by16</code></td>
      <td style="width: 10rem;"><figure class="image is-9by16"><img src="{{site.url}}/images/placeholders/360x640.png"></figure></td>
      <td>9 by 16</td>
  </tr>
  <tr>
      <td><code>image is-1by2</code></td>
      <td style="width: 10rem;"><figure class="image is-1by2"><img src="{{site.url}}/images/placeholders/320x640.png"></figure></td>
      <td>1 by 2</td>
  </tr>
  <tr>
    <td><code>image is-1by3</code></td>
    <td style="width: 10rem;"><figure class="image is-1by3"><img src="{{site.url}}/images/placeholders/240x720.png"></figure></td>
    <td>1 by 3</td>
  </tr>
</tbody>
</table>

<div class="content">
  <p>
    The only <strong>requirement</strong> is for the parent element to already have a specific <strong>width</strong>.
  </p>
</div>

<div class="content">
  <p>The <code>image</code> container will usually take up the <strong>whole width</strong> while maintaining the perfect ratio.
  <br>If it doesn't, you can force it by appending the <code>is-fullwidth</code> modifier.</p>
</div>

{% include elements/anchor.html name="Arbitrary ratios with any element" %}

<div class="content">
  <p>
    You can apply a specific <strong>ratio</strong> on <strong>any element</strong> other than an <code>img</code>, simply by applying the <code>has-ratio</code> modifier to a resizable element.
  </p>
  <p>
    For example, you can apply a <code>16by9</code> ratio on an <code>iframe</code>. Resize the browser, and you'll see how the ratio is maintained.
  </p>
</div>

{% include elements/snippet.html content=iframe_ratio horizontal=true %}

{% include components/variables.html type='element' %}
