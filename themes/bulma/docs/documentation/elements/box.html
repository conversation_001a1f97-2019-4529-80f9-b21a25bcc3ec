---
title: Box
layout: documentation
doc-tab: elements
doc-subtab: box
breadcrumb:
- home
- documentation
- elements
- elements-box
meta:
  colors: false
  sizes: false
  variables: true
---

{% capture box_example %}
<div class="box">
  I'm in a box.
</div>
{% endcapture %}

{% capture box_form_example %}
<form class="box">
  <div class="field">
    <label class="label">Email</label>
    <div class="control">
      <input class="input" type="email" placeholder="e.g. <EMAIL>">
    </div>
  </div>

  <div class="field">
    <label class="label">Password</label>
    <div class="control">
      <input class="input" type="password" placeholder="********">
    </div>
  </div>

  <button class="button is-primary">Sign in</button>
</form>
{% endcapture %}

{% capture box_card_example %}
<div class="box">
  <article class="media">
    <div class="media-left">
      <figure class="image is-64x64">
        <img src="{{site.url}}/images/placeholders/128x128.png" alt="Image">
      </figure>
    </div>
    <div class="media-content">
      <div class="content">
        <p>
          <strong><PERSON></strong> <small>@johnsmith</small> <small>31m</small>
          <br>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean efficitur sit amet massa fringilla egestas. Nullam condimentum luctus turpis.
        </p>
      </div>
      <nav class="level is-mobile">
        <div class="level-left">
          <a class="level-item" aria-label="reply">
            <span class="icon is-small">
              <i class="fas fa-reply" aria-hidden="true"></i>
            </span>
          </a>
          <a class="level-item" aria-label="retweet">
            <span class="icon is-small">
              <i class="fas fa-retweet" aria-hidden="true"></i>
            </span>
          </a>
          <a class="level-item" aria-label="like">
            <span class="icon is-small">
              <i class="fas fa-heart" aria-hidden="true"></i>
            </span>
          </a>
        </div>
      </nav>
    </div>
  </article>
</div>
{% endcapture %}

<div class="content">
  <p>
    The <code>box</code> element is a simple container with a white background, some padding, and a box shadow.
  </p>
</div>

{% include elements/snippet.html content=box_example %}

<div class="content">
  <p>
    Because it acts as a container, you can easily include other components, like <strong>form elements</strong>:
  </p>
</div>

{% include elements/snippet.html content=box_form_example more=true %}

<div class="content">
  <p>
    Or a  <code>media</code> object:
  </p>
</div>

{% include elements/snippet.html content=box_card_example more=true %}

{% include components/variables.html type='element' %}
