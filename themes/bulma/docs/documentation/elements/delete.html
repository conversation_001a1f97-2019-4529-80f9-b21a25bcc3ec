---
title: Delete
layout: documentation
doc-tab: elements
doc-subtab: delete
breadcrumb:
- home
- documentation
- elements
- elements-delete
meta:
  colors: false
  sizes: true
  variables: false
---

{% capture cross_example %}
<button class="delete"></button>
{% endcapture %}

{% capture cross_sizes_example %}
<button class="delete is-small"></button>
<button class="delete"></button>
<button class="delete is-medium"></button>
<button class="delete is-large"></button>
{% endcapture %}

{% capture cross_elements_example %}
<div class="block">
  <span class="tag is-success">
    Hello World
    <button class="delete is-small"></button>
  </span>
</div>

<div class="notification is-danger">
  <button class="delete"></button>
  Lorem ipsum dolor sit amet, consectetur adipiscing elit lorem ipsum dolor sit amet, consectetur adipiscing elit
</div>

<article class="message is-info">
  <div class="message-header">
    Info
    <button class="delete"></button>
  </div>
  <div class="message-body">
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque risus mi, tempus quis placerat ut, porta nec nulla. Vestibulum rhoncus ac ex sit amet fringilla. Nullam gravida purus diam, et dictum felis venenatis efficitur. Aenean ac eleifend lacus, in mollis lectus. Donec sodales, arcu et sollicitudin porttitor, tortor urna tempor ligula, id porttitor mi magna a neque. Donec dui urna, vehicula et sem eget, facilisis sodales sem.
  </div>
</article>
{% endcapture %}

<div class="content">
  <p>
    The <code>delete</code> element is a stand-alone element that can be used in different contexts.
  </p>
  <p>
    On its own, it's a simple circle with a cross:
  </p>
</div>

{% include elements/snippet.html content=cross_example %}

{% include elements/anchor.html name="Sizes" %}

<div class="content">
  <p>
    It comes in <strong>4 sizes</strong>:
  </p>
</div>

{% include elements/snippet.html content=cross_sizes_example %}

{% include elements/anchor.html name="Combinations" %}

<div class="content">
  <p>
    Bulma uses it for the <a href="{{ site.url }}/documentation/elements/tag/">tags</a>, the <a href="{{ site.url }}/documentation/elements/notification/">notifications</a>, and the <a href="{{ site.url }}/documentation/components/message/">messages</a>:
  </p>
</div>

{% include elements/snippet.html content=cross_elements_example %}
