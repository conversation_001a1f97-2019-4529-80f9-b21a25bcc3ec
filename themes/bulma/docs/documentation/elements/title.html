---
title: Title and Subtitle
layout: documentation
doc-tab: elements
doc-subtab: title
breadcrumb:
- home
- documentation
- elements
- elements-title
meta:
  colors: false
  sizes: true
  variables: true
---

{% capture default %}
<h1 class="title">Title</h1>
<h2 class="subtitle">Subtitle</h2>
{% endcapture %}

{% capture title_sizes %}
<h1 class="title is-1">Title 1</h1>
<h2 class="title is-2">Title 2</h2>
<h3 class="title is-3">Title 3</h3>
<h4 class="title is-4">Title 4</h4>
<h5 class="title is-5">Title 5</h5>
<h6 class="title is-6">Title 6</h6>
{% endcapture %}

{% capture subtitle_sizes %}
<h1 class="subtitle is-1">Subtitle 1</h1>
<h2 class="subtitle is-2">Subtitle 2</h2>
<h3 class="subtitle is-3">Subtitle 3</h3>
<h4 class="subtitle is-4">Subtitle 4</h4>
<h5 class="subtitle is-5">Subtitle 5</h5>
<h6 class="subtitle is-6">Subtitle 6</h6>
{% endcapture %}

{% capture combine %}
<p class="title is-1">Title 1</p>
<p class="subtitle is-3">Subtitle 3</p>

<p class="title is-2">Title 2</p>
<p class="subtitle is-4">Subtitle 4</p>

<p class="title is-3">Title 3</p>
<p class="subtitle is-5">Subtitle 5</p>
{% endcapture %}

{% capture spaced %}
<p class="title is-1 is-spaced">Title 1</p>
<p class="subtitle is-3">Subtitle 3</p>

<p class="title is-2 is-spaced">Title 2</p>
<p class="subtitle is-4">Subtitle 4</p>

<p class="title is-3 is-spaced">Title 3</p>
<p class="subtitle is-5">Subtitle 5</p>
{% endcapture %}

<div class="columns">
  <div class="column is-3">
    <div class="content">
      <p>There are <strong>2 types</strong> of heading:</p>
      <ul>
        <li>
          <code>title</code>
        </li>
        <li>
          <code>subtitle</code>
        </li>
      </ul>
    </div>
  </div>
  <div class="column is-4">
    <p class="title">Title</p>
    <p class="subtitle">Subtitle</p>
  </div>
  <div class="column is-5">
    {% highlight html %}{{ default }}{% endhighlight %}
  </div>
</div>

{% include elements/anchor.html name="Sizes" %}

<div class="columns">
  <div class="column is-3">
    <p>There are <strong>6 sizes</strong> available:</p>
  </div>
  <div class="column is-4">
    <p class="title is-1">Title 1</p>
    <p class="title is-2">Title 2</p>
    <p class="title is-3">Title 3 (default size)</p>
    <p class="title is-4">Title 4</p>
    <p class="title is-5">Title 5</p>
    <p class="title is-6">Title 6</p>
  </div>
  <div class="column is-5">
    {% highlight html %}{{ title_sizes }}{% endhighlight %}
  </div>
</div>

<div class="columns">
  <div class="column is-3"></div>
  <div class="column is-4">
    <p class="subtitle is-1">Subtitle 1</p>
    <p class="subtitle is-2">Subtitle 2</p>
    <p class="subtitle is-3">Subtitle 3</p>
    <p class="subtitle is-4">Subtitle 4</p>
    <p class="subtitle is-5">Subtitle 5 (default size)</p>
    <p class="subtitle is-6">Subtitle 6</p>
  </div>
  <div class="column is-5">
    {% highlight html %}{{ subtitle_sizes }}{% endhighlight %}
  </div>
</div>

<hr>

<div class="columns">
  <div class="column is-3">
    <div class="content">
      <p>When you <strong>combine</strong> a title and a subtitle, they move closer together.</p>
      <p>As a rule of thumb, it is recommended to use a size difference of <strong>two</strong>. So if you use a <code>title is-1</code>, combine it with a <code>subtitle is-3</code>.</p>
    </div>
  </div>
  <div class="column is-4">
    <div class="block">
      <p class="title is-1">Title 1</p>
      <p class="subtitle is-3">Subtitle 3</p>
    </div>
    <div class="block">
      <p class="title is-2">Title 2</p>
      <p class="subtitle is-4">Subtitle 4</p>
    </div>
    <div class="block">
      <p class="title is-3">Title 3</p>
      <p class="subtitle is-5">Subtitle 5</p>
    </div>
  </div>
  <div class="column is-5">
    {% highlight html %}{{ combine }}{% endhighlight %}
  </div>
</div>

<hr>

<div class="columns">
  <div class="column is-3">
    <div class="content">
      <p>
        <span class="tag is-success">New!</span>
      </p>
      <p>You can maintain the normal spacing between titles and subtitles if you use the <code>is-spaced</code> modifier on the first element.</p>
    </div>
  </div>
  <div class="column is-4">
    <div class="block">
      <p class="title is-1 is-spaced">Title 1</p>
      <p class="subtitle is-3">Subtitle 3</p>
    </div>
    <div class="block">
      <p class="title is-2 is-spaced">Title 2</p>
      <p class="subtitle is-4">Subtitle 4</p>
    </div>
    <div class="block">
      <p class="title is-3 is-spaced">Title 3</p>
      <p class="subtitle is-5">Subtitle 5</p>
    </div>
  </div>
  <div class="column is-5">
    {% highlight html %}{{ spaced }}{% endhighlight %}
  </div>
</div>

{% include components/variables.html type='element' %}
