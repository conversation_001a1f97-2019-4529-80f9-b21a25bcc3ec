---
title: Block
layout: documentation
doc-tab: elements
doc-subtab: block
breadcrumb:
- home
- documentation
- elements
- elements-block
meta:
  colors: false
  sizes: false
  variables: true
---

{% capture block_example %}
<div class="block">
  This text is within a <strong>block</strong>.
</div>
<div class="block">
  This text is within a <strong>second block</strong>. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean efficitur sit amet massa fringilla egestas. Nullam condimentum luctus turpis.
</div>
<div class="block">
  This text is within a <strong>third block</strong>. This block has no margin at the bottom.
</div>
{% endcapture %}

{% capture no_block_example %}
<div>
  This text is <em>not</em> within a <strong>block</strong>.
</div>
<div>
  This text <em>isn't</em> within a <strong>block</strong> either. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean efficitur sit amet massa fringilla egestas. Nullam condimentum luctus turpis.
</div>
<div>
  This text is also <em>not</em> within a <strong>block</strong>.
</div>
{% endcapture %}

{% capture block_css %}
.block:not(:last-child) {
  margin-bottom: 1.5rem;
}
{% endcapture %}

{% capture no_block %}
<p class="title mb-0">Without block</p>
<div class="block mb-0">
  <div class="field is-grouped">
    <div class="control is-expanded">
      <input class="input" type="text" placeholder="Text input">
    </div>
    <div class="control">
      <button class="button is-primary">Button</button>
    </div>
  </div>
</div>
<nav class="pagination mb-0" role="navigation" aria-label="pagination">
  <a class="pagination-previous" title="This is the first page" disabled>Previous</a>
  <a class="pagination-next">Next page</a>
  <ul class="pagination-list">
    <li>
      <a class="pagination-link is-current" aria-label="Page 1" aria-current="page">1</a>
    </li>
    <li>
      <a class="pagination-link" aria-label="Goto page 2">2</a>
    </li>
    <li>
      <a class="pagination-link" aria-label="Goto page 3">3</a>
    </li>
  </ul>
</nav>
<progress class="progress is-success mb-0" value="30" max="100">30%</progress>
<div class="notification is-warning mb-0">
  <button class="delete"></button>
  Primar lorem ipsum dolor sit amet, consectetur adipiscing elit lorem ipsum dolor.
</div>
<article class="message is-danger mb-0">
  <div class="message-header">
    <p>Error</p>
    <button class="delete" aria-label="delete"></button>
  </div>
  <div class="message-body">
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. <strong>Pellentesque risus mi</strong>, tempus quis placerat ut, porta nec nulla. Vestibulum rhoncus ac ex sit amet fringilla. Nullam gravida purus diam, et dictum <a>felis venenatis</a> efficitur.
  </div>
</article>
{% endcapture %}

{% capture with_block %}
<p class="title">With block</p>
<div class="block">
  <div class="field is-grouped">
    <div class="control is-expanded">
      <input class="input" type="text" placeholder="Text input">
    </div>
    <div class="control">
      <button class="button is-primary">Button</button>
    </div>
  </div>
</div>
<nav class="pagination" role="navigation" aria-label="pagination">
  <a class="pagination-previous" title="This is the first page" disabled>Previous</a>
  <a class="pagination-next">Next page</a>
  <ul class="pagination-list">
    <li>
      <a class="pagination-link is-current" aria-label="Page 1" aria-current="page">1</a>
    </li>
    <li>
      <a class="pagination-link" aria-label="Goto page 2">2</a>
    </li>
    <li>
      <a class="pagination-link" aria-label="Goto page 3">3</a>
    </li>
  </ul>
</nav>
<progress class="progress is-success" value="30" max="100">30%</progress>
<div class="notification is-warning">
  <button class="delete"></button>
  Primar lorem ipsum dolor sit amet, consectetur adipiscing elit lorem ipsum dolor.
</div>
<article class="message is-danger">
  <div class="message-header">
    <p>Error</p>
    <button class="delete" aria-label="delete"></button>
  </div>
  <div class="message-body">
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. <strong>Pellentesque risus mi</strong>, tempus quis placerat ut, porta nec nulla. Vestibulum rhoncus ac ex sit amet fringilla. Nullam gravida purus diam, et dictum <a>felis venenatis</a> efficitur.
  </div>
</article>
{% endcapture %}

<!--  -->

<div class="content">
  <p>
    The <code>block</code> element is a simple <strong>spacer tool</strong>. It allows <strong>sibling</strong> HTML elements to have a consistent margin between them:
  </p>
</div>

{% include elements/snippet.html content=block_example %}

<div class="content">
  <p>
    As you can see, the first two blocks have a <code>margin-bottom</code> applied, but <strong>not the third one.</strong>. That is because Bulma applies a space on all blocks, <strong>except the last one</strong>. This means you can use as many blocks as you want, the spacing will only appear <strong>between them</strong>.
  </p>
  <p>
    Without using <code>block</code>, the HTML elements would have no space between them:
  </p>
</div>

{% include elements/snippet.html content=no_block_example %}

<!--  -->

{% include elements/anchor.html name="You're already using it" %}


<div class="content">
  <p>
    As a matter of fact, you're <strong>already using</strong> the <code>block</code> without knowing it. Its CSS properties are <strong>shared</strong> across several Bulma elements and components:
  </p>

  <ul>
    <li><code>breadcrumb</code></li>
    <li><code>level</code></li>
    <li><code>message</code></li>
    <li><code>pagination</code></li>
    <li><code>tabs</code></li>
    <li><code>box</code></li>
    <li><code>content</code></li>
    <li><code>notification</code></li>
    <li><code>other</code></li>
    <li><code>progress</code></li>
    <li><code>table</code></li>
    <li><code>title</code></li>
  </ul>

  <p>
    This is thanks to the <code>@extend %block</code> Sass placeholder feature.
  </p>

  <p>
    Here's how it would look like in comparison:
  </p>
</div>

<div class="columns is-size-6">
  <div class="column">
    {{ no_block }}
  </div>
  <div class="column">
    {{ with_block }}
  </div>
</div>

<div class="content">
  <p>
    No matter which Bulma elements and components you are using, and no matter their order, they will have a <strong>consistent space</strong> between them.
  </p>
</div>

<!--  -->

{% include elements/anchor.html name="One line of CSS" %}

<div class="content">
  <p>
    As you can see, the CSS of the <code>block</code> is very simple: it applies a <code>margin-bottom</code> on all siblings, <strong>except the last one</strong>.
  </p>
</div>

{% highlight css %}{{ block_css }}{% endhighlight %}

<div class="content">
  <p>
    This prevents the last sibling from adding unnecessary space at the bottom.
  </p>
</div>

{% include components/variables.html type='element' %}
