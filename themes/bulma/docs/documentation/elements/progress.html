---
title: Progress Bar
layout: documentation
doc-tab: elements
doc-subtab: progress
breadcrumb:
- home
- documentation
- elements
- elements-progress
meta:
  colors: true
  sizes: true
  variables: true
---

{% capture progress %}
<progress class="progress" value="15" max="100">15%</progress>
{% endcapture %}

{% capture progress_indeterminate %}
<progress class="progress is-small is-primary" max="100">15%</progress>
<progress class="progress is-danger" max="100">30%</progress>
<progress class="progress is-medium is-dark" max="100">45%</progress>
<progress class="progress is-large is-info" max="100">60%</progress>
{% endcapture %}

<div class="content">
  <p>
    The Bulma progress bar is a simple CSS class that styles the native <a href="https://developer.mozilla.org/en-US/docs/Web/HTML/Element/progress" target="_blank"><code>&lt;progress&gt;</code> HTML element</a>.
  </p>
</div>

{% include elements/snippet.html content=progress %}

{% include elements/anchor.html name="Colors" %}

<div class="content">
  <p>
    The progress bar element is available in all the <strong>different colors</strong> defined by the <a href="{{ site.data.links.by_id.customize-variables.path }}"><code>$colors</code> Sass map</a>.
  </p>
</div>

{% for color in site.data.colors.justColors %}
{% capture foobar %}
{% assign n = forloop.index | times: 15 %}
<progress class="progress is-{{ color }}" value="{{ n }}" max="100">{{ n }}%</progress>
{% endcapture %}
{% include elements/snippet.html content=foobar %}
{% endfor %}

{% include elements/anchor.html name="Sizes" %}

{% for size in site.data.sizes %}
{% capture foobar %}
{% assign n = forloop.index | times: 20 %}
<progress class="progress is-{{ size }}" value="{{ n }}" max="100">{{ n }}%</progress>
{% endcapture %}
{% include elements/snippet.html content=foobar %}
{% endfor %}

{% include elements/anchor.html name="Indeterminate" %}

<div class="content">
  <p>
    If you don't set the HTML <code>value</code> attribute, you can display an <strong>indeterminate</strong> progress bar. It's used to show that some progress is going on, but the total duration is not yet determined.
  </p>
</div>

{% include elements/snippet.html content=progress_indeterminate %}

{% include components/variables.html type='element' %}
