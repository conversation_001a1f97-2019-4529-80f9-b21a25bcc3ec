---
title: Button
layout: documentation
doc-tab: elements
doc-subtab: button
breadcrumb:
- home
- documentation
- elements
- elements-button
meta:
  colors: true
  sizes: true
  variables: true
---

{% capture button_example %}
<button class="button">Button</button>
{% endcapture %}

{% capture button_tags_example %}
<a class="button">Anchor</a>
<button class="button">Button</button>
<input class="button" type="submit" value="Submit input">
<input class="button" type="reset" value="Reset input">
{% endcapture %}

{% capture button_colors_a_example %}
<button class="button is-white">White</button>
<button class="button is-light">Light</button>
<button class="button is-dark">Dark</button>
<button class="button is-black">Black</button>
<button class="button is-text">Text</button>
<button class="button is-ghost">Ghost</button>
{% endcapture %}

{% capture button_colors_b_example %}
<div class="buttons">
  <button class="button is-primary">Primary</button>
  <button class="button is-link">Link</button>
</div>

<div class="buttons">
  <button class="button is-info">Info</button>
  <button class="button is-success">Success</button>
  <button class="button is-warning">Warning</button>
  <button class="button is-danger">Danger</button>
</div>
{% endcapture %}

{% capture button_light_colors_b_example %}
<div class="buttons">
  <button class="button is-primary is-light">Primary</button>
  <button class="button is-link is-light">Link</button>
</div>

<div class="buttons">
  <button class="button is-info is-light">Info</button>
  <button class="button is-success is-light">Success</button>
  <button class="button is-warning is-light">Warning</button>
  <button class="button is-danger is-light">Danger</button>
</div>
{% endcapture %}

{% capture button_sizes_example %}
<button class="button is-small">Small</button>
<button class="button">Default</button>
<button class="button is-normal">Normal</button>
<button class="button is-medium">Medium</button>
<button class="button is-large">Large</button>
{% endcapture %}

{% capture buttons_medium_sizes_example %}
<div class="buttons are-medium">
  <button class="button">All</button>
  <button class="button">Medium</button>
  <button class="button">Size</button>
</div>
{% endcapture %}

{% capture buttons_small_normal_sizes_example %}
<div class="buttons are-small">
  <button class="button">Small</button>
  <button class="button">Small</button>
  <button class="button">Small</button>
  <button class="button is-normal">Normal</button>
  <button class="button">Small</button>
</div>
{% endcapture %}

{% capture button_displays_example %}
<button class="button is-small is-fullwidth">Small</button>
<button class="button is-fullwidth">Normal</button>
<button class="button is-medium is-fullwidth">Medium</button>
<button class="button is-large is-fullwidth">Large</button>
{% endcapture %}

{% capture button_outlined_example %}
<button class="button is-outlined">Outlined</button>
<button class="button is-primary is-outlined">Outlined</button>
<button class="button is-link is-outlined">Outlined</button>
<button class="button is-info is-outlined">Outlined</button>
<button class="button is-success is-outlined">Outlined</button>
<button class="button is-danger is-outlined">Outlined</button>
{% endcapture %}

{% capture button_inverted_example %}
<button class="button is-primary is-inverted">Inverted</button>
<button class="button is-link is-inverted">Inverted</button>
<button class="button is-info is-inverted">Inverted</button>
<button class="button is-success is-inverted">Inverted</button>
<button class="button is-danger is-inverted">Inverted</button>
{% endcapture %}

{% capture button_inverted_outlined_example %}
<button class="button is-primary is-inverted is-outlined">Invert Outlined</button>
<button class="button is-link is-inverted is-outlined">Invert Outlined</button>
<button class="button is-info is-inverted is-outlined">Invert Outlined</button>
<button class="button is-success is-inverted is-outlined">Invert Outlined</button>
<button class="button is-danger is-inverted is-outlined">Invert Outlined</button>
{% endcapture %}

{% capture button_rounded_example %}
<button class="button is-rounded">Rounded</button>
<button class="button is-primary is-rounded">Rounded</button>
<button class="button is-link is-rounded">Rounded</button>
<button class="button is-info is-rounded">Rounded</button>
<button class="button is-success is-rounded">Rounded</button>
<button class="button is-danger is-rounded">Rounded</button>
{% endcapture %}

{% capture button_normal_example %}
<button class="button">Normal</button>
<button class="button is-primary">Normal</button>
<button class="button is-link">Normal</button>
<button class="button is-info">Normal</button>
<button class="button is-success">Normal</button>
<button class="button is-warning">Normal</button>
<button class="button is-danger">Normal</button>
{% endcapture %}

{% capture button_hover_example %}
<button class="button is-hovered">Hover</button>
<button class="button is-primary is-hovered">Hover</button>
<button class="button is-link is-hovered">Hover</button>
<button class="button is-info is-hovered">Hover</button>
<button class="button is-success is-hovered">Hover</button>
<button class="button is-warning is-hovered">Hover</button>
<button class="button is-danger is-hovered">Hover</button>
{% endcapture %}

{% capture button_focus_example %}
<button class="button is-focused">Focus</button>
<button class="button is-primary is-focused">Focus</button>
<button class="button is-link is-focused">Focus</button>
<button class="button is-info is-focused">Focus</button>
<button class="button is-success is-focused">Focus</button>
<button class="button is-warning is-focused">Focus</button>
<button class="button is-danger is-focused">Focus</button>
{% endcapture %}

{% capture button_active_example %}
<button class="button is-active">Active</button>
<button class="button is-primary is-active">Active</button>
<button class="button is-link is-active">Active</button>
<button class="button is-info is-active">Active</button>
<button class="button is-success is-active">Active</button>
<button class="button is-warning is-active">Active</button>
<button class="button is-danger is-active">Active</button>
{% endcapture %}

{% capture button_loading_example %}
<button class="button is-loading">Loading</button>
<button class="button is-primary is-loading">Loading</button>
<button class="button is-link is-loading">Loading</button>
<button class="button is-info is-loading">Loading</button>
<button class="button is-success is-loading">Loading</button>
<button class="button is-warning is-loading">Loading</button>
<button class="button is-danger is-loading">Loading</button>
{% endcapture %}

{% capture button_static_example %}
<span class="button is-static">Static</span>
{% endcapture %}

{% capture button_disabled_example %}
<button class="button" title="Disabled button" disabled>Disabled</button>
<button class="button is-primary" title="Disabled button" disabled>Disabled</button>
<button class="button is-link" title="Disabled button" disabled>Disabled</button>
<button class="button is-info" title="Disabled button" disabled>Disabled</button>
<button class="button is-success" title="Disabled button" disabled>Disabled</button>
<button class="button is-warning" title="Disabled button" disabled>Disabled</button>
<button class="button is-danger" title="Disabled button" disabled>Disabled</button>
{% endcapture %}

{% capture button_fa_example %}
<p class="buttons">
  <button class="button">
    <span class="icon is-small">
      <i class="fas fa-bold"></i>
    </span>
  </button>
  <button class="button">
    <span class="icon is-small">
      <i class="fas fa-italic"></i>
    </span>
  </button>
  <button class="button">
    <span class="icon is-small">
      <i class="fas fa-underline"></i>
    </span>
  </button>
</p>
<p class="buttons">
  <button class="button">
    <span class="icon">
      <i class="fab fa-github"></i>
    </span>
    <span>GitHub</span>
  </button>
  <button class="button is-primary">
    <span class="icon">
      <i class="fab fa-twitter"></i>
    </span>
    <span>@jgthms</span>
  </button>
  <button class="button is-success">
    <span class="icon is-small">
      <i class="fas fa-check"></i>
    </span>
    <span>Save</span>
  </button>
  <button class="button is-danger is-outlined">
    <span>Delete</span>
    <span class="icon is-small">
      <i class="fas fa-times"></i>
    </span>
  </button>
</p>
<p class="buttons">
  <button class="button is-small">
    <span class="icon is-small">
      <i class="fab fa-github"></i>
    </span>
    <span>GitHub</span>
  </button>
  <button class="button">
    <span class="icon">
      <i class="fab fa-github"></i>
    </span>
    <span>GitHub</span>
  </button>
  <button class="button is-medium">
    <span class="icon">
      <i class="fab fa-github"></i>
    </span>
    <span>GitHub</span>
  </button>
  <button class="button is-large">
    <span class="icon is-medium">
      <i class="fab fa-github"></i>
    </span>
    <span>GitHub</span>
  </button>
</p>
{% endcapture %}

{% capture button_only_icon_example %}
<p class="buttons">
  <button class="button is-small">
    <span class="icon is-small">
      <i class="fas fa-heading"></i>
    </span>
  </button>
</p>
<p class="buttons">
  <button class="button">
    <span class="icon is-small">
      <i class="fas fa-heading"></i>
    </span>
  </button>
  <button class="button">
    <span class="icon">
      <i class="fas fa-heading fa-lg"></i>
    </span>
  </button>
</p>
<p class="buttons">
  <button class="button is-medium">
    <span class="icon is-small">
      <i class="fas fa-heading"></i>
    </span>
  </button>
  <button class="button is-medium">
    <span class="icon">
      <i class="fas fa-heading fa-lg"></i>
    </span>
  </button>
  <button class="button is-medium">
    <span class="icon is-medium">
      <i class="fas fa-heading fa-2x"></i>
    </span>
  </button>
</p>
<p class="buttons">
  <button class="button is-large">
    <span class="icon is-small">
      <i class="fas fa-heading"></i>
    </span>
  </button>
  <button class="button is-large">
    <span class="icon">
      <i class="fas fa-heading fa-lg"></i>
    </span>
  </button>
  <button class="button is-large">
    <span class="icon is-medium">
      <i class="fas fa-heading fa-2x"></i>
    </span>
  </button>
</p>
{% endcapture %}

{% capture button_group_example %}
<div class="field is-grouped">
  <p class="control">
    <button class="button is-link">
      Save changes
    </button>
  </p>
  <p class="control">
    <button class="button">
      Cancel
    </button>
  </p>
  <p class="control">
    <button class="button is-danger">
      Delete post
    </button>
  </p>
</div>
{% endcapture %}

{% capture button_addons_example %}
<div class="field has-addons">
  <p class="control">
    <button class="button">
      <span class="icon is-small">
        <i class="fas fa-align-left"></i>
      </span>
      <span>Left</span>
    </button>
  </p>
  <p class="control">
    <button class="button">
      <span class="icon is-small">
        <i class="fas fa-align-center"></i>
      </span>
      <span>Center</span>
    </button>
  </p>
  <p class="control">
    <button class="button">
      <span class="icon is-small">
        <i class="fas fa-align-right"></i>
      </span>
      <span>Right</span>
    </button>
  </p>
</div>
{% endcapture %}

{% capture button_group_addons_example %}
<div class="field has-addons">
  <p class="control">
    <button class="button">
      <span class="icon is-small">
        <i class="fas fa-bold"></i>
      </span>
      <span>Bold</span>
    </button>
  </p>
  <p class="control">
    <button class="button">
      <span class="icon is-small">
        <i class="fas fa-italic"></i>
      </span>
      <span>Italic</span>
    </button>
  </p>
  <p class="control">
    <button class="button">
      <span class="icon is-small">
        <i class="fas fa-underline"></i>
      </span>
      <span>Underline</span>
    </button>
  </p>
</div>

<div class="field has-addons">
  <p class="control">
    <button class="button">
      <span class="icon is-small">
        <i class="fas fa-align-left"></i>
      </span>
      <span>Left</span>
    </button>
  </p>
  <p class="control">
    <button class="button">
      <span class="icon is-small">
        <i class="fas fa-align-center"></i>
      </span>
      <span>Center</span>
    </button>
  </p>
  <p class="control">
    <button class="button">
      <span class="icon is-small">
        <i class="fas fa-align-right"></i>
      </span>
      <span>Right</span>
    </button>
  </p>
</div>
{% endcapture %}

{% capture buttons_list %}
<div class="buttons">
  <button class="button is-success">Save changes</button>
  <button class="button is-info">Save and continue</button>
  <button class="button is-danger">Cancel</button>
</div>
{% endcapture %}

{% capture buttons_multiple %}
<div class="buttons">
  <button class="button">One</button>
  <button class="button">Two</button>
  <button class="button">Three</button>
  <button class="button">Four</button>
  <button class="button">Five</button>
  <button class="button">Six</button>
  <button class="button">Seven</button>
  <button class="button">Eight</button>
  <button class="button">Nine</button>
  <button class="button">Ten</button>
  <button class="button">Eleven</button>
  <button class="button">Twelve</button>
  <button class="button">Thirteen</button>
  <button class="button">Fourteen</button>
  <button class="button">Fifteen</button>
  <button class="button">Sixteen</button>
  <button class="button">Seventeen</button>
  <button class="button">Eighteen</button>
  <button class="button">Nineteen</button>
  <button class="button">Twenty</button>
</div>
{% endcapture %}

{% capture buttons_addons %}
<div class="buttons has-addons">
  <button class="button">Yes</button>
  <button class="button">Maybe</button>
  <button class="button">No</button>
</div>
{% endcapture %}

{% capture buttons_addons_centered %}
<div class="buttons has-addons is-centered">
  <button class="button">Yes</button>
  <button class="button">Maybe</button>
  <button class="button">No</button>
</div>
{% endcapture %}

{% capture buttons_addons_right %}
<div class="buttons has-addons is-right">
  <button class="button">Yes</button>
  <button class="button">Maybe</button>
  <button class="button">No</button>
</div>
{% endcapture %}

{% capture buttons_addons_selected %}
<div class="buttons has-addons">
  <button class="button is-success is-selected">Yes</button>
  <button class="button">Maybe</button>
  <button class="button">No</button>
</div>

<div class="buttons has-addons">
  <button class="button">Yes</button>
  <button class="button is-info is-selected">Maybe</button>
  <button class="button">No</button>
</div>

<div class="buttons has-addons">
  <button class="button">Yes</button>
  <button class="button">Maybe</button>
  <button class="button is-danger is-selected">No</button>
</div>
{% endcapture %}

<div class="content">
  <p>
    The <strong>button</strong> is an essential element of any design. It's meant to look and behave as an <strong>interactive</strong> element of your page.
  </p>
</div>

{% include elements/snippet.html wrapper="buttons" content=button_example %}

<div class="content">
  <p>
    The <code>button</code> class can be used on:
  </p>
  <ul>
    <li>
      <code>&lt;a&gt;</code> anchor links
    </li>
    <li>
      <code>&lt;button&gt;</code> form buttons
    </li>
    <li>
      <code>&lt;input type="submit"&gt;</code> submit inputs
    </li>
    <li>
      <code>&lt;input type="reset"&gt;</code> reset inputs
    </li>
  </ul>
</div>

{% include elements/snippet.html wrapper="buttons" content=button_tags_example %}

{% include elements/anchor.html name="Colors" %}

<div class="content">
  <p>
    The button is available in all the <strong>different colors</strong> defined by the <a href="{{ site.data.links.by_id.customize-variables.path }}"><code>$colors</code> Sass map</a>.
  </p>
</div>

{% include elements/snippet.html wrapper="buttons" content=button_colors_a_example %}

{% include elements/snippet.html content=button_colors_b_example %}

<div class="content">
  <p>
    Each color now comes in its <strong>light version</strong>. Simply append the modifier <code>is-light</code> to the color modifier to apply the light version of the button.
  </p>
</div>

{% include elements/snippet.html content=button_light_colors_b_example %}

{% include elements/anchor.html name="Sizes" %}

<div class="content">
  <p>
    The button comes in <strong>4 different sizes:</strong>
  </p>
  <ul>
    <li>
      small
    </li>
    <li>
      normal
    </li>
    <li>
      medium
    </li>
    <li>
      large
    </li>
  </ul>
  <p>
    While the default size is the <strong>normal</strong> one, the <code>is-normal</code> modifier exists in case you need to reset the button to its normal size.
  </p>
</div>

{% include elements/snippet.html wrapper="buttons" content=button_sizes_example %}

<div class="content">
  <p>
    You can change the size of <strong>multiple buttons</strong> at once by wrapping them in a <code>buttons</code> parent, and applying one of 3 modifiers:
  </p>
  <ul>
    <li>
      <code>buttons are-small</code>
    </li>
    <li>
      <code>buttons are-medium</code>
    </li>
    <li>
      <code>buttons are-large</code>
    </li>
  </ul>
</div>

{% include elements/snippet.html content=buttons_medium_sizes_example %}

<div class="content">
  <p>
    You can change the size of only a <strong>subset of buttons</strong> by simply applying a modifier class to them.
    <br>
    For example, if you want all buttons to be <strong>small</strong> but still have one in its <strong>normal</strong> size, simply do the following:
  </p>
</div>

{% include elements/snippet.html content=buttons_small_normal_sizes_example %}

<table class="table is-bordered">
  <thead>
    <tr>
      <th>Name</th>
      <th>Fixed size</th>
      <th>Responsive <small class="has-text-grey has-text-weight-normal">(resize window to see in action)</small></th>
    </tr>
  </thead>
</table>

{% include elements/anchor.html name="Responsive sizes" %}

{% include elements/new-tag.html version="0.9.4" %}

<div class="content">
  <p>
    If you want different button sizes for each breakpoint, you can use Bulma's <strong>responsive</strong> buttons. Simply append the <code>is-responsive</code> modifier:
  </p>
</div>

{% include snippets/responsive-buttons.html %}

<div class="block">
  <a target="_blank" class="button is-small is-link is-outlined" href="{{ site.url }}/playground/responsive-buttons/">
    <span>Open table in new window</span>
    <span class="icon">
      <i class="fa fa-external-link-alt"></i>
    </span>
  </a>
</div>

<div class="content">
  <p>
    You can customise the sizes by overwriting the <code>$button-responsive-sizes</code> Sass variable.
  </p>
</div>

{% include elements/anchor.html name="Displays" %}

{% include elements/snippet.html wrapper="buttons" content=button_displays_example %}

{% include elements/anchor.html name="Styles" %}

<h4 class="subtitle">Outlined</h4>

{% include elements/snippet.html wrapper="buttons" content=button_outlined_example %}

<h4 class="subtitle">Inverted (the text color becomes the background color, and vice-versa)</h4>

<div class="columns">
  <div class="column">
    <div class="bd-callout is-primary">
      <div class="buttons">
        {{ button_inverted_example }}
      </div>
    </div>
  </div>
  <div class="column">
    {% highlight html %}{{button_inverted_example}}{% endhighlight %}
  </div>
</div>

<h4 class="subtitle">Invert Outlined (the invert color becomes the text and border colors)</h4>

<div class="columns">
  <div class="column">
    <div class="bd-callout is-primary">
      <div class="buttons">
        {{ button_inverted_outlined_example }}
      </div>
    </div>
  </div>
  <div class="column">
    {% highlight html %}{{ button_inverted_outlined_example }}{% endhighlight %}
  </div>
</div>

<h4 class="subtitle">Rounded buttons</h4>

<div class="columns">
  <div class="column">
    <div class="buttons">
      {{ button_rounded_example }}
    </div>
  </div>
  <div class="column">
    {% highlight html %}{{ button_rounded_example }}{% endhighlight %}
  </div>
</div>

{% include elements/anchor.html name="States" %}

<div class="content">
  <p>
    Bulma styles the different <strong>states</strong> of its buttons. Each state is available as a pseudo-class and a CSS class:
  </p>
  <ul>
    <li>
      <code>:hover</code> and <code>is-hovered</code>
    </li>
    <li>
      <code>:focus</code> and <code>is-focused</code>
    </li>
    <li>
      <code>:active</code> and <code>is-active</code>
    </li>
  </ul>
  <p>
    This allows you to obtain the style of a certain state without having to trigger it.
  </p>
</div>

<h4 class="subtitle">Normal</h4>

{% include elements/snippet.html wrapper="buttons" content=button_normal_example %}

<h4 class="subtitle">Hover</h4>

{% include elements/snippet.html wrapper="buttons" content=button_hover_example %}

<h4 class="subtitle">Focus</h4>

{% include elements/snippet.html wrapper="buttons" content=button_focus_example %}

<h4 class="subtitle">Active</h4>

{% include elements/snippet.html wrapper="buttons" content=button_active_example %}

<h4 class="subtitle">Loading</h4>

<div class="content">
  <p>
    You can very easily turn a button into its <strong>loading</strong> version by appending the <code>is-loading</code> modifier. You don't even need to remove the inner text, which allows the button to maintain its <strong>original size</strong> between its default and loading states.
  </p>
</div>

<div class="columns">
  <div class="column">
    <div class="buttons">
      {{ button_loading_example }}
    </div>
    <div class="message is-info">
      <div class="message-body">
        <p>
          Since the loading spinner is implemented using the <code>::after</code> pseudo-element, it is not supported by the <code>&lt;input&nbsp;type="submit"&gt;</code> element. Consider using <code>&lt;button&nbsp;type="submit"&gt;</code> instead.
        </p>
      </div>
    </div>
  </div>
  <div class="column">
    {% highlight html %}{{button_loading_example}}{% endhighlight %}
  </div>
</div>

<h4 id="static-button" class="subtitle">
  Static
</h4>

<div class="columns">
  <div class="column">
    <div class="content">
      <p>
        You can create a <strong>non-interactive button</strong> by using the <code>is-static</code> modifier. This is useful to align a text label with an input, for example when using <a href="{{site.url}}/documentation/form/general#form-addons">form addons</a>.
      </p>
    </div>
    {{ button_static_example }}
  </div>
  <div class="column">
    {% highlight html %}{{ button_static_example }}{% endhighlight %}
  </div>
</div>

<h4 class="subtitle">Disabled</h4>

<div class="content">
  <p>
    Bulma supports the use of the <code>disabled</code> <a href="https://developer.mozilla.org/en-US/docs/Web/HTML/Element/button" target="_blank">Boolean HTML attribute</a>, which prevents the user from interacting with the button.
  </p>
</div>

<div class="columns">
  <div class="column">
    <div class="buttons">
      {{ button_disabled_example }}
    </div>
    <div class="message is-danger">
      <div class="message-body">
        <p>The <code>is-disabled</code> CSS class has been deprecated in favor of the <code>disabled</code> HTML attribute. <a href="https://github.com/jgthms/bulma/issues/276">Learn more</a></p>
      </div>
    </div>
  </div>
  <div class="column">
    {% highlight html %}{{button_disabled_example}}{% endhighlight %}
  </div>
</div>

<h4 class="subtitle">With Font Awesome icons</h4>

<div class="content">
  <p>
    Bulma buttons can easily be enhanced by adding a <strong>Font Awesome icon</strong>. For the best results, wrap the inner text in a separate <code>&lt;span&gt;</code> element.
  </p>
</div>

{% include elements/snippet.html content=button_fa_example clipped=true %}

<div class="columns">
  <div class="column">
    <div class="content">
      <p>
        If the button only contains an icon, Bulma will make sure the button remains <strong>square</strong>, no matter the size of the button <em>or</em> of the icon.
      </p>
    </div>
    {{ button_only_icon_example }}
  </div>
  <div class="column">
    {% highlight html %}{{ button_only_icon_example }}{% endhighlight %}
  </div>
</div>

{% include elements/anchor.html name="Button group" %}

<div class="content">
  <p>If you want to <strong>group</strong> buttons together on a <strong>single line</strong>, use the <code>is-grouped</code> modifier on the <code>field</code> container:</p>
</div>

{% include elements/snippet.html content=button_group_example %}

{% include elements/anchor.html name="Button addons" %}

<div class="content">
  <p>If you want to use buttons as <strong>addons</strong>, use the <code>has-addons</code> modifier on the <code>field</code> container:</p>
</div>

{% include elements/snippet.html content=button_addons_example %}

{% include elements/anchor.html name="Button group with addons" %}

<div class="content">
  <p>You can group together addons as well:</p>
</div>

{% include elements/snippet.html content=button_group_addons_example %}

{% include elements/anchor.html name="List of buttons" %}

<div class="columns">
  <div class="column">
    <div class="content">
      <p>
        You can create a <strong>list of buttons</strong> by using the <code>buttons</code> container.
      </p>
    </div>
    <div class="bd-example">
      {{ buttons_list }}
    </div>
  </div>
  <div class="column">
    {% highlight html %}{{ buttons_list }}{% endhighlight %}
  </div>
</div>

<div class="columns">
  <div class="column">
    <div class="content">
      <p>
        If the list is <strong>very long</strong>, it will automatically wrap on <strong>multiple lines</strong>, while keeping all buttons <strong>evenly spaced</strong>.
      </p>
    </div>
    <div class="bd-example">
      {{ buttons_multiple }}
    </div>
  </div>
  <div class="column bd-highlight-full">
    {% highlight html %}{{ buttons_multiple }}{% endhighlight %}
  </div>
</div>

<div class="columns">
  <div class="column">
    <div class="content">
      <p>
        You can <strong>attach buttons together</strong> with the <code>has-addons</code> modifier.
      </p>
    </div>
    <div class="bd-example">
      {{ buttons_addons }}
    </div>
  </div>
  <div class="column bd-highlight-full">
    {% highlight html %}{{ buttons_addons }}{% endhighlight %}
  </div>
</div>

<div class="columns">
  <div class="column">
    <div class="content">
      <p>
        Use the <code>is-centered</code> or the <code>is-right</code> modifiers to alter the <strong>alignment</strong>.
      </p>
    </div>
    <div class="bd-example">
      {{ buttons_addons_centered }}
      {{ buttons_addons_right }}
    </div>
  </div>
  <div class="column bd-highlight-full">
    {% highlight html %}{{ buttons_addons_centered }}{{ buttons_addons_right }}{% endhighlight %}
  </div>
</div>

<div class="columns">
  <div class="column">
    <div class="content">
      <p>
        You can use any <strong>modifier</strong> class on each button to differentiate them. Make sure to add the <code>is-selected</code> modifier as well to make sure the selected button is <em>above</em> its siblings.
      </p>
    </div>
    <div class="bd-example">
      {{ buttons_addons_selected }}
    </div>
  </div>
  <div class="column bd-highlight-full">
    {% highlight html %}{{ buttons_addons_selected }}{% endhighlight %}
  </div>
</div>

<div class="message is-info">
  <div class="message-header">
    <p>Difference between <a href="{{ site.url }}/documentation/form/general/#form-group">form groups</a> and <strong>list of buttons</strong></p>
  </div>
  <div class="message-body">
    <div class="content">
      <p>
        While this list of buttons style can be achieved with either <code>field is-grouped</code> or the new <code>buttons</code> class, there are a few differences:
      </p>
      <ul>
        <li>
          <code>buttons</code> has a <strong>simpler markup</strong>
        </li>
        <li>
          <code>buttons</code> can only contain <code>button</code> elements
        </li>
        <li>
          <code>field is-grouped</code> can contain <em>any</em> type of <code>control</code> inputs
        </li>
        <li>
          <code>field is-grouped</code> can be forced to fit all controls on a <strong>single line</strong>
        </li>
        <li>
          with <code>field is-grouped</code> you can <strong>expand</strong> one of the controls
        </li>
      </ul>
      <p>
        Basically, if you only want a list of <em>buttons</em>, using <code>buttons</code> is recommended. If you need more control on the styling and the elements, use a <a href="{{ site.url }}/documentation/form/general/#form-group">form group</a>.
      </p>
    </div>
  </div>
</div>

{% include components/variables.html type='element' %}
