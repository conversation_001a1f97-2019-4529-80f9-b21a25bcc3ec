---
title: Tags
layout: documentation
doc-tab: elements
doc-subtab: tag
breadcrumb:
- home
- documentation
- elements
- elements-tag
meta:
  colors: true
  sizes: true
  variables: true
---

{% capture tag %}
<span class="tag">
  Tag label
</span>
{% endcapture %}

{% capture tags_colors %}
<span class="tag is-black">Black</span>
<span class="tag is-dark">Dark</span>
<span class="tag is-light">Light</span>
<span class="tag is-white">White</span>
<span class="tag is-primary">Primary</span>
<span class="tag is-link">Link</span>
<span class="tag is-info">Info</span>
<span class="tag is-success">Success</span>
<span class="tag is-warning">Warning</span>
<span class="tag is-danger">Danger</span>
{% endcapture %}

{% capture tags_light_colors %}
<span class="tag is-primary is-light">Primary</span>
<span class="tag is-link is-light">Link</span>
<span class="tag is-info is-light">Info</span>
<span class="tag is-success is-light">Success</span>
<span class="tag is-warning is-light">Warning</span>
<span class="tag is-danger is-light">Danger</span>
{% endcapture %}

{% capture sizes %}
<span class="tag is-link is-normal">Normal</span>
<span class="tag is-primary is-medium">Medium</span>
<span class="tag is-info is-large">Large</span>
{% endcapture %}

{% capture are_medium %}
<div class="tags are-medium">
  <span class="tag">All</span>
  <span class="tag">Medium</span>
  <span class="tag">Size</span>
</div>
{% endcapture %}

{% capture are_large %}
<div class="tags are-large">
  <span class="tag">All</span>
  <span class="tag">Large</span>
  <span class="tag">Size</span>
</div>
{% endcapture %}

{% capture are_medium_one_large %}
<div class="tags are-medium">
  <span class="tag">Medium</span>
  <span class="tag is-normal">Normal</span>
  <span class="tag">Medium</span>
  <span class="tag is-large">Large</span>
  <span class="tag">Medium</span>
</div>
{% endcapture %}

{% capture rounded %}
<span class="tag is-rounded">Rounded</span>
{% endcapture %}

{% capture delete %}
<span class="tag is-success">
  Bar
  <button class="delete is-small"></button>
</span>
<span class="tag is-warning is-medium">
  Hello
  <button class="delete is-small"></button>
</span>
<span class="tag is-danger is-large">
  World
  <button class="delete"></button>
</span>
{% endcapture %}

{% capture is_delete %}
<a class="tag is-delete"></a>
{% endcapture %}

{% capture tags %}
<div class="tags">
  <span class="tag">One</span>
  <span class="tag">Two</span>
  <span class="tag">Three</span>
</div>
{% endcapture %}

{% capture tags_multiple %}
<div class="tags">
  <span class="tag">One</span>
  <span class="tag">Two</span>
  <span class="tag">Three</span>
  <span class="tag">Four</span>
  <span class="tag">Five</span>
  <span class="tag">Six</span>
  <span class="tag">Seven</span>
  <span class="tag">Eight</span>
  <span class="tag">Nine</span>
  <span class="tag">Ten</span>
  <span class="tag">Eleven</span>
  <span class="tag">Twelve</span>
  <span class="tag">Thirteen</span>
  <span class="tag">Fourteen</span>
  <span class="tag">Fifteen</span>
  <span class="tag">Sixteen</span>
  <span class="tag">Seventeen</span>
  <span class="tag">Eighteen</span>
  <span class="tag">Nineteen</span>
  <span class="tag">Twenty</span>
</div>
{% endcapture %}

{% capture tags_addons %}
<div class="tags has-addons">
  <span class="tag">Package</span>
  <span class="tag is-primary">Bulma</span>
</div>
{% endcapture %}

{% capture tags_field_addons %}
<div class="field is-grouped is-grouped-multiline">
  <div class="control">
    <div class="tags has-addons">
      <span class="tag is-dark">npm</span>
      <span class="tag is-info">{{ site.data.meta.version }}</span>
    </div>
  </div>

  <div class="control">
    <div class="tags has-addons">
      <span class="tag is-dark">build</span>
      <span class="tag is-success">passing</span>
    </div>
  </div>

  <div class="control">
    <div class="tags has-addons">
      <span class="tag is-dark">chat</span>
      <span class="tag is-primary">on gitter</span>
    </div>
  </div>
</div>
{% endcapture %}

{% capture tags_delete_addons %}
<div class="tags has-addons">
  <span class="tag is-danger">Alex Smith</span>
  <a class="tag is-delete"></a>
</div>
{% endcapture %}

{% capture tags_blog_addons %}
<div class="field is-grouped is-grouped-multiline">
  <div class="control">
    <div class="tags has-addons">
      <a class="tag is-link">Technology</a>
      <a class="tag is-delete"></a>
    </div>
  </div>

  <div class="control">
    <div class="tags has-addons">
      <a class="tag is-link">CSS</a>
      <a class="tag is-delete"></a>
    </div>
  </div>

  <div class="control">
    <div class="tags has-addons">
      <a class="tag is-link">Flexbox</a>
      <a class="tag is-delete"></a>
    </div>
  </div>

  <div class="control">
    <div class="tags has-addons">
      <a class="tag is-link">Web Design</a>
      <a class="tag is-delete"></a>
    </div>
  </div>

  <div class="control">
    <div class="tags has-addons">
      <a class="tag is-link">Open Source</a>
      <a class="tag is-delete"></a>
    </div>
  </div>

  <div class="control">
    <div class="tags has-addons">
      <a class="tag is-link">Community</a>
      <a class="tag is-delete"></a>
    </div>
  </div>

  <div class="control">
    <div class="tags has-addons">
      <a class="tag is-link">Documentation</a>
      <a class="tag is-delete"></a>
    </div>
  </div>
</div>
{% endcapture %}

<div class="content">
  <p>
    The Bulma <strong>tag</strong> is a small but versatile element. It's very useful as a way to attach information to a block or other component. Its size makes it also easy to display in numbers, making it appropriate for long lists of items.
  </p>
</div>

<div class="columns">
  <div class="column is-4">
    By default, a <strong>tag</strong> is a 1.5rem high label.
  </div>
  <div class="column is-2">
    <span class="tag">
      Tag label
    </span>
  </div>
  <div class="column is-6">
    {% highlight html %}{{ tag }}{% endhighlight %}
  </div>
</div>

{% include elements/anchor.html name="Colors" %}

<div class="columns">
  <div class="column is-4">
    Like with buttons, there are <strong>10 different colors</strong> available.
  </div>
  <div class="column is-2">
    <p class="field">
      <span class="tag is-black">
        Black
      </span>
    </p>
    <p class="field">
      <span class="tag is-dark">
        Dark
      </span>
    </p>
    <p class="field">
      <span class="tag is-light">
        Light
      </span>
    </p>
    <p class="field">
      <span class="tag is-white">
        White
      </span>
    </p>
    <p class="field">
      <span class="tag is-primary">
        Primary
      </span>
    </p>
    <p class="field">
      <span class="tag is-link">
        Link
      </span>
    </p>
    <p class="field">
      <span class="tag is-info">
        Info
      </span>
    </p>
    <p class="field">
      <span class="tag is-success">
        Success
      </span>
    </p>
    <p class="field">
      <span class="tag is-warning">
        Warning
      </span>
    </p>
    <span class="tag is-danger">
      Danger
    </span>
  </div>
  <div class="column is-6">
    {% highlight html %}{{ tags_colors }}{% endhighlight %}
  </div>
</div>

<div class="columns">
  <div class="column is-4">
    <p>
      You can now choose the <strong>light version</strong> of a color.
    </p>
  </div>
  <div class="column is-2">
    <p class="field">
      <span class="tag is-primary is-light">
        Primary
      </span>
    </p>
    <p class="field">
      <span class="tag is-link is-light">
        Link
      </span>
    </p>
    <p class="field">
      <span class="tag is-info is-light">
        Info
      </span>
    </p>
    <p class="field">
      <span class="tag is-success is-light">
        Success
      </span>
    </p>
    <p class="field">
      <span class="tag is-warning is-light">
        Warning
      </span>
    </p>
    <span class="tag is-danger is-light">
      Danger
    </span>
  </div>
  <div class="column is-6">
    {% highlight html %}{{ tags_light_colors }}{% endhighlight %}
  </div>
</div>

{% include elements/anchor.html name="Sizes" %}

<div class="columns">
  <div class="column is-4">
    <div class="content">
      <p>
        The tag comes in <strong>3 different</strong> sizes.
      </p>
      <p>
        While the default size is the <strong>normal</strong> one, the <code>is-normal</code> modifier exists in case you need to reset the tag to its normal size.
      </p>
    </div>
  </div>
  <div class="column is-2">
    <p class="field">
      <span class="tag is-link is-normal">
        Normal
      </span>
    </p>
    <p class="field">
      <span class="tag is-primary is-medium">
        Medium
      </span>
    </p>
    <p class="field">
      <span class="tag is-info is-large">
        Large
      </span>
    </p>
  </div>
  <div class="column is-6">
    {% highlight html %}{{ sizes }}{% endhighlight %}
  </div>
</div>

<div class="content">
  <p>
    You can change the size of <strong>all</strong> tags at once:
  </p>
</div>

{% include elements/snippet.html content=are_medium %}

{% include elements/snippet.html content=are_large %}

<div class="content">
  <p>
    You can however keep the original size of a <strong>subset</strong> of tags, simply by applying one of its modifier class:
  </p>
</div>

{% include elements/snippet.html content=are_medium_one_large %}

{% include elements/anchor.html name="Modifiers" %}

<div class="columns">
  <div class="column is-4">
    You can add the <code>is-rounded</code> modifier to make a <strong>rounded</strong> tag.
  </div>
  <div class="column is-2">
    {{ rounded }}
  </div>
  <div class="column is-6">
    {% highlight html %}{{ rounded }}{% endhighlight %}
  </div>
</div>

<div class="columns">
  <div class="column is-4">
    You can add the <code>is-delete</code> modifier to turn the tag into a <strong>delete button</strong>.
  </div>
  <div class="column is-2">
    {{ is_delete }}
  </div>
  <div class="column is-6">
    {% highlight html %}{{ is_delete }}{% endhighlight %}
  </div>
</div>

{% include elements/anchor.html name="Combinations" %}

<div class="columns">
  <div class="column is-4">
    You can append a <strong>delete button</strong>.
  </div>
  <div class="column is-2">
    <p class="field">
      <span class="tag is-success">
        Bar
        <button class="delete is-small"></button>
      </span>
    </p>
    <p class="field">
      <span class="tag is-warning is-medium">
        Hello
        <button class="delete is-small"></button>
      </span>
    </p>
    <p class="field">
      <span class="tag is-danger is-large">
        World
        <button class="delete"></button>
      </span>
    </p>
  </div>
  <div class="column is-6">
    {% highlight html %}{{ delete }}{% endhighlight %}
  </div>
</div>

{% include elements/anchor.html name="List of tags" %}

<div class="columns">
  <div class="column">
    <div class="content">
      <p>
        You can now create a <strong>list of tags</strong> with the <code>tags</code> container.
      </p>
    </div>
    <div class="bd-example">
      {{ tags }}
    </div>
  </div>
  <div class="column">
    {% highlight html %}{{ tags }}{% endhighlight %}
  </div>
</div>

<div class="columns">
  <div class="column">
    <div class="content">
      <p>
        If the list is <strong>very long</strong>, it will automatically wrap on <strong>multiple lines</strong>, while keeping all tags <strong>evenly spaced</strong>.
      </p>
    </div>
    <div class="bd-example">
      {{ tags_multiple }}
    </div>
  </div>
  <div class="column bd-highlight-full">
    {% highlight html %}{{ tags_multiple }}{% endhighlight %}
  </div>
</div>

{% include elements/anchor.html name="Tag addons" %}

<div class="columns">
  <div class="column">
    <div class="content">
      <p>
        You can <strong>attach tags together</strong> with the <code>has-addons</code> modifier.
      </p>
    </div>
    <div class="bd-example">
      {{ tags_addons }}
    </div>
  </div>
  <div class="column bd-highlight-full">
    {% highlight html %}{{ tags_addons }}{% endhighlight %}
  </div>
</div>

<div class="columns">
  <div class="column">
    <div class="content">
      <p>
        You can attach a <strong>text</strong> tag with a <strong>delete</strong> tag together.
      </p>
    </div>
    <div class="bd-example">
      {{ tags_delete_addons }}
    </div>
  </div>
  <div class="column bd-highlight-full">
    {% highlight html %}{{ tags_delete_addons }}{% endhighlight %}
  </div>
</div>

<div class="columns">
  <div class="column">
    <div class="content">
      <p>
        If you want to attach <code>tags</code> containers <strong>together</strong>, simply use the <code>field</code> element with the <code>is-grouped</code> and <code>is-grouped-multiline</code> modifiers.
      </p>
    </div>
    <div class="bd-example">
      {{ tags_field_addons }}
    </div>
  </div>
  <div class="column bd-highlight-full">
    {% highlight html %}{{ tags_field_addons }}{% endhighlight %}
  </div>
</div>

<div class="columns">
  <div class="column">
    <div class="content">
      <p>
        This can be useful for a long list of <strong>blog tags</strong>.
      </p>
    </div>
    <div class="bd-example">
      {{ tags_blog_addons }}
    </div>
  </div>
  <div class="column">
    {% highlight html %}{{ tags_blog_addons }}{% endhighlight %}
  </div>
</div>

{% include components/variables.html type='element' %}
