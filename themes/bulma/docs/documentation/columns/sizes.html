---
title: Column sizes
layout: documentation
doc-tab: columns
doc-subtab: sizes
breadcrumb:
- home
- documentation
- columns
- columns-sizes
---

{% capture columns_sizes %}
<div class="columns">
  <div class="column is-four-fifths">is-four-fifths</div>
  <div class="column">Auto</div>
  <div class="column">Auto</div>
</div>

<div class="columns">
  <div class="column is-three-quarters">is-three-quarters</div>
  <div class="column">Auto</div>
  <div class="column">Auto</div>
</div>

<div class="columns">
  <div class="column is-two-thirds">is-two-thirds</div>
  <div class="column">Auto</div>
  <div class="column">Auto</div>
</div>

<div class="columns">
  <div class="column is-three-fifths">is-three-fifths</div>
  <div class="column">Auto</div>
  <div class="column">Auto</div>
</div>

<div class="columns">
  <div class="column is-half">is-half</div>
  <div class="column">Auto</div>
  <div class="column">Auto</div>
</div>

<div class="columns">
  <div class="column is-two-fifths">is-two-fifths</div>
  <div class="column">Auto</div>
  <div class="column">Auto</div>
</div>

<div class="columns">
  <div class="column is-one-third">is-one-third</div>
  <div class="column">Auto</div>
  <div class="column">Auto</div>
</div>

<div class="columns">
  <div class="column is-one-quarter">is-one-quarter</div>
  <div class="column">Auto</div>
  <div class="column">Auto</div>
</div>

<div class="columns">
  <div class="column is-one-fifth">is-one-fifth</div>
  <div class="column">Auto</div>
  <div class="column">Auto</div>
</div>
{% endcapture %}

{% capture columns_offset %}
<div class="columns is-mobile">
  <div class="column is-half is-offset-one-quarter"></div>
</div>

<div class="columns is-mobile">
  <div class="column is-three-fifths is-offset-one-fifth"></div>
</div>

<div class="columns is-mobile">
  <div class="column is-4 is-offset-8"></div>
</div>

<div class="columns is-mobile">
  <div class="column is-11 is-offset-1"></div>
</div>
{% endcapture %}

{% capture columns_narrow %}
<div class="columns">
  <div class="column is-narrow">
    <div class="box" style="width: 200px;">
      <p class="title is-5">Narrow column</p>
      <p class="subtitle">This column is only 200px wide.</p>
    </div>
  </div>
  <div class="column">
    <div class="box">
      <p class="title is-5">Flexible column</p>
      <p class="subtitle">This column will take up the remaining space available.</p>
    </div>
  </div>
</div>
{% endcapture %}

<div class="content">
  <p>If you want to change the <strong>size</strong> of a single column, you can use one of the following classes:</p>
  <ul>
    <li>
      <code>is-three-quarters</code>
    </li>
    <li>
      <code>is-two-thirds</code>
    </li>
    <li>
      <code>is-half</code>
    </li>
    <li>
      <code>is-one-third</code>
    </li>
    <li>
      <code>is-one-quarter</code>
    </li>
    <li>
      <code>is-full</code>
    </li>
  </ul>
  <p>The <em>other</em> columns will fill up the <strong>remaining</strong> space automatically.</p>
</div>

<div class="content">
  <p>You can now use the following multiples of <code>20%</code> as well:</p>
  <ul>
    <li>
      <code>is-four-fifths</code>
    </li>
    <li>
      <code>is-three-fifths</code>
    </li>
    <li>
      <code>is-two-fifths</code>
    </li>
    <li>
      <code>is-one-fifth</code>
    </li>
  </ul>
</div>

<div class="columns">
  <div class="column is-full">
    <p class="bd-notification is-primary">
      <code class="html">is-full</code>
    </p>
  </div>
</div>

<div class="columns">
  <div class="column is-four-fifths">
    <p class="bd-notification is-primary">
      <code class="html">is-four-fifths</code>
    </p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj">Auto</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij">Auto</p>
  </div>
</div>

<div class="columns">
  <div class="column is-three-quarters">
    <p class="bd-notification is-primary">
      <code class="html">is-three-quarters</code>
    </p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj">Auto</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij">Auto</p>
  </div>
</div>

<div class="columns">
  <div class="column is-two-thirds">
    <p class="bd-notification is-primary">
      <code class="html">is-two-thirds</code>
    </p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj">Auto</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij">Auto</p>
  </div>
</div>

<div class="columns">
  <div class="column is-three-fifths">
    <p class="bd-notification is-primary">
      <code class="html">is-three-fifths</code>
    </p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj">Auto</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij">Auto</p>
  </div>
</div>

<div class="columns">
  <div class="column is-half">
    <p class="bd-notification is-primary">
      <code class="html">is-half</code>
    </p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj">Auto</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij">Auto</p>
  </div>
</div>

<div class="columns">
  <div class="column is-two-fifths">
    <p class="bd-notification is-primary">
      <code class="html">is-two-fifths</code>
    </p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj">Auto</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij">Auto</p>
  </div>
</div>

<div class="columns">
  <div class="column is-one-third">
    <p class="bd-notification is-primary">
      <code class="html">is-one-third</code>
    </p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj">Auto</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij">Auto</p>
  </div>
</div>

<div class="columns">
  <div class="column is-one-quarter">
    <p class="bd-notification is-primary">
      <code class="html">is-one-quarter</code>
    </p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj">Auto</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij">Auto</p>
  </div>
</div>

<div class="columns">
  <div class="column is-one-fifth">
    <p class="bd-notification is-primary">
      <code class="html">is-one-fifth</code>
    </p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj">Auto</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij">Auto</p>
  </div>
</div>

<div class="bd-highlight-full">
  {% highlight html %}{{ columns_sizes }}{% endhighlight %}
</div>

{% include elements/anchor.html name="12 columns system" %}

<div class="content">
  <p>As the grid can be divided into <strong>12</strong> columns, there are size classes for each division:</p>
  <ul>
    <li><code>is-1</code></li>
    <li><code>is-2</code></li>
    <li><code>is-3</code></li>
    <li><code>is-4</code></li>
    <li><code>is-5</code></li>
    <li><code>is-6</code></li>
    <li><code>is-7</code></li>
    <li><code>is-8</code></li>
    <li><code>is-9</code></li>
    <li><code>is-10</code></li>
    <li><code>is-11</code></li>
    <li><code>is-12</code></li>
  </ul>
</div>

<div class="message is-info">
  <p class="message-header">Naming convention</p>
  <p class="message-body">Each modifier class is named after <strong>how many columns you want out of 12</strong>. So if you want 7 columns out of 12, use <code>is-7</code>.</p>
</div>
<div class="columns">
  <div class="column is-1">
    <p class="bd-notification is-primary"><code>is-1</code></p>
  </div>
  <div class="column">
    <p class="bd-notification has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification has-text-centered">1</p>
  </div>
</div>
<div class="columns">
  <div class="column is-2">
    <p class="bd-notification is-primary"><code>is-2</code></p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij has-text-centered">1</p>
  </div>
</div>
<div class="columns">
  <div class="column is-3">
    <p class="bd-notification is-primary"><code>is-3</code></p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
</div>
<div class="columns">
  <div class="column is-4">
    <p class="bd-notification is-primary"><code>is-4</code></p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij has-text-centered">1</p>
  </div>
</div>
<div class="columns">
  <div class="column is-5">
    <p class="bd-notification is-primary"><code>is-5</code></p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
</div>
<div class="columns">
  <div class="column is-6">
    <p class="bd-notification is-primary"><code>is-6</code></p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij has-text-centered">1</p>
  </div>
</div>
<div class="columns">
  <div class="column is-7">
    <p class="bd-notification is-primary"><code>is-7</code></p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
</div>
<div class="columns">
  <div class="column is-8">
    <p class="bd-notification is-primary"><code>is-8</code></p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij has-text-centered">1</p>
  </div>
</div>
<div class="columns">
  <div class="column is-9">
    <p class="bd-notification is-primary"><code>is-9</code></p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
</div>
<div class="columns">
  <div class="column is-10">
    <p class="bd-notification is-primary"><code>is-10</code></p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-wajofij has-text-centered">1</p>
  </div>
</div>
<div class="columns">
  <div class="column is-11">
    <p class="bd-notification is-primary"><code>is-11</code></p>
  </div>
  <div class="column">
    <p class="bd-notification is-fojeisj has-text-centered">1</p>
  </div>
</div>
<div class="columns">
  <div class="column is-12">
    <p class="bd-notification is-primary"><code>is-12</code></p>
  </div>
</div>

{% include elements/anchor.html name="Offset" %}

<div class="content">
  <p>
    While you can use <em>empty columns</em> (like <code>&lt;div class="column"&gt;&lt;/div&gt;</code>) to create horizontal space around <code>.column</code> elements, you can also use <strong>offset</strong> modifiers like <code>.is-offset-x</code>:
  </p>
</div>

<div class="columns is-mobile">
  <div class="column is-half is-offset-one-quarter">
    <p class="bd-notification is-primary">
      <code class="html">is-half</code><br>
      <code class="html">is-offset-one-quarter</code>
    </p>
  </div>
</div>

<div class="columns is-mobile">
  <div class="column is-three-fifths is-offset-one-fifth">
    <p class="bd-notification is-primary">
      <code class="html">is-three-fifths</code><br>
      <code class="html">is-offset-one-fifth</code>
    </p>
  </div>
</div>

<div class="columns is-mobile">
  <div class="column is-4 is-offset-8">
    <p class="bd-notification is-primary">
      <code class="html">is-4</code><br>
      <code class="html">is-offset-8</code>
    </p>
  </div>
</div>

<div class="columns is-mobile">
  <div class="column is-11 is-offset-1">
    <p class="bd-notification is-primary">
      <code class="html">is-11</code><br>
      <code class="html">is-offset-1</code>
    </p>
  </div>
</div>

<div class="bd-highlight-full">
  {% highlight html %}{{ columns_offset }}{% endhighlight %}
</div>

{% include elements/anchor.html name="Narrow column" %}

<div class="content">
  <p>If you want a column to only take the <strong>space it needs</strong>, use the <code>is-narrow</code> modifier. The other column(s) will fill up the remaining space.</p>
</div>

<div class="columns">
  <div class="column is-narrow">
    <div class="box" style="width: 200px;">
      <p class="title is-5">Narrow column</p>
      <p class="subtitle">This column is only 200px wide.</p>
    </div>
  </div>
  <div class="column">
    <div class="box">
      <p class="title is-5">Flexible column</p>
      <p class="subtitle">This column will take up the remaining space available.</p>
    </div>
  </div>
</div>

<div class="bd-highlight-full">
  {% highlight html %}{{ columns_narrow }}{% endhighlight %}
</div>

<div class="content">
  <p>As for the size modifiers, you can have narrow columns for different <strong>breakpoints</strong>:</p>
  <ul>
    <li>
      <code>.is-narrow-mobile</code>
    </li>
    <li>
      <code>.is-narrow-tablet</code>
    </li>
    <li>
      <code>.is-narrow-touch</code>
    </li>
    <li>
      <code>.is-narrow-desktop</code>
    </li>
    <li>
      <code>.is-narrow-widescreen</code>
    </li>
    <li>
      <code>.is-narrow-fullhd</code>
    </li>
  </ul>
</div>
