---
title: Columns powered by Flexbox
layout: documentation
doc-tab: columns
doc-subtab: basics
breadcrumb:
- home
- documentation
- columns
- columns-basics
---

{% capture columns %}
<div class="columns">
  <div class="column">
    First column
  </div>
  <div class="column">
    Second column
  </div>
  <div class="column">
    Third column
  </div>
  <div class="column">
    Fourth column
  </div>
</div>
{% endcapture %}

<div class="content">
  <p>Building a <strong>columns layout</strong> with Bulma is very simple:</p>
  <ol>
    <li>Add a <code>columns</code> container</li>
    <li>Add as many <code>column</code> elements as you want</li>
  </ol>
  <p>Each column will have an <strong>equal width</strong>, no matter the number of columns.</p>
</div>

<div class="columns">
  <div class="column">
    <p class="bd-notification is-primary">First column</p>
  </div>
  <div class="column">
    <p class="bd-notification is-primary">Second column</p>
  </div>
  <div class="column">
    <p class="bd-notification is-primary">Third column</p>
  </div>
  <div class="column">
    <p class="bd-notification is-primary">Fourth column</p>
  </div>
</div>

<div class="bd-highlight-full">
  {% highlight html %}{{ columns }}{% endhighlight %}
</div>
