---
title: Columns responsiveness
layout: documentation
doc-tab: columns
doc-subtab: responsiveness
breadcrumb:
- home
- documentation
- columns
- columns-responsiveness
---

{% capture columns_mobile %}
<div class="columns is-mobile">
  <div class="column">1</div>
  <div class="column">2</div>
  <div class="column">3</div>
  <div class="column">4</div>
</div>
{% endcapture %}

{% capture columns_desktop %}
<div class="columns is-desktop">
  <div class="column">1</div>
  <div class="column">2</div>
  <div class="column">3</div>
  <div class="column">4</div>
</div>
{% endcapture %}

{% capture columns_multiple_breakpoints %}
<div class="columns is-mobile">
  <div class="column is-three-quarters-mobile is-two-thirds-tablet is-half-desktop is-one-third-widescreen is-one-quarter-fullhd">
    <code>is-three-quarters-mobile</code><br>
    <code>is-two-thirds-tablet</code><br>
    <code>is-half-desktop</code><br>
    <code>is-one-third-widescreen</code><br>
    <code>is-one-quarter-fullhd</code>
  </div>
  <div class="column">2</div>
  <div class="column">3</div>
  <div class="column">4</div>
  <div class="column">5</div>
</div>
{% endcapture %}

{% include elements/anchor.html name="Mobile columns" %}

<div class="content">
  <p>
    By default, columns are only activated from <strong>tablet</strong> onwards. This means columns are stacked on top of each other on <strong>mobile</strong>.
    <br>
    If you want columns to work on <strong>mobile too</strong>, just add the <code>is-mobile</code> modifier on the <code>columns</code> container:
  </p>
</div>

<div class="columns is-mobile">
  <div class="column">
    <p class="bd-notification is-primary">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-primary">2</p>
  </div>
  <div class="column">
    <p class="bd-notification is-primary">3</p>
  </div>
  <div class="column">
    <p class="bd-notification is-primary">4</p>
  </div>
</div>

{% highlight html %}{{ columns_mobile }}{% endhighlight %}

<div class="message is-info">
  <p class="message-header">
    Resize
  </p>
  <p class="message-body">
    If you want to see the difference, resize your browser and see <em>when</em> the columns are stacked and when they are horizontally distributed.
  </p>
</div>

<div class="content">
  <p>
    If you <em>only</em> want columns on <strong>desktop</strong> upwards, just use the <code>is-desktop</code> modifier on the <code>columns</code> container:
  </p>
</div>

<div class="columns is-desktop">
  <div class="column">
    <p class="bd-notification is-primary">1</p>
  </div>
  <div class="column">
    <p class="bd-notification is-primary">2</p>
  </div>
  <div class="column">
    <p class="bd-notification is-primary">3</p>
  </div>
  <div class="column">
    <p class="bd-notification is-primary">4</p>
  </div>
</div>

{% highlight html %}{{ columns_desktop }}{% endhighlight %}

{% include elements/anchor.html name="Different column sizes per breakpoint" %}

<div class="content">
  <p>You can define a <strong>column size</strong> for <em>each</em> viewport size: mobile, tablet, and desktop.</p>
</div>

<div class="columns is-mobile">
  <div class="column is-three-quarters-mobile is-two-thirds-tablet is-half-desktop is-one-third-widescreen is-one-quarter-fullhd">
    <p class="bd-notification is-primary">
      <code>is-three-quarters-mobile</code><br>
      <code>is-two-thirds-tablet</code><br>
      <code>is-half-desktop</code><br>
      <code>is-one-third-widescreen</code><br>
      <code>is-one-quarter-fullhd</code><br>
    </p>
  </div>
  <div class="column">
    <p class="bd-notification is-primary">2</p>
  </div>
  <div class="column">
    <p class="bd-notification is-primary">3</p>
  </div>
  <div class="column">
    <p class="bd-notification is-primary">4</p>
  </div>
  <div class="column">
    <p class="bd-notification is-primary">5</p>
  </div>
</div>

<div class="message is-info">
  <p class="message-header">Resize</p>
  <p class="message-body">If you want to see these classes in action, resize your browser window and see how the same column varies in width at each breakpoint.</p>
</div>

{% highlight html %}{{ columns_multiple_breakpoints }}{% endhighlight %}
