---
title: Column options
layout: documentation
doc-tab: columns
doc-subtab: options
breadcrumb:
- home
- documentation
- columns
- columns-options
---

{% capture columns_vcentered %}
<div class="columns is-vcentered">
  <div class="column is-8">
    <p class="bd-notification is-primary">First column</p>
  </div>
  <div class="column">
    <p class="bd-notification is-primary">Second column with more content. This is so you can see the vertical alignment.</p>
  </div>
</div>
{% endcapture %}

{% capture columns_multiline %}
<div class="columns is-multiline is-mobile">
  <div class="column is-one-quarter">
    <code>is-one-quarter</code>
  </div>
  <div class="column is-one-quarter">
    <code>is-one-quarter</code>
  </div>
  <div class="column is-one-quarter">
    <code>is-one-quarter</code>
  </div>
  <div class="column is-one-quarter">
    <code>is-one-quarter</code>
  </div>
  <div class="column is-half">
    <code>is-half</code>
  </div>
  <div class="column is-one-quarter">
    <code>is-one-quarter</code>
  </div>
  <div class="column is-one-quarter">
    <code>is-one-quarter</code>
  </div>
  <div class="column is-one-quarter">
    <code>is-one-quarter</code>
  </div>
  <div class="column">
    Auto
  </div>
</div>
{% endcapture %}

{% capture columns_mobile_centered %}
<div class="columns is-mobile is-centered">
  <div class="column is-half">
    <p class="bd-notification is-primary">
      <code class="html">is-half</code><br>
    </p>
  </div>
</div>
{% endcapture %}

{% capture columns_mobile_multiline_centered %}
<div class="columns is-mobile is-multiline is-centered">
  <div class="column is-narrow">
    <p class="bd-notification is-primary">
      <code class="html">is-narrow</code><br>
      First Column
    </p>
  </div>
  <div class="column is-narrow">
    <p class="bd-notification is-primary">
      <code class="html">is-narrow</code><br>
      Our Second Column
    </p>
  </div>
  <div class="column is-narrow">
    <p class="bd-notification is-primary">
      <code class="html">is-narrow</code><br>
      Third Column
    </p>
  </div>
  <div class="column is-narrow">
    <p class="bd-notification is-primary">
      <code class="html">is-narrow</code><br>
      The Fourth Column
    </p>
  </div>
  <div class="column is-narrow">
    <p class="bd-notification is-primary">
      <code class="html">is-narrow</code><br>
      Fifth Column
    </p>
  </div>
</div>
{% endcapture %}

{% include elements/anchor.html name="Vertical alignment" %}

<div class="content">
  <p>To align your columns vertically, add the <code>is-vcentered</code> modifier to the <code>columns</code> container.</p>
</div>

<div class="columns is-vcentered">
  <div class="column is-8">
    <p class="bd-notification is-primary">First column</p>
  </div>
  <div class="column">
    <p class="bd-notification is-primary">Second column with more content. This is so you can see the vertical alignment.</p>
  </div>
</div>

{% highlight html %}{{ columns_vcentered }}{% endhighlight %}

{% include elements/anchor.html name="Multiline" %}

<div class="content">
  <p>Whenever you want to start a new line, you can close a <code>columns</code> container and start a new one. But you can also add the <code>is-multiline</code> modifier and add <strong>more</strong> column elements than would fit in a single row.</p>
</div>

<div class="columns is-multiline is-mobile">
  <div class="column is-one-quarter">
    <p class="bd-notification is-primary"><code>is-one-quarter</code></p>
  </div>
  <div class="column is-one-quarter">
    <p class="bd-notification is-primary"><code>is-one-quarter</code></p>
  </div>
  <div class="column is-one-quarter">
    <p class="bd-notification is-primary"><code>is-one-quarter</code></p>
  </div>
  <div class="column is-one-quarter">
    <p class="bd-notification is-primary"><code>is-one-quarter</code></p>
  </div>
  <div class="column is-half">
    <p class="bd-notification is-primary"><code>is-half</code></p>
  </div>
  <div class="column is-one-quarter">
    <p class="bd-notification is-primary"><code>is-one-quarter</code></p>
  </div>
  <div class="column is-one-quarter">
    <p class="bd-notification is-primary"><code>is-one-quarter</code></p>
  </div>
  <div class="column is-one-quarter">
    <p class="bd-notification is-primary"><code>is-one-quarter</code></p>
  </div>
  <div class="column">
    <p class="bd-notification is-primary">Auto</p>
  </div>
</div>

{% highlight html %}{{ columns_multiline }}{% endhighlight %}

{% include elements/anchor.html name="Centering columns" %}

<div class="content">
  <p>
    While you can use <em>empty columns</em> (like <code>&lt;div class="column"&gt;&lt;/div&gt;</code>) to create horizontal space around <code>.column</code> elements, you can also use <code>.is-centered</code> on the parent <code>.columns</code> element:
  </p>
</div>

<div class="columns is-mobile is-centered">
  <div class="column is-half">
    <p class="bd-notification is-primary">
      <code class="html">is-half</code><br>
    </p>
  </div>
</div>

{% highlight html %}{{ columns_mobile_centered }}{% endhighlight %}

<div class="content">
  <p>
    Use with <code>.is-multiline</code> to create a flexible, centered list (try resizing to see centering in different viewport sizes):
  </p>
</div>

<div class="columns is-mobile is-multiline is-centered">
  <div class="column is-narrow">
    <p class="bd-notification is-primary" style="padding: 1.25rem 1.5rem;">
      <code class="html">is-narrow</code><br>
      First Column
    </p>
  </div>
  <div class="column is-narrow">
    <p class="bd-notification is-primary" style="padding: 1.25rem 1.5rem;">
      <code class="html">is-narrow</code><br>
      Our Second Column
    </p>
  </div>
  <div class="column is-narrow">
    <p class="bd-notification is-primary" style="padding: 1.25rem 1.5rem;">
      <code class="html">is-narrow</code><br>
      Third Column
    </p>
  </div>
  <div class="column is-narrow">
    <p class="bd-notification is-primary" style="padding: 1.25rem 1.5rem;">
      <code class="html">is-narrow</code><br>
      The Fourth Column
    </p>
  </div>
  <div class="column is-narrow">
    <p class="bd-notification is-primary" style="padding: 1.25rem 1.5rem;">
      <code class="html">is-narrow</code><br>
      Fifth Column
    </p>
  </div>
</div>

{% highlight html %}{{ columns_mobile_multiline_centered }}{% endhighlight %}
