{"by_name": {"$content-heading-color": {"name": "$content-heading-color", "value": "$text-strong", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$content-heading-weight": {"name": "$content-heading-weight", "value": "$weight-semibold", "type": "variable", "computed_type": "font-weight", "computed_value": "600"}, "$content-heading-line-height": {"name": "$content-heading-line-height", "value": "1.125", "type": "unitless"}, "$content-block-margin-bottom": {"name": "$content-block-margin-bottom", "value": "1em", "type": "size"}, "$content-blockquote-background-color": {"name": "$content-blockquote-background-color", "value": "$background", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 96%)"}, "$content-blockquote-border-left": {"name": "$content-blockquote-border-left", "value": "5px solid $border", "type": "size"}, "$content-blockquote-padding": {"name": "$content-blockquote-padding", "value": "1.25em 1.5em", "type": "size"}, "$content-pre-padding": {"name": "$content-pre-padding", "value": "1.25em 1.5em", "type": "size"}, "$content-table-cell-border": {"name": "$content-table-cell-border", "value": "1px solid $border", "type": "size"}, "$content-table-cell-border-width": {"name": "$content-table-cell-border-width", "value": "0 0 1px", "type": "size"}, "$content-table-cell-padding": {"name": "$content-table-cell-padding", "value": "0.5em 0.75em", "type": "size"}, "$content-table-cell-heading-color": {"name": "$content-table-cell-heading-color", "value": "$text-strong", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$content-table-head-cell-border-width": {"name": "$content-table-head-cell-border-width", "value": "0 0 2px", "type": "size"}, "$content-table-head-cell-color": {"name": "$content-table-head-cell-color", "value": "$text-strong", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$content-table-body-last-row-cell-border-bottom-width": {"name": "$content-table-body-last-row-cell-border-bottom-width", "value": "0", "type": "string"}, "$content-table-foot-cell-border-width": {"name": "$content-table-foot-cell-border-width", "value": "2px 0 0", "type": "size"}, "$content-table-foot-cell-color": {"name": "$content-table-foot-cell-color", "value": "$text-strong", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}}, "list": ["$content-heading-color", "$content-heading-weight", "$content-heading-line-height", "$content-block-margin-bottom", "$content-blockquote-background-color", "$content-blockquote-border-left", "$content-blockquote-padding", "$content-pre-padding", "$content-table-cell-border", "$content-table-cell-border-width", "$content-table-cell-padding", "$content-table-cell-heading-color", "$content-table-head-cell-border-width", "$content-table-head-cell-color", "$content-table-body-last-row-cell-border-bottom-width", "$content-table-foot-cell-border-width", "$content-table-foot-cell-color"], "file_path": "elements/content.sass"}