{"by_name": {"$button-color": {"name": "$button-color", "value": "$text-strong", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$button-background-color": {"name": "$button-background-color", "value": "$scheme-main", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 100%)"}, "$button-family": {"name": "$button-family", "value": "false", "type": "boolean"}, "$button-border-color": {"name": "$button-border-color", "value": "$border", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 86%)"}, "$button-border-width": {"name": "$button-border-width", "value": "$control-border-width", "type": "variable"}, "$button-padding-vertical": {"name": "$button-padding-vertical", "value": "calc(0.5em - #{$button-border-width})", "type": "size"}, "$button-padding-horizontal": {"name": "$button-padding-horizontal", "value": "1em", "type": "size"}, "$button-hover-color": {"name": "$button-hover-color", "value": "$link-hover", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$button-hover-border-color": {"name": "$button-hover-border-color", "value": "$link-hover-border", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 71%)"}, "$button-focus-color": {"name": "$button-focus-color", "value": "$link-focus", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$button-focus-border-color": {"name": "$button-focus-border-color", "value": "$link-focus-border", "type": "variable", "computed_type": "color", "computed_value": "hsl(229, 53%,  53%)"}, "$button-focus-box-shadow-size": {"name": "$button-focus-box-shadow-size", "value": "0 0 0 0.125em", "type": "size"}, "$button-focus-box-shadow-color": {"name": "$button-focus-box-shadow-color", "value": "bulmaRgba($link, 0.25)", "type": "compound"}, "$button-active-color": {"name": "$button-active-color", "value": "$link-active", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$button-active-border-color": {"name": "$button-active-border-color", "value": "$link-active-border", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 29%)"}, "$button-text-color": {"name": "$button-text-color", "value": "$text", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 29%)"}, "$button-text-decoration": {"name": "$button-text-decoration", "value": "underline", "type": "string"}, "$button-text-hover-background-color": {"name": "$button-text-hover-background-color", "value": "$background", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 96%)"}, "$button-text-hover-color": {"name": "$button-text-hover-color", "value": "$text-strong", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$button-ghost-background": {"name": "$button-ghost-background", "value": "none", "type": "string"}, "$button-ghost-border-color": {"name": "$button-ghost-border-color", "value": "transparent", "type": "string"}, "$button-ghost-color": {"name": "$button-ghost-color", "value": "$link", "type": "variable", "computed_type": "color", "computed_value": "hsl(229, 53%,  53%)"}, "$button-ghost-decoration": {"name": "$button-ghost-decoration", "value": "none", "type": "string"}, "$button-ghost-hover-color": {"name": "$button-ghost-hover-color", "value": "$link", "type": "variable", "computed_type": "color", "computed_value": "hsl(229, 53%,  53%)"}, "$button-ghost-hover-decoration": {"name": "$button-ghost-hover-decoration", "value": "underline", "type": "string"}, "$button-disabled-background-color": {"name": "$button-disabled-background-color", "value": "$scheme-main", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 100%)"}, "$button-disabled-border-color": {"name": "$button-disabled-border-color", "value": "$border", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 86%)"}, "$button-disabled-shadow": {"name": "$button-disabled-shadow", "value": "none", "type": "shadow"}, "$button-disabled-opacity": {"name": "$button-disabled-opacity", "value": "0.5", "type": "unitless"}, "$button-static-color": {"name": "$button-static-color", "value": "$text-light", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 48%)"}, "$button-static-background-color": {"name": "$button-static-background-color", "value": "$scheme-main-ter", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 96%)"}, "$button-static-border-color": {"name": "$button-static-border-color", "value": "$border", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 86%)"}, "$button-colors": {"name": "$button-colors", "value": "$colors", "type": "variable", "computed_type": "map", "computed_value": "Bulma colors"}, "$button-responsive-sizes": {"name": "$button-responsive-sizes", "value": "(\"mobile\": (\"small\": ($size-small * 0.75), \"normal\": ($size-small * 0.875), \"medium\": $size-small, \"large\": $size-normal), \"tablet-only\": (\"small\": ($size-small * 0.875), \"normal\": ($size-small), \"medium\": $size-normal, \"large\": $size-medium))", "type": "compound"}}, "list": ["$button-color", "$button-background-color", "$button-family", "$button-border-color", "$button-border-width", "$button-padding-vertical", "$button-padding-horizontal", "$button-hover-color", "$button-hover-border-color", "$button-focus-color", "$button-focus-border-color", "$button-focus-box-shadow-size", "$button-focus-box-shadow-color", "$button-active-color", "$button-active-border-color", "$button-text-color", "$button-text-decoration", "$button-text-hover-background-color", "$button-text-hover-color", "$button-ghost-background", "$button-ghost-border-color", "$button-ghost-color", "$button-ghost-decoration", "$button-ghost-hover-color", "$button-ghost-hover-decoration", "$button-disabled-background-color", "$button-disabled-border-color", "$button-disabled-shadow", "$button-disabled-opacity", "$button-static-color", "$button-static-background-color", "$button-static-border-color", "$button-colors", "$button-responsive-sizes"], "file_path": "elements/button.sass"}