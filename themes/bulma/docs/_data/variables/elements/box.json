{"by_name": {"$box-color": {"name": "$box-color", "value": "$text", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 29%)"}, "$box-background-color": {"name": "$box-background-color", "value": "$scheme-main", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 100%)"}, "$box-radius": {"name": "$box-radius", "value": "$radius-large", "type": "variable", "computed_type": "size", "computed_value": "6px"}, "$box-shadow": {"name": "$box-shadow", "value": "$shadow", "type": "variable", "computed_type": "shadow", "computed_value": "0 0.5em 1em -0.125em rgba($scheme-invert, 0.1), 0 0px 0 1px rgba($scheme-invert, 0.02)"}, "$box-padding": {"name": "$box-padding", "value": "1.25rem", "type": "size"}, "$box-link-hover-shadow": {"name": "$box-link-hover-shadow", "value": "0 0.5em 1em -0.125em rgba($scheme-invert, 0.1), 0 0 0 1px $link", "type": "shadow"}, "$box-link-active-shadow": {"name": "$box-link-active-shadow", "value": "inset 0 1px 2px rgba($scheme-invert, 0.2), 0 0 0 1px $link", "type": "shadow"}}, "list": ["$box-color", "$box-background-color", "$box-radius", "$box-shadow", "$box-padding", "$box-link-hover-shadow", "$box-link-active-shadow"], "file_path": "elements/box.sass"}