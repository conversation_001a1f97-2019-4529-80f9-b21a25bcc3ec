{"by_name": {"$table-color": {"name": "$table-color", "value": "$text-strong", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$table-background-color": {"name": "$table-background-color", "value": "$scheme-main", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 100%)"}, "$table-cell-border": {"name": "$table-cell-border", "value": "1px solid $border", "type": "size"}, "$table-cell-border-width": {"name": "$table-cell-border-width", "value": "0 0 1px", "type": "size"}, "$table-cell-padding": {"name": "$table-cell-padding", "value": "0.5em 0.75em", "type": "size"}, "$table-cell-heading-color": {"name": "$table-cell-heading-color", "value": "$text-strong", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$table-cell-text-align": {"name": "$table-cell-text-align", "value": "left", "type": "string"}, "$table-head-cell-border-width": {"name": "$table-head-cell-border-width", "value": "0 0 2px", "type": "size"}, "$table-head-cell-color": {"name": "$table-head-cell-color", "value": "$text-strong", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$table-foot-cell-border-width": {"name": "$table-foot-cell-border-width", "value": "2px 0 0", "type": "size"}, "$table-foot-cell-color": {"name": "$table-foot-cell-color", "value": "$text-strong", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$table-head-background-color": {"name": "$table-head-background-color", "value": "transparent", "type": "string"}, "$table-body-background-color": {"name": "$table-body-background-color", "value": "transparent", "type": "string"}, "$table-foot-background-color": {"name": "$table-foot-background-color", "value": "transparent", "type": "string"}, "$table-row-hover-background-color": {"name": "$table-row-hover-background-color", "value": "$scheme-main-bis", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 98%)"}, "$table-row-active-background-color": {"name": "$table-row-active-background-color", "value": "$primary", "type": "variable", "computed_type": "color", "computed_value": "hsl(171, 100%, 41%)"}, "$table-row-active-color": {"name": "$table-row-active-color", "value": "$primary-invert", "type": "variable", "computed_type": "color", "computed_value": "#fff"}, "$table-striped-row-even-background-color": {"name": "$table-striped-row-even-background-color", "value": "$scheme-main-bis", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 98%)"}, "$table-striped-row-even-hover-background-color": {"name": "$table-striped-row-even-hover-background-color", "value": "$scheme-main-ter", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 96%)"}, "$table-colors": {"name": "$table-colors", "value": "$colors", "type": "variable", "computed_type": "map", "computed_value": "Bulma colors"}}, "list": ["$table-color", "$table-background-color", "$table-cell-border", "$table-cell-border-width", "$table-cell-padding", "$table-cell-heading-color", "$table-cell-text-align", "$table-head-cell-border-width", "$table-head-cell-color", "$table-foot-cell-border-width", "$table-foot-cell-color", "$table-head-background-color", "$table-body-background-color", "$table-foot-background-color", "$table-row-hover-background-color", "$table-row-active-background-color", "$table-row-active-color", "$table-striped-row-even-background-color", "$table-striped-row-even-hover-background-color", "$table-colors"], "file_path": "elements/table.sass"}