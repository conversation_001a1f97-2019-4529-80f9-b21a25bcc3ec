{"by_name": {"$title-color": {"name": "$title-color", "value": "$text-strong", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$title-family": {"name": "$title-family", "value": "false", "type": "boolean"}, "$title-size": {"name": "$title-size", "value": "$size-3", "type": "variable", "computed_type": "size", "computed_value": "2rem"}, "$title-weight": {"name": "$title-weight", "value": "$weight-semibold", "type": "variable", "computed_type": "font-weight", "computed_value": "600"}, "$title-line-height": {"name": "$title-line-height", "value": "1.125", "type": "unitless"}, "$title-strong-color": {"name": "$title-strong-color", "value": "inherit", "type": "string"}, "$title-strong-weight": {"name": "$title-strong-weight", "value": "inherit", "type": "string"}, "$title-sub-size": {"name": "$title-sub-size", "value": "0.75em", "type": "size"}, "$title-sup-size": {"name": "$title-sup-size", "value": "0.75em", "type": "size"}, "$subtitle-color": {"name": "$subtitle-color", "value": "$text", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 29%)"}, "$subtitle-family": {"name": "$subtitle-family", "value": "false", "type": "boolean"}, "$subtitle-size": {"name": "$subtitle-size", "value": "$size-5", "type": "variable", "computed_type": "size", "computed_value": "1.25rem"}, "$subtitle-weight": {"name": "$subtitle-weight", "value": "$weight-normal", "type": "variable", "computed_type": "font-weight", "computed_value": "400"}, "$subtitle-line-height": {"name": "$subtitle-line-height", "value": "1.25", "type": "unitless"}, "$subtitle-strong-color": {"name": "$subtitle-strong-color", "value": "$text-strong", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$subtitle-strong-weight": {"name": "$subtitle-strong-weight", "value": "$weight-semibold", "type": "variable", "computed_type": "font-weight", "computed_value": "600"}, "$subtitle-negative-margin": {"name": "$subtitle-negative-margin", "value": "-1.25rem", "type": "size"}}, "list": ["$title-color", "$title-family", "$title-size", "$title-weight", "$title-line-height", "$title-strong-color", "$title-strong-weight", "$title-sub-size", "$title-sup-size", "$subtitle-color", "$subtitle-family", "$subtitle-size", "$subtitle-weight", "$subtitle-line-height", "$subtitle-strong-color", "$subtitle-strong-weight", "$subtitle-negative-margin"], "file_path": "elements/title.sass"}