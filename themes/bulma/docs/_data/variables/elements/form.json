{"by_name": {"$input-color": {"name": "$input-color", "value": "$grey-darker", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$input-background-color": {"name": "$input-background-color", "value": "$white", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 100%)"}, "$input-border-color": {"name": "$input-border-color", "value": "$grey-lighter", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 86%)"}, "$input-height": {"name": "$input-height", "value": "$control-height", "type": "variable"}, "$input-shadow": {"name": "$input-shadow", "value": "inset 0 1px 2px rgba($black, 0.1)", "type": "size"}, "$input-placeholder-color": {"name": "$input-placeholder-color", "value": "rgba($input-color, 0.3)", "type": "color"}, "$input-hover-color": {"name": "$input-hover-color", "value": "$grey-darker", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$input-hover-border-color": {"name": "$input-hover-border-color", "value": "$grey-light", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 71%)"}, "$input-focus-color": {"name": "$input-focus-color", "value": "$grey-darker", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$input-focus-border-color": {"name": "$input-focus-border-color", "value": "$link", "type": "variable", "computed_type": "color", "computed_value": "hsl(217, 71%,  53%)"}, "$input-focus-box-shadow-size": {"name": "$input-focus-box-shadow-size", "value": "0 0 0 0.125em", "type": "size"}, "$input-focus-box-shadow-color": {"name": "$input-focus-box-shadow-color", "value": "rgba($link, 0.25)", "type": "color"}, "$input-disabled-color": {"name": "$input-disabled-color", "value": "$text-light", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 48%)"}, "$input-disabled-background-color": {"name": "$input-disabled-background-color", "value": "$background", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 96%)"}, "$input-disabled-border-color": {"name": "$input-disabled-border-color", "value": "$background", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 96%)"}, "$input-disabled-placeholder-color": {"name": "$input-disabled-placeholder-color", "value": "rgba($input-disabled-color, 0.3)", "type": "color"}, "$input-arrow": {"name": "$input-arrow", "value": "$link", "type": "variable", "computed_type": "color", "computed_value": "hsl(217, 71%,  53%)"}, "$input-icon-color": {"name": "$input-icon-color", "value": "$grey-lighter", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 86%)"}, "$input-icon-active-color": {"name": "$input-icon-active-color", "value": "$grey", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 48%)"}, "$input-radius": {"name": "$input-radius", "value": "$radius", "type": "variable", "computed_type": "size", "computed_value": "4px"}, "$file-border-color": {"name": "$file-border-color", "value": "$border", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 86%)"}, "$file-radius": {"name": "$file-radius", "value": "$radius", "type": "variable", "computed_type": "size", "computed_value": "4px"}, "$file-cta-background-color": {"name": "$file-cta-background-color", "value": "$white-ter", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 96%)"}, "$file-cta-color": {"name": "$file-cta-color", "value": "$grey-dark", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 29%)"}, "$file-cta-hover-color": {"name": "$file-cta-hover-color", "value": "$grey-darker", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$file-cta-active-color": {"name": "$file-cta-active-color", "value": "$grey-darker", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$file-name-border-color": {"name": "$file-name-border-color", "value": "$border", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 86%)"}, "$file-name-border-style": {"name": "$file-name-border-style", "value": "solid", "type": "string"}, "$file-name-border-width": {"name": "$file-name-border-width", "value": "1px 1px 1px 0", "type": "size"}, "$file-name-max-width": {"name": "$file-name-max-width", "value": "16em", "type": "size"}, "$label-color": {"name": "$label-color", "value": "$grey-darker", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$label-weight": {"name": "$label-weight", "value": "$weight-bold", "type": "variable", "computed_type": "font-weight", "computed_value": "700"}, "$help-size": {"name": "$help-size", "value": "$size-small", "type": "variable", "computed_type": "size", "computed_value": "0.75rem"}}, "list": ["$input-color", "$input-background-color", "$input-border-color", "$input-height", "$input-shadow", "$input-placeholder-color", "$input-hover-color", "$input-hover-border-color", "$input-focus-color", "$input-focus-border-color", "$input-focus-box-shadow-size", "$input-focus-box-shadow-color", "$input-disabled-color", "$input-disabled-background-color", "$input-disabled-border-color", "$input-disabled-placeholder-color", "$input-arrow", "$input-icon-color", "$input-icon-active-color", "$input-radius", "$file-border-color", "$file-radius", "$file-cta-background-color", "$file-cta-color", "$file-cta-hover-color", "$file-cta-active-color", "$file-name-border-color", "$file-name-border-style", "$file-name-border-width", "$file-name-max-width", "$label-color", "$label-weight", "$help-size"], "file_path": "elements/form.sass"}