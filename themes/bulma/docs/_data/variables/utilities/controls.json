{"by_name": {"$control-radius": {"name": "$control-radius", "value": "$radius", "type": "variable", "computed_type": "size", "computed_value": "4px"}, "$control-radius-small": {"name": "$control-radius-small", "value": "$radius-small", "type": "variable", "computed_type": "size", "computed_value": "2px"}, "$control-border-width": {"name": "$control-border-width", "value": "1px", "type": "size"}, "$control-height": {"name": "$control-height", "value": "2.5em", "type": "size"}, "$control-line-height": {"name": "$control-line-height", "value": "1.5", "type": "unitless"}, "$control-padding-vertical": {"name": "$control-padding-vertical", "value": "calc(0.5em - #{$control-border-width})", "type": "size"}, "$control-padding-horizontal": {"name": "$control-padding-horizontal", "value": "calc(0.75em - #{$control-border-width})", "type": "size"}}, "list": ["$control-radius", "$control-radius-small", "$control-border-width", "$control-height", "$control-line-height", "$control-padding-vertical", "$control-padding-horizontal"], "file_path": "utilities/controls.sass"}