{"by_name": {"$primary": {"name": "$primary", "value": "$turquoise", "type": "variable", "computed_type": "color", "computed_value": "hsl(171, 100%, 41%)"}, "$info": {"name": "$info", "value": "$cyan", "type": "variable", "computed_type": "color", "computed_value": "hsl(207, 61%,  53%)"}, "$success": {"name": "$success", "value": "$green", "type": "variable", "computed_type": "color", "computed_value": "hsl(153, 53%,  53%)"}, "$warning": {"name": "$warning", "value": "$yellow", "type": "variable", "computed_type": "color", "computed_value": "hsl(44,  100%, 77%)"}, "$danger": {"name": "$danger", "value": "$red", "type": "variable", "computed_type": "color", "computed_value": "hsl(348, 86%, 61%)"}, "$light": {"name": "$light", "value": "$white-ter", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 96%)"}, "$dark": {"name": "$dark", "value": "$grey-darker", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$orange-invert": {"name": "$orange-invert", "value": "findColorInvert($orange)", "type": "function", "computed_type": "color", "computed_value": "#fff"}, "$yellow-invert": {"name": "$yellow-invert", "value": "findColorInvert($yellow)", "type": "function", "computed_type": "color", "computed_value": "rgba(0, 0, 0, 0.7)"}, "$green-invert": {"name": "$green-invert", "value": "findColorInvert($green)", "type": "function", "computed_type": "color", "computed_value": "#fff"}, "$turquoise-invert": {"name": "$turquoise-invert", "value": "findColorInvert($turquoise)", "type": "function", "computed_type": "color", "computed_value": "#fff"}, "$cyan-invert": {"name": "$cyan-invert", "value": "findColorInvert($cyan)", "type": "function", "computed_type": "color", "computed_value": "#fff"}, "$blue-invert": {"name": "$blue-invert", "value": "findColorInvert($blue)", "type": "function", "computed_type": "color", "computed_value": "#fff"}, "$purple-invert": {"name": "$purple-invert", "value": "findColorInvert($purple)", "type": "function", "computed_type": "color", "computed_value": "#fff"}, "$red-invert": {"name": "$red-invert", "value": "findColorInvert($red)", "type": "function", "computed_type": "color", "computed_value": "#fff"}, "$primary-invert": {"name": "$primary-invert", "value": "findColorInvert($primary)", "type": "function", "computed_type": "color", "computed_value": "#fff"}, "$primary-light": {"name": "$primary-light", "value": "findLightColor($primary)", "type": "compound"}, "$primary-dark": {"name": "$primary-dark", "value": "findDarkColor($primary)", "type": "compound"}, "$info-invert": {"name": "$info-invert", "value": "findColorInvert($info)", "type": "function", "computed_type": "color", "computed_value": "#fff"}, "$info-light": {"name": "$info-light", "value": "findLightColor($info)", "type": "compound"}, "$info-dark": {"name": "$info-dark", "value": "findDarkColor($info)", "type": "compound"}, "$success-invert": {"name": "$success-invert", "value": "findColorInvert($success)", "type": "function", "computed_type": "color", "computed_value": "#fff"}, "$success-light": {"name": "$success-light", "value": "findLightColor($success)", "type": "compound"}, "$success-dark": {"name": "$success-dark", "value": "findDarkColor($success)", "type": "compound"}, "$warning-invert": {"name": "$warning-invert", "value": "findColorInvert($warning)", "type": "function", "computed_type": "color", "computed_value": "#fff"}, "$warning-light": {"name": "$warning-light", "value": "findLightColor($warning)", "type": "compound"}, "$warning-dark": {"name": "$warning-dark", "value": "findDarkColor($warning)", "type": "compound"}, "$danger-invert": {"name": "$danger-invert", "value": "findColorInvert($danger)", "type": "function", "computed_type": "color", "computed_value": "#fff"}, "$danger-light": {"name": "$danger-light", "value": "findLightColor($danger)", "type": "compound"}, "$danger-dark": {"name": "$danger-dark", "value": "findDarkColor($danger)", "type": "compound"}, "$light-invert": {"name": "$light-invert", "value": "findColorInvert($light)", "type": "function", "computed_type": "color", "computed_value": "#fff"}, "$dark-invert": {"name": "$dark-invert", "value": "findColorInvert($dark)", "type": "function", "computed_type": "color", "computed_value": "#fff"}, "$scheme-main": {"name": "$scheme-main", "value": "$white", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 100%)"}, "$scheme-main-bis": {"name": "$scheme-main-bis", "value": "$white-bis", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 98%)"}, "$scheme-main-ter": {"name": "$scheme-main-ter", "value": "$white-ter", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 96%)"}, "$scheme-invert": {"name": "$scheme-invert", "value": "$black", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 4%)"}, "$scheme-invert-bis": {"name": "$scheme-invert-bis", "value": "$black-bis", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 7%)"}, "$scheme-invert-ter": {"name": "$scheme-invert-ter", "value": "$black-ter", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 14%)"}, "$background": {"name": "$background", "value": "$white-ter", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 96%)"}, "$border": {"name": "$border", "value": "$grey-lighter", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 86%)"}, "$border-hover": {"name": "$border-hover", "value": "$grey-light", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 71%)"}, "$border-light": {"name": "$border-light", "value": "$grey-lightest", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 93%)"}, "$border-light-hover": {"name": "$border-light-hover", "value": "$grey-light", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 71%)"}, "$text": {"name": "$text", "value": "$grey-dark", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 29%)"}, "$text-invert": {"name": "$text-invert", "value": "findColorInvert($text)", "type": "function", "computed_type": "color", "computed_value": "#fff"}, "$text-light": {"name": "$text-light", "value": "$grey", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 48%)"}, "$text-strong": {"name": "$text-strong", "value": "$grey-darker", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$code": {"name": "$code", "value": "darken($red, 15%)", "type": "compound"}, "$code-background": {"name": "$code-background", "value": "$background", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 96%)"}, "$pre": {"name": "$pre", "value": "$text", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 29%)"}, "$pre-background": {"name": "$pre-background", "value": "$background", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 96%)"}, "$link": {"name": "$link", "value": "$blue", "type": "variable", "computed_type": "color", "computed_value": "hsl(229, 53%,  53%)"}, "$link-invert": {"name": "$link-invert", "value": "findColorInvert($link)", "type": "function", "computed_type": "color", "computed_value": "#fff"}, "$link-light": {"name": "$link-light", "value": "findLightColor($link)", "type": "compound"}, "$link-dark": {"name": "$link-dark", "value": "findDarkColor($link)", "type": "compound"}, "$link-visited": {"name": "$link-visited", "value": "$purple", "type": "variable", "computed_type": "color", "computed_value": "hsl(271, 100%, 71%)"}, "$link-hover": {"name": "$link-hover", "value": "$grey-darker", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$link-hover-border": {"name": "$link-hover-border", "value": "$grey-light", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 71%)"}, "$link-focus": {"name": "$link-focus", "value": "$grey-darker", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$link-focus-border": {"name": "$link-focus-border", "value": "$blue", "type": "variable", "computed_type": "color", "computed_value": "hsl(229, 53%,  53%)"}, "$link-active": {"name": "$link-active", "value": "$grey-darker", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$link-active-border": {"name": "$link-active-border", "value": "$grey-dark", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 29%)"}, "$family-primary": {"name": "$family-primary", "value": "$family-sans-serif", "type": "variable", "computed_type": "font-family", "computed_value": "BlinkMacSystemFont, -apple-system, \"Segoe UI\", \"Roboto\", \"Oxygen\", \"Ubuntu\", \"Cantarell\", \"Fira Sans\", \"Droid Sans\", \"Helvetica Neue\", \"Helvetica\", \"Arial\", sans-serif"}, "$family-secondary": {"name": "$family-secondary", "value": "$family-sans-serif", "type": "variable", "computed_type": "font-family", "computed_value": "BlinkMacSystemFont, -apple-system, \"Segoe UI\", \"Roboto\", \"Oxygen\", \"Ubuntu\", \"Cantarell\", \"Fira Sans\", \"Droid Sans\", \"Helvetica Neue\", \"Helvetica\", \"Arial\", sans-serif"}, "$family-code": {"name": "$family-code", "value": "$family-monospace", "type": "variable", "computed_type": "font-family", "computed_value": "monospace"}, "$size-small": {"name": "$size-small", "value": "$size-7", "type": "variable", "computed_type": "size", "computed_value": "0.75rem"}, "$size-normal": {"name": "$size-normal", "value": "$size-6", "type": "variable", "computed_type": "size", "computed_value": "1rem"}, "$size-medium": {"name": "$size-medium", "value": "$size-5", "type": "variable", "computed_type": "size", "computed_value": "1.25rem"}, "$size-large": {"name": "$size-large", "value": "$size-4", "type": "variable", "computed_type": "size", "computed_value": "1.5rem"}, "$shadow": {"name": "$shadow", "value": "0 0.5em 1em -0.125em rgba($scheme-invert, 0.1), 0 0px 0 1px rgba($scheme-invert, 0.02)", "type": "shadow"}, "$custom-colors": {"name": "$custom-colors", "value": "null", "type": "keyword"}, "$custom-shades": {"name": "$custom-shades", "value": "null", "type": "keyword"}, "$colors": {"name": "$colors", "value": "mergeColorMaps((\"white\": ($white, $black), \"black\": ($black, $white), \"light\": ($light, $light-invert), \"dark\": ($dark, $dark-invert), \"primary\": ($primary, $primary-invert, $primary-light, $primary-dark), \"link\": ($link, $link-invert, $link-light, $link-dark), \"info\": ($info, $info-invert, $info-light, $info-dark), \"success\": ($success, $success-invert, $success-light, $success-dark), \"warning\": ($warning, $warning-invert, $warning-light, $warning-dark), \"danger\": ($danger, $danger-invert, $danger-light, $danger-dark)), $custom-colors)", "type": "map"}, "$shades": {"name": "$shades", "value": "mergeColorMaps((\"black-bis\": $black-bis, \"black-ter\": $black-ter, \"grey-darker\": $grey-darker, \"grey-dark\": $grey-dark, \"grey\": $grey, \"grey-light\": $grey-light, \"grey-lighter\": $grey-lighter, \"white-ter\": $white-ter, \"white-bis\": $white-bis), $custom-shades)", "type": "map"}, "$sizes": {"name": "$sizes", "value": "$size-1 $size-2 $size-3 $size-4 $size-5 $size-6 $size-7", "type": "map"}}, "list": ["$primary", "$info", "$success", "$warning", "$danger", "$light", "$dark", "$orange-invert", "$yellow-invert", "$green-invert", "$turquoise-invert", "$cyan-invert", "$blue-invert", "$purple-invert", "$red-invert", "$primary-invert", "$primary-light", "$primary-dark", "$info-invert", "$info-light", "$info-dark", "$success-invert", "$success-light", "$success-dark", "$warning-invert", "$warning-light", "$warning-dark", "$danger-invert", "$danger-light", "$danger-dark", "$light-invert", "$dark-invert", "$scheme-main", "$scheme-main-bis", "$scheme-main-ter", "$scheme-invert", "$scheme-invert-bis", "$scheme-invert-ter", "$background", "$border", "$border-hover", "$border-light", "$border-light-hover", "$text", "$text-invert", "$text-light", "$text-strong", "$code", "$code-background", "$pre", "$pre-background", "$link", "$link-invert", "$link-light", "$link-dark", "$link-visited", "$link-hover", "$link-hover-border", "$link-focus", "$link-focus-border", "$link-active", "$link-active-border", "$family-primary", "$family-secondary", "$family-code", "$size-small", "$size-normal", "$size-medium", "$size-large", "$shadow", "$custom-colors", "$custom-shades", "$colors", "$shades", "$sizes"], "file_path": "utilities/derived-variables.sass"}