{"by_name": {"$black": {"name": "$black", "value": "hsl(0, 0%, 4%)", "type": "color"}, "$black-bis": {"name": "$black-bis", "value": "hsl(0, 0%, 7%)", "type": "color"}, "$black-ter": {"name": "$black-ter", "value": "hsl(0, 0%, 14%)", "type": "color"}, "$grey-darker": {"name": "$grey-darker", "value": "hsl(0, 0%, 21%)", "type": "color"}, "$grey-dark": {"name": "$grey-dark", "value": "hsl(0, 0%, 29%)", "type": "color"}, "$grey": {"name": "$grey", "value": "hsl(0, 0%, 48%)", "type": "color"}, "$grey-light": {"name": "$grey-light", "value": "hsl(0, 0%, 71%)", "type": "color"}, "$grey-lighter": {"name": "$grey-lighter", "value": "hsl(0, 0%, 86%)", "type": "color"}, "$grey-lightest": {"name": "$grey-lightest", "value": "hsl(0, 0%, 93%)", "type": "color"}, "$white-ter": {"name": "$white-ter", "value": "hsl(0, 0%, 96%)", "type": "color"}, "$white-bis": {"name": "$white-bis", "value": "hsl(0, 0%, 98%)", "type": "color"}, "$white": {"name": "$white", "value": "hsl(0, 0%, 100%)", "type": "color"}, "$orange": {"name": "$orange", "value": "hsl(14,  100%, 53%)", "type": "color"}, "$yellow": {"name": "$yellow", "value": "hsl(44,  100%, 77%)", "type": "color"}, "$green": {"name": "$green", "value": "hsl(153, 53%,  53%)", "type": "color"}, "$turquoise": {"name": "$turquoise", "value": "hsl(171, 100%, 41%)", "type": "color"}, "$cyan": {"name": "$cyan", "value": "hsl(207, 61%,  53%)", "type": "color"}, "$blue": {"name": "$blue", "value": "hsl(229, 53%,  53%)", "type": "color"}, "$purple": {"name": "$purple", "value": "hsl(271, 100%, 71%)", "type": "color"}, "$red": {"name": "$red", "value": "hsl(348, 86%, 61%)", "type": "color"}, "$family-sans-serif": {"name": "$family-sans-serif", "value": "BlinkMacSystemFont, -apple-system, \"Segoe UI\", \"Roboto\", \"Oxygen\", \"Ubuntu\", \"Cantarell\", \"Fira Sans\", \"Droid Sans\", \"Helvetica Neue\", \"Helvetica\", \"Arial\", sans-serif", "type": "font-family"}, "$family-monospace": {"name": "$family-monospace", "value": "monospace", "type": "font-family"}, "$render-mode": {"name": "$render-mode", "value": "optimizeLegibility", "type": "keyword"}, "$size-1": {"name": "$size-1", "value": "3rem", "type": "size"}, "$size-2": {"name": "$size-2", "value": "2.5rem", "type": "size"}, "$size-3": {"name": "$size-3", "value": "2rem", "type": "size"}, "$size-4": {"name": "$size-4", "value": "1.5rem", "type": "size"}, "$size-5": {"name": "$size-5", "value": "1.25rem", "type": "size"}, "$size-6": {"name": "$size-6", "value": "1rem", "type": "size"}, "$size-7": {"name": "$size-7", "value": "0.75rem", "type": "size"}, "$weight-light": {"name": "$weight-light", "value": "300", "type": "font-weight"}, "$weight-normal": {"name": "$weight-normal", "value": "400", "type": "font-weight"}, "$weight-medium": {"name": "$weight-medium", "value": "500", "type": "font-weight"}, "$weight-semibold": {"name": "$weight-semibold", "value": "600", "type": "font-weight"}, "$weight-bold": {"name": "$weight-bold", "value": "700", "type": "font-weight"}, "$block-spacing": {"name": "$block-spacing", "value": "1.5rem", "type": "size"}, "$gap": {"name": "$gap", "value": "32px", "type": "size"}, "$tablet": {"name": "$tablet", "value": "769px", "type": "size"}, "$desktop": {"name": "$desktop", "value": "960px + (2 * $gap)", "type": "computed"}, "$widescreen": {"name": "$widescreen", "value": "1152px + (2 * $gap)", "type": "computed"}, "$widescreen-enabled": {"name": "$widescreen-enabled", "value": "true", "type": "boolean"}, "$fullhd": {"name": "$fullhd", "value": "1344px + (2 * $gap)", "type": "computed"}, "$fullhd-enabled": {"name": "$fullhd-enabled", "value": "true", "type": "boolean"}, "$breakpoints": {"name": "$breakpoints", "value": "(\"mobile\": (\"until\": $tablet), \"tablet\": (\"from\": $tablet), \"tablet-only\": (\"from\": $tablet, \"until\": $desktop), \"touch\": (\"from\": $desktop), \"desktop\": (\"from\": $desktop), \"desktop-only\": (\"from\": $desktop, \"until\": $widescreen), \"until-widescreen\": (\"until\": $widescreen), \"widescreen\": (\"from\": $widescreen), \"widescreen-only\": (\"from\": $widescreen, \"until\": $fullhd), \"until-fullhd\": (\"until\": $fullhd), \"fullhd\": (\"from\": $fullhd))", "type": "compound"}, "$easing": {"name": "$easing", "value": "ease-out", "type": "keyword"}, "$radius-small": {"name": "$radius-small", "value": "2px", "type": "size"}, "$radius": {"name": "$radius", "value": "4px", "type": "size"}, "$radius-large": {"name": "$radius-large", "value": "6px", "type": "size"}, "$radius-rounded": {"name": "$radius-rounded", "value": "9999px", "type": "size"}, "$speed": {"name": "$speed", "value": "86ms", "type": "duration"}, "$variable-columns": {"name": "$variable-columns", "value": "true", "type": "boolean"}, "$rtl": {"name": "$rtl", "value": "false", "type": "boolean"}}, "list": ["$black", "$black-bis", "$black-ter", "$grey-darker", "$grey-dark", "$grey", "$grey-light", "$grey-lighter", "$grey-lightest", "$white-ter", "$white-bis", "$white", "$orange", "$yellow", "$green", "$turquoise", "$cyan", "$blue", "$purple", "$red", "$family-sans-serif", "$family-monospace", "$render-mode", "$size-1", "$size-2", "$size-3", "$size-4", "$size-5", "$size-6", "$size-7", "$weight-light", "$weight-normal", "$weight-medium", "$weight-semibold", "$weight-bold", "$block-spacing", "$gap", "$tablet", "$desktop", "$widescreen", "$widescreen-enabled", "$fullhd", "$fullhd-enabled", "$breakpoints", "$easing", "$radius-small", "$radius", "$radius-large", "$radius-rounded", "$speed", "$variable-columns", "$rtl"], "file_path": "utilities/initial-variables.sass"}