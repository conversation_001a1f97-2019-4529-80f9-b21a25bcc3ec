{"by_name": {"$body-background-color": {"name": "$body-background-color", "value": "$scheme-main", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 100%)"}, "$body-size": {"name": "$body-size", "value": "16px", "type": "size"}, "$body-min-width": {"name": "$body-min-width", "value": "300px", "type": "size"}, "$body-rendering": {"name": "$body-rendering", "value": "optimizeLegibility", "type": "keyword"}, "$body-family": {"name": "$body-family", "value": "$family-primary", "type": "variable", "computed_type": "font-family", "computed_value": "BlinkMacSystemFont, -apple-system, \"Segoe UI\", \"Roboto\", \"Oxygen\", \"Ubuntu\", \"Cantarell\", \"Fira Sans\", \"Droid Sans\", \"Helvetica Neue\", \"Helvetica\", \"Arial\", sans-serif"}, "$body-overflow-x": {"name": "$body-overflow-x", "value": "hidden", "type": "string"}, "$body-overflow-y": {"name": "$body-overflow-y", "value": "scroll", "type": "string"}, "$body-color": {"name": "$body-color", "value": "$text", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 29%)"}, "$body-font-size": {"name": "$body-font-size", "value": "1em", "type": "size"}, "$body-weight": {"name": "$body-weight", "value": "$weight-normal", "type": "variable", "computed_type": "font-weight", "computed_value": "400"}, "$body-line-height": {"name": "$body-line-height", "value": "1.5", "type": "unitless"}, "$code-family": {"name": "$code-family", "value": "$family-code", "type": "variable", "computed_type": "font-family", "computed_value": "monospace"}, "$code-padding": {"name": "$code-padding", "value": "0.25em 0.5em 0.25em", "type": "size"}, "$code-weight": {"name": "$code-weight", "value": "normal", "type": "font-weight"}, "$code-size": {"name": "$code-size", "value": "0.875em", "type": "size"}, "$small-font-size": {"name": "$small-font-size", "value": "0.875em", "type": "size"}, "$hr-background-color": {"name": "$hr-background-color", "value": "$background", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 96%)"}, "$hr-height": {"name": "$hr-height", "value": "2px", "type": "size"}, "$hr-margin": {"name": "$hr-margin", "value": "1.5rem 0", "type": "size"}, "$strong-color": {"name": "$strong-color", "value": "$text-strong", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$strong-weight": {"name": "$strong-weight", "value": "$weight-bold", "type": "variable", "computed_type": "font-weight", "computed_value": "700"}, "$pre-font-size": {"name": "$pre-font-size", "value": "0.875em", "type": "size"}, "$pre-padding": {"name": "$pre-padding", "value": "1.25rem 1.5rem", "type": "size"}, "$pre-code-font-size": {"name": "$pre-code-font-size", "value": "1em", "type": "size"}}, "list": ["$body-background-color", "$body-size", "$body-min-width", "$body-rendering", "$body-family", "$body-overflow-x", "$body-overflow-y", "$body-color", "$body-font-size", "$body-weight", "$body-line-height", "$code-family", "$code-padding", "$code-weight", "$code-size", "$small-font-size", "$hr-background-color", "$hr-height", "$hr-margin", "$strong-color", "$strong-weight", "$pre-font-size", "$pre-padding", "$pre-code-font-size"], "file_path": "base/generic.sass"}