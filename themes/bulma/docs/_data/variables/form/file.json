{"by_name": {"$file-border-color": {"name": "$file-border-color", "value": "$border", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 86%)"}, "$file-radius": {"name": "$file-radius", "value": "$radius", "type": "variable", "computed_type": "size", "computed_value": "4px"}, "$file-cta-background-color": {"name": "$file-cta-background-color", "value": "$scheme-main-ter", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 96%)"}, "$file-cta-color": {"name": "$file-cta-color", "value": "$text", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 29%)"}, "$file-cta-hover-color": {"name": "$file-cta-hover-color", "value": "$text-strong", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$file-cta-active-color": {"name": "$file-cta-active-color", "value": "$text-strong", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$file-name-border-color": {"name": "$file-name-border-color", "value": "$border", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 86%)"}, "$file-name-border-style": {"name": "$file-name-border-style", "value": "solid", "type": "string"}, "$file-name-border-width": {"name": "$file-name-border-width", "value": "1px 1px 1px 0", "type": "size"}, "$file-name-max-width": {"name": "$file-name-max-width", "value": "16em", "type": "size"}, "$file-colors": {"name": "$file-colors", "value": "$form-colors", "type": "variable"}}, "list": ["$file-border-color", "$file-radius", "$file-cta-background-color", "$file-cta-color", "$file-cta-hover-color", "$file-cta-active-color", "$file-name-border-color", "$file-name-border-style", "$file-name-border-width", "$file-name-max-width", "$file-colors"], "file_path": "form/file.sass"}