{"by_name": {"$form-colors": {"name": "$form-colors", "value": "$colors", "type": "variable", "computed_type": "map", "computed_value": "Bulma colors"}, "$input-color": {"name": "$input-color", "value": "$text-strong", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$input-background-color": {"name": "$input-background-color", "value": "$scheme-main", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 100%)"}, "$input-border-color": {"name": "$input-border-color", "value": "$border", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 86%)"}, "$input-height": {"name": "$input-height", "value": "$control-height", "type": "variable"}, "$input-shadow": {"name": "$input-shadow", "value": "inset 0 0.0625em 0.125em rgba($scheme-invert, 0.05)", "type": "shadow"}, "$input-placeholder-color": {"name": "$input-placeholder-color", "value": "bulmaRgba($input-color, 0.3)", "type": "compound"}, "$input-hover-color": {"name": "$input-hover-color", "value": "$text-strong", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$input-hover-border-color": {"name": "$input-hover-border-color", "value": "$border-hover", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 71%)"}, "$input-focus-color": {"name": "$input-focus-color", "value": "$text-strong", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$input-focus-border-color": {"name": "$input-focus-border-color", "value": "$link", "type": "variable", "computed_type": "color", "computed_value": "hsl(229, 53%,  53%)"}, "$input-focus-box-shadow-size": {"name": "$input-focus-box-shadow-size", "value": "0 0 0 0.125em", "type": "size"}, "$input-focus-box-shadow-color": {"name": "$input-focus-box-shadow-color", "value": "bulmaRgba($link, 0.25)", "type": "compound"}, "$input-disabled-color": {"name": "$input-disabled-color", "value": "$text-light", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 48%)"}, "$input-disabled-background-color": {"name": "$input-disabled-background-color", "value": "$background", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 96%)"}, "$input-disabled-border-color": {"name": "$input-disabled-border-color", "value": "$background", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 96%)"}, "$input-disabled-placeholder-color": {"name": "$input-disabled-placeholder-color", "value": "bulmaRgba($input-disabled-color, 0.3)", "type": "compound"}, "$input-arrow": {"name": "$input-arrow", "value": "$link", "type": "variable", "computed_type": "color", "computed_value": "hsl(229, 53%,  53%)"}, "$input-icon-color": {"name": "$input-icon-color", "value": "$border", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 86%)"}, "$input-icon-active-color": {"name": "$input-icon-active-color", "value": "$text", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 29%)"}, "$input-radius": {"name": "$input-radius", "value": "$radius", "type": "variable", "computed_type": "size", "computed_value": "4px"}}, "list": ["$form-colors", "$input-color", "$input-background-color", "$input-border-color", "$input-height", "$input-shadow", "$input-placeholder-color", "$input-hover-color", "$input-hover-border-color", "$input-focus-color", "$input-focus-border-color", "$input-focus-box-shadow-size", "$input-focus-box-shadow-color", "$input-disabled-color", "$input-disabled-background-color", "$input-disabled-border-color", "$input-disabled-placeholder-color", "$input-arrow", "$input-icon-color", "$input-icon-active-color", "$input-radius"], "file_path": "form/shared.sass"}