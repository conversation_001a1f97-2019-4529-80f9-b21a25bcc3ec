{"by_name": {"$panel-margin": {"name": "$panel-margin", "value": "$block-spacing", "type": "variable", "computed_type": "size", "computed_value": "1.5rem"}, "$panel-item-border": {"name": "$panel-item-border", "value": "1px solid $border-light", "type": "size"}, "$panel-radius": {"name": "$panel-radius", "value": "$radius-large", "type": "variable", "computed_type": "size", "computed_value": "6px"}, "$panel-shadow": {"name": "$panel-shadow", "value": "$shadow", "type": "variable", "computed_type": "shadow", "computed_value": "0 0.5em 1em -0.125em rgba($scheme-invert, 0.1), 0 0px 0 1px rgba($scheme-invert, 0.02)"}, "$panel-heading-background-color": {"name": "$panel-heading-background-color", "value": "$border-light", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 93%)"}, "$panel-heading-color": {"name": "$panel-heading-color", "value": "$text-strong", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$panel-heading-line-height": {"name": "$panel-heading-line-height", "value": "1.25", "type": "unitless"}, "$panel-heading-padding": {"name": "$panel-heading-padding", "value": "0.75em 1em", "type": "size"}, "$panel-heading-radius": {"name": "$panel-heading-radius", "value": "$radius", "type": "variable", "computed_type": "size", "computed_value": "4px"}, "$panel-heading-size": {"name": "$panel-heading-size", "value": "1.25em", "type": "size"}, "$panel-heading-weight": {"name": "$panel-heading-weight", "value": "$weight-bold", "type": "variable", "computed_type": "font-weight", "computed_value": "700"}, "$panel-tabs-font-size": {"name": "$panel-tabs-font-size", "value": "0.875em", "type": "size"}, "$panel-tab-border-bottom": {"name": "$panel-tab-border-bottom", "value": "1px solid $border", "type": "size"}, "$panel-tab-active-border-bottom-color": {"name": "$panel-tab-active-border-bottom-color", "value": "$link-active-border", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 29%)"}, "$panel-tab-active-color": {"name": "$panel-tab-active-color", "value": "$link-active", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$panel-list-item-color": {"name": "$panel-list-item-color", "value": "$text", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 29%)"}, "$panel-list-item-hover-color": {"name": "$panel-list-item-hover-color", "value": "$link", "type": "variable", "computed_type": "color", "computed_value": "hsl(229, 53%,  53%)"}, "$panel-block-color": {"name": "$panel-block-color", "value": "$text-strong", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$panel-block-hover-background-color": {"name": "$panel-block-hover-background-color", "value": "$background", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 96%)"}, "$panel-block-active-border-left-color": {"name": "$panel-block-active-border-left-color", "value": "$link", "type": "variable", "computed_type": "color", "computed_value": "hsl(229, 53%,  53%)"}, "$panel-block-active-color": {"name": "$panel-block-active-color", "value": "$link-active", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$panel-block-active-icon-color": {"name": "$panel-block-active-icon-color", "value": "$link", "type": "variable", "computed_type": "color", "computed_value": "hsl(229, 53%,  53%)"}, "$panel-icon-color": {"name": "$panel-icon-color", "value": "$text-light", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 48%)"}, "$panel-colors": {"name": "$panel-colors", "value": "$colors", "type": "variable", "computed_type": "map", "computed_value": "Bulma colors"}}, "list": ["$panel-margin", "$panel-item-border", "$panel-radius", "$panel-shadow", "$panel-heading-background-color", "$panel-heading-color", "$panel-heading-line-height", "$panel-heading-padding", "$panel-heading-radius", "$panel-heading-size", "$panel-heading-weight", "$panel-tabs-font-size", "$panel-tab-border-bottom", "$panel-tab-active-border-bottom-color", "$panel-tab-active-color", "$panel-list-item-color", "$panel-list-item-hover-color", "$panel-block-color", "$panel-block-hover-background-color", "$panel-block-active-border-left-color", "$panel-block-active-color", "$panel-block-active-icon-color", "$panel-icon-color", "$panel-colors"], "file_path": "components/panel.sass"}