{"by_name": {"$tabs-border-bottom-color": {"name": "$tabs-border-bottom-color", "value": "$border", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 86%)"}, "$tabs-border-bottom-style": {"name": "$tabs-border-bottom-style", "value": "solid", "type": "string"}, "$tabs-border-bottom-width": {"name": "$tabs-border-bottom-width", "value": "1px", "type": "size"}, "$tabs-link-color": {"name": "$tabs-link-color", "value": "$text", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 29%)"}, "$tabs-link-hover-border-bottom-color": {"name": "$tabs-link-hover-border-bottom-color", "value": "$text-strong", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$tabs-link-hover-color": {"name": "$tabs-link-hover-color", "value": "$text-strong", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$tabs-link-active-border-bottom-color": {"name": "$tabs-link-active-border-bottom-color", "value": "$link", "type": "variable", "computed_type": "color", "computed_value": "hsl(229, 53%,  53%)"}, "$tabs-link-active-color": {"name": "$tabs-link-active-color", "value": "$link", "type": "variable", "computed_type": "color", "computed_value": "hsl(229, 53%,  53%)"}, "$tabs-link-padding": {"name": "$tabs-link-padding", "value": "0.5em 1em", "type": "size"}, "$tabs-boxed-link-radius": {"name": "$tabs-boxed-link-radius", "value": "$radius", "type": "variable", "computed_type": "size", "computed_value": "4px"}, "$tabs-boxed-link-hover-background-color": {"name": "$tabs-boxed-link-hover-background-color", "value": "$background", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 96%)"}, "$tabs-boxed-link-hover-border-bottom-color": {"name": "$tabs-boxed-link-hover-border-bottom-color", "value": "$border", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 86%)"}, "$tabs-boxed-link-active-background-color": {"name": "$tabs-boxed-link-active-background-color", "value": "$scheme-main", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 100%)"}, "$tabs-boxed-link-active-border-color": {"name": "$tabs-boxed-link-active-border-color", "value": "$border", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 86%)"}, "$tabs-boxed-link-active-border-bottom-color": {"name": "$tabs-boxed-link-active-border-bottom-color", "value": "transparent", "type": "string"}, "$tabs-toggle-link-border-color": {"name": "$tabs-toggle-link-border-color", "value": "$border", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 86%)"}, "$tabs-toggle-link-border-style": {"name": "$tabs-toggle-link-border-style", "value": "solid", "type": "string"}, "$tabs-toggle-link-border-width": {"name": "$tabs-toggle-link-border-width", "value": "1px", "type": "size"}, "$tabs-toggle-link-hover-background-color": {"name": "$tabs-toggle-link-hover-background-color", "value": "$background", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 96%)"}, "$tabs-toggle-link-hover-border-color": {"name": "$tabs-toggle-link-hover-border-color", "value": "$border-hover", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 71%)"}, "$tabs-toggle-link-radius": {"name": "$tabs-toggle-link-radius", "value": "$radius", "type": "variable", "computed_type": "size", "computed_value": "4px"}, "$tabs-toggle-link-active-background-color": {"name": "$tabs-toggle-link-active-background-color", "value": "$link", "type": "variable", "computed_type": "color", "computed_value": "hsl(229, 53%,  53%)"}, "$tabs-toggle-link-active-border-color": {"name": "$tabs-toggle-link-active-border-color", "value": "$link", "type": "variable", "computed_type": "color", "computed_value": "hsl(229, 53%,  53%)"}, "$tabs-toggle-link-active-color": {"name": "$tabs-toggle-link-active-color", "value": "$link-invert", "type": "variable", "computed_type": "color", "computed_value": "#fff"}}, "list": ["$tabs-border-bottom-color", "$tabs-border-bottom-style", "$tabs-border-bottom-width", "$tabs-link-color", "$tabs-link-hover-border-bottom-color", "$tabs-link-hover-color", "$tabs-link-active-border-bottom-color", "$tabs-link-active-color", "$tabs-link-padding", "$tabs-boxed-link-radius", "$tabs-boxed-link-hover-background-color", "$tabs-boxed-link-hover-border-bottom-color", "$tabs-boxed-link-active-background-color", "$tabs-boxed-link-active-border-color", "$tabs-boxed-link-active-border-bottom-color", "$tabs-toggle-link-border-color", "$tabs-toggle-link-border-style", "$tabs-toggle-link-border-width", "$tabs-toggle-link-hover-background-color", "$tabs-toggle-link-hover-border-color", "$tabs-toggle-link-radius", "$tabs-toggle-link-active-background-color", "$tabs-toggle-link-active-border-color", "$tabs-toggle-link-active-color"], "file_path": "components/tabs.sass"}