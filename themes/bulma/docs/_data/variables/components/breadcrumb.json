{"by_name": {"$breadcrumb-item-color": {"name": "$breadcrumb-item-color", "value": "$link", "type": "variable", "computed_type": "color", "computed_value": "hsl(229, 53%,  53%)"}, "$breadcrumb-item-hover-color": {"name": "$breadcrumb-item-hover-color", "value": "$link-hover", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$breadcrumb-item-active-color": {"name": "$breadcrumb-item-active-color", "value": "$text-strong", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$breadcrumb-item-padding-vertical": {"name": "$breadcrumb-item-padding-vertical", "value": "0", "type": "string"}, "$breadcrumb-item-padding-horizontal": {"name": "$breadcrumb-item-padding-horizontal", "value": "0.75em", "type": "size"}, "$breadcrumb-item-separator-color": {"name": "$breadcrumb-item-separator-color", "value": "$border-hover", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 71%)"}}, "list": ["$breadcrumb-item-color", "$breadcrumb-item-hover-color", "$breadcrumb-item-active-color", "$breadcrumb-item-padding-vertical", "$breadcrumb-item-padding-horizontal", "$breadcrumb-item-separator-color"], "file_path": "components/breadcrumb.sass"}