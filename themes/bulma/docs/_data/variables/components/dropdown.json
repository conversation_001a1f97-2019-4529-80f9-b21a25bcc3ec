{"by_name": {"$dropdown-menu-min-width": {"name": "$dropdown-menu-min-width", "value": "12rem", "type": "size"}, "$dropdown-content-background-color": {"name": "$dropdown-content-background-color", "value": "$scheme-main", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 100%)"}, "$dropdown-content-arrow": {"name": "$dropdown-content-arrow", "value": "$link", "type": "variable", "computed_type": "color", "computed_value": "hsl(229, 53%,  53%)"}, "$dropdown-content-offset": {"name": "$dropdown-content-offset", "value": "4px", "type": "size"}, "$dropdown-content-padding-bottom": {"name": "$dropdown-content-padding-bottom", "value": "0.5rem", "type": "size"}, "$dropdown-content-padding-top": {"name": "$dropdown-content-padding-top", "value": "0.5rem", "type": "size"}, "$dropdown-content-radius": {"name": "$dropdown-content-radius", "value": "$radius", "type": "variable", "computed_type": "size", "computed_value": "4px"}, "$dropdown-content-shadow": {"name": "$dropdown-content-shadow", "value": "$shadow", "type": "variable", "computed_type": "shadow", "computed_value": "0 0.5em 1em -0.125em rgba($scheme-invert, 0.1), 0 0px 0 1px rgba($scheme-invert, 0.02)"}, "$dropdown-content-z": {"name": "$dropdown-content-z", "value": "20", "type": "string"}, "$dropdown-item-color": {"name": "$dropdown-item-color", "value": "$text", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 29%)"}, "$dropdown-item-hover-color": {"name": "$dropdown-item-hover-color", "value": "$scheme-invert", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 4%)"}, "$dropdown-item-hover-background-color": {"name": "$dropdown-item-hover-background-color", "value": "$background", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 96%)"}, "$dropdown-item-active-color": {"name": "$dropdown-item-active-color", "value": "$link-invert", "type": "variable", "computed_type": "color", "computed_value": "#fff"}, "$dropdown-item-active-background-color": {"name": "$dropdown-item-active-background-color", "value": "$link", "type": "variable", "computed_type": "color", "computed_value": "hsl(229, 53%,  53%)"}, "$dropdown-divider-background-color": {"name": "$dropdown-divider-background-color", "value": "$border-light", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 93%)"}}, "list": ["$dropdown-menu-min-width", "$dropdown-content-background-color", "$dropdown-content-arrow", "$dropdown-content-offset", "$dropdown-content-padding-bottom", "$dropdown-content-padding-top", "$dropdown-content-radius", "$dropdown-content-shadow", "$dropdown-content-z", "$dropdown-item-color", "$dropdown-item-hover-color", "$dropdown-item-hover-background-color", "$dropdown-item-active-color", "$dropdown-item-active-background-color", "$dropdown-divider-background-color"], "file_path": "components/dropdown.sass"}