{"by_name": {"$pagination-color": {"name": "$pagination-color", "value": "$text-strong", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$pagination-border-color": {"name": "$pagination-border-color", "value": "$border", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 86%)"}, "$pagination-margin": {"name": "$pagination-margin", "value": "-0.25rem", "type": "size"}, "$pagination-min-width": {"name": "$pagination-min-width", "value": "$control-height", "type": "variable"}, "$pagination-item-font-size": {"name": "$pagination-item-font-size", "value": "1em", "type": "size"}, "$pagination-item-margin": {"name": "$pagination-item-margin", "value": "0.25rem", "type": "size"}, "$pagination-item-padding-left": {"name": "$pagination-item-padding-left", "value": "0.5em", "type": "size"}, "$pagination-item-padding-right": {"name": "$pagination-item-padding-right", "value": "0.5em", "type": "size"}, "$pagination-nav-padding-left": {"name": "$pagination-nav-padding-left", "value": "0.75em", "type": "size"}, "$pagination-nav-padding-right": {"name": "$pagination-nav-padding-right", "value": "0.75em", "type": "size"}, "$pagination-hover-color": {"name": "$pagination-hover-color", "value": "$link-hover", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$pagination-hover-border-color": {"name": "$pagination-hover-border-color", "value": "$link-hover-border", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 71%)"}, "$pagination-focus-color": {"name": "$pagination-focus-color", "value": "$link-focus", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$pagination-focus-border-color": {"name": "$pagination-focus-border-color", "value": "$link-focus-border", "type": "variable", "computed_type": "color", "computed_value": "hsl(229, 53%,  53%)"}, "$pagination-active-color": {"name": "$pagination-active-color", "value": "$link-active", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$pagination-active-border-color": {"name": "$pagination-active-border-color", "value": "$link-active-border", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 29%)"}, "$pagination-disabled-color": {"name": "$pagination-disabled-color", "value": "$text-light", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 48%)"}, "$pagination-disabled-background-color": {"name": "$pagination-disabled-background-color", "value": "$border", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 86%)"}, "$pagination-disabled-border-color": {"name": "$pagination-disabled-border-color", "value": "$border", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 86%)"}, "$pagination-current-color": {"name": "$pagination-current-color", "value": "$link-invert", "type": "variable", "computed_type": "color", "computed_value": "#fff"}, "$pagination-current-background-color": {"name": "$pagination-current-background-color", "value": "$link", "type": "variable", "computed_type": "color", "computed_value": "hsl(229, 53%,  53%)"}, "$pagination-current-border-color": {"name": "$pagination-current-border-color", "value": "$link", "type": "variable", "computed_type": "color", "computed_value": "hsl(229, 53%,  53%)"}, "$pagination-ellipsis-color": {"name": "$pagination-ellipsis-color", "value": "$grey-light", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 71%)"}, "$pagination-shadow-inset": {"name": "$pagination-shadow-inset", "value": "inset 0 1px 2px rgba($scheme-invert, 0.2)", "type": "size"}}, "list": ["$pagination-color", "$pagination-border-color", "$pagination-margin", "$pagination-min-width", "$pagination-item-font-size", "$pagination-item-margin", "$pagination-item-padding-left", "$pagination-item-padding-right", "$pagination-nav-padding-left", "$pagination-nav-padding-right", "$pagination-hover-color", "$pagination-hover-border-color", "$pagination-focus-color", "$pagination-focus-border-color", "$pagination-active-color", "$pagination-active-border-color", "$pagination-disabled-color", "$pagination-disabled-background-color", "$pagination-disabled-border-color", "$pagination-current-color", "$pagination-current-background-color", "$pagination-current-border-color", "$pagination-ellipsis-color", "$pagination-shadow-inset"], "file_path": "components/pagination.sass"}