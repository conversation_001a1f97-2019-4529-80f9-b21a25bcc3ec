{"by_name": {"$menu-item-color": {"name": "$menu-item-color", "value": "$text", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 29%)"}, "$menu-item-radius": {"name": "$menu-item-radius", "value": "$radius-small", "type": "variable", "computed_type": "size", "computed_value": "2px"}, "$menu-item-hover-color": {"name": "$menu-item-hover-color", "value": "$text-strong", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$menu-item-hover-background-color": {"name": "$menu-item-hover-background-color", "value": "$background", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 96%)"}, "$menu-item-active-color": {"name": "$menu-item-active-color", "value": "$link-invert", "type": "variable", "computed_type": "color", "computed_value": "#fff"}, "$menu-item-active-background-color": {"name": "$menu-item-active-background-color", "value": "$link", "type": "variable", "computed_type": "color", "computed_value": "hsl(229, 53%,  53%)"}, "$menu-list-border-left": {"name": "$menu-list-border-left", "value": "1px solid $border", "type": "size"}, "$menu-list-line-height": {"name": "$menu-list-line-height", "value": "1.25", "type": "unitless"}, "$menu-list-link-padding": {"name": "$menu-list-link-padding", "value": "0.5em 0.75em", "type": "size"}, "$menu-nested-list-margin": {"name": "$menu-nested-list-margin", "value": "0.75em", "type": "size"}, "$menu-nested-list-padding-left": {"name": "$menu-nested-list-padding-left", "value": "0.75em", "type": "size"}, "$menu-label-color": {"name": "$menu-label-color", "value": "$text-light", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 48%)"}, "$menu-label-font-size": {"name": "$menu-label-font-size", "value": "0.75em", "type": "size"}, "$menu-label-letter-spacing": {"name": "$menu-label-letter-spacing", "value": "0.1em", "type": "size"}, "$menu-label-spacing": {"name": "$menu-label-spacing", "value": "1em", "type": "size"}}, "list": ["$menu-item-color", "$menu-item-radius", "$menu-item-hover-color", "$menu-item-hover-background-color", "$menu-item-active-color", "$menu-item-active-background-color", "$menu-list-border-left", "$menu-list-line-height", "$menu-list-link-padding", "$menu-nested-list-margin", "$menu-nested-list-padding-left", "$menu-label-color", "$menu-label-font-size", "$menu-label-letter-spacing", "$menu-label-spacing"], "file_path": "components/menu.sass"}