{"by_name": {"$modal-z": {"name": "$modal-z", "value": "40", "type": "string"}, "$modal-background-background-color": {"name": "$modal-background-background-color", "value": "bulmaRgba($scheme-invert, 0.86)", "type": "compound"}, "$modal-content-width": {"name": "$modal-content-width", "value": "640px", "type": "size"}, "$modal-content-margin-mobile": {"name": "$modal-content-margin-mobile", "value": "20px", "type": "size"}, "$modal-content-spacing-mobile": {"name": "$modal-content-spacing-mobile", "value": "160px", "type": "size"}, "$modal-content-spacing-tablet": {"name": "$modal-content-spacing-tablet", "value": "40px", "type": "size"}, "$modal-close-dimensions": {"name": "$modal-close-dimensions", "value": "40px", "type": "size"}, "$modal-close-right": {"name": "$modal-close-right", "value": "20px", "type": "size"}, "$modal-close-top": {"name": "$modal-close-top", "value": "20px", "type": "size"}, "$modal-card-spacing": {"name": "$modal-card-spacing", "value": "40px", "type": "size"}, "$modal-card-head-background-color": {"name": "$modal-card-head-background-color", "value": "$background", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 96%)"}, "$modal-card-head-border-bottom": {"name": "$modal-card-head-border-bottom", "value": "1px solid $border", "type": "size"}, "$modal-card-head-padding": {"name": "$modal-card-head-padding", "value": "20px", "type": "size"}, "$modal-card-head-radius": {"name": "$modal-card-head-radius", "value": "$radius-large", "type": "variable", "computed_type": "size", "computed_value": "6px"}, "$modal-card-title-color": {"name": "$modal-card-title-color", "value": "$text-strong", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$modal-card-title-line-height": {"name": "$modal-card-title-line-height", "value": "1", "type": "string"}, "$modal-card-title-size": {"name": "$modal-card-title-size", "value": "$size-4", "type": "variable", "computed_type": "size", "computed_value": "1.5rem"}, "$modal-card-foot-radius": {"name": "$modal-card-foot-radius", "value": "$radius-large", "type": "variable", "computed_type": "size", "computed_value": "6px"}, "$modal-card-foot-border-top": {"name": "$modal-card-foot-border-top", "value": "1px solid $border", "type": "size"}, "$modal-card-body-background-color": {"name": "$modal-card-body-background-color", "value": "$scheme-main", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 100%)"}, "$modal-card-body-padding": {"name": "$modal-card-body-padding", "value": "20px", "type": "size"}, "$modal-breakpoint": {"name": "$modal-breakpoint", "value": "$tablet", "type": "variable", "computed_type": "size", "computed_value": "769px"}}, "list": ["$modal-z", "$modal-background-background-color", "$modal-content-width", "$modal-content-margin-mobile", "$modal-content-spacing-mobile", "$modal-content-spacing-tablet", "$modal-close-dimensions", "$modal-close-right", "$modal-close-top", "$modal-card-spacing", "$modal-card-head-background-color", "$modal-card-head-border-bottom", "$modal-card-head-padding", "$modal-card-head-radius", "$modal-card-title-color", "$modal-card-title-line-height", "$modal-card-title-size", "$modal-card-foot-radius", "$modal-card-foot-border-top", "$modal-card-body-background-color", "$modal-card-body-padding", "$modal-breakpoint"], "file_path": "components/modal.sass"}