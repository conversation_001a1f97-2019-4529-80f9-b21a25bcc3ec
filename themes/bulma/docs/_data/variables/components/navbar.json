{"by_name": {"$navbar-background-color": {"name": "$navbar-background-color", "value": "$scheme-main", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 100%)"}, "$navbar-box-shadow-size": {"name": "$navbar-box-shadow-size", "value": "0 2px 0 0", "type": "size"}, "$navbar-box-shadow-color": {"name": "$navbar-box-shadow-color", "value": "$background", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 96%)"}, "$navbar-height": {"name": "$navbar-height", "value": "3.25rem", "type": "size"}, "$navbar-padding-vertical": {"name": "$navbar-padding-vertical", "value": "1rem", "type": "size"}, "$navbar-padding-horizontal": {"name": "$navbar-padding-horizontal", "value": "2rem", "type": "size"}, "$navbar-z": {"name": "$navbar-z", "value": "30", "type": "string"}, "$navbar-fixed-z": {"name": "$navbar-fixed-z", "value": "30", "type": "string"}, "$navbar-item-color": {"name": "$navbar-item-color", "value": "$text", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 29%)"}, "$navbar-item-hover-color": {"name": "$navbar-item-hover-color", "value": "$link", "type": "variable", "computed_type": "color", "computed_value": "hsl(229, 53%,  53%)"}, "$navbar-item-hover-background-color": {"name": "$navbar-item-hover-background-color", "value": "$scheme-main-bis", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 98%)"}, "$navbar-item-active-color": {"name": "$navbar-item-active-color", "value": "$scheme-invert", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 4%)"}, "$navbar-item-active-background-color": {"name": "$navbar-item-active-background-color", "value": "transparent", "type": "string"}, "$navbar-item-img-max-height": {"name": "$navbar-item-img-max-height", "value": "1.75rem", "type": "size"}, "$navbar-burger-color": {"name": "$navbar-burger-color", "value": "$navbar-item-color", "type": "variable"}, "$navbar-tab-hover-background-color": {"name": "$navbar-tab-hover-background-color", "value": "transparent", "type": "string"}, "$navbar-tab-hover-border-bottom-color": {"name": "$navbar-tab-hover-border-bottom-color", "value": "$link", "type": "variable", "computed_type": "color", "computed_value": "hsl(229, 53%,  53%)"}, "$navbar-tab-active-color": {"name": "$navbar-tab-active-color", "value": "$link", "type": "variable", "computed_type": "color", "computed_value": "hsl(229, 53%,  53%)"}, "$navbar-tab-active-background-color": {"name": "$navbar-tab-active-background-color", "value": "transparent", "type": "string"}, "$navbar-tab-active-border-bottom-color": {"name": "$navbar-tab-active-border-bottom-color", "value": "$link", "type": "variable", "computed_type": "color", "computed_value": "hsl(229, 53%,  53%)"}, "$navbar-tab-active-border-bottom-style": {"name": "$navbar-tab-active-border-bottom-style", "value": "solid", "type": "string"}, "$navbar-tab-active-border-bottom-width": {"name": "$navbar-tab-active-border-bottom-width", "value": "3px", "type": "size"}, "$navbar-dropdown-background-color": {"name": "$navbar-dropdown-background-color", "value": "$scheme-main", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 100%)"}, "$navbar-dropdown-border-top": {"name": "$navbar-dropdown-border-top", "value": "2px solid $border", "type": "size"}, "$navbar-dropdown-offset": {"name": "$navbar-dropdown-offset", "value": "-4px", "type": "size"}, "$navbar-dropdown-arrow": {"name": "$navbar-dropdown-arrow", "value": "$link", "type": "variable", "computed_type": "color", "computed_value": "hsl(229, 53%,  53%)"}, "$navbar-dropdown-radius": {"name": "$navbar-dropdown-radius", "value": "$radius-large", "type": "variable", "computed_type": "size", "computed_value": "6px"}, "$navbar-dropdown-z": {"name": "$navbar-dropdown-z", "value": "20", "type": "string"}, "$navbar-dropdown-boxed-radius": {"name": "$navbar-dropdown-boxed-radius", "value": "$radius-large", "type": "variable", "computed_type": "size", "computed_value": "6px"}, "$navbar-dropdown-boxed-shadow": {"name": "$navbar-dropdown-boxed-shadow", "value": "0 8px 8px bulmaRgba($scheme-invert, 0.1), 0 0 0 1px bulmaRgba($scheme-invert, 0.1)", "type": "shadow"}, "$navbar-dropdown-item-hover-color": {"name": "$navbar-dropdown-item-hover-color", "value": "$scheme-invert", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 4%)"}, "$navbar-dropdown-item-hover-background-color": {"name": "$navbar-dropdown-item-hover-background-color", "value": "$background", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 96%)"}, "$navbar-dropdown-item-active-color": {"name": "$navbar-dropdown-item-active-color", "value": "$link", "type": "variable", "computed_type": "color", "computed_value": "hsl(229, 53%,  53%)"}, "$navbar-dropdown-item-active-background-color": {"name": "$navbar-dropdown-item-active-background-color", "value": "$background", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 96%)"}, "$navbar-divider-background-color": {"name": "$navbar-divider-background-color", "value": "$background", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 96%)"}, "$navbar-divider-height": {"name": "$navbar-divider-height", "value": "2px", "type": "size"}, "$navbar-bottom-box-shadow-size": {"name": "$navbar-bottom-box-shadow-size", "value": "0 -2px 0 0", "type": "size"}, "$navbar-breakpoint": {"name": "$navbar-breakpoint", "value": "$desktop", "type": "variable", "computed_type": "computed", "computed_value": "960px + (2 * $gap)"}, "$navbar-colors": {"name": "$navbar-colors", "value": "$colors", "type": "variable", "computed_type": "map", "computed_value": "Bulma colors"}}, "list": ["$navbar-background-color", "$navbar-box-shadow-size", "$navbar-box-shadow-color", "$navbar-height", "$navbar-padding-vertical", "$navbar-padding-horizontal", "$navbar-z", "$navbar-fixed-z", "$navbar-item-color", "$navbar-item-hover-color", "$navbar-item-hover-background-color", "$navbar-item-active-color", "$navbar-item-active-background-color", "$navbar-item-img-max-height", "$navbar-burger-color", "$navbar-tab-hover-background-color", "$navbar-tab-hover-border-bottom-color", "$navbar-tab-active-color", "$navbar-tab-active-background-color", "$navbar-tab-active-border-bottom-color", "$navbar-tab-active-border-bottom-style", "$navbar-tab-active-border-bottom-width", "$navbar-dropdown-background-color", "$navbar-dropdown-border-top", "$navbar-dropdown-offset", "$navbar-dropdown-arrow", "$navbar-dropdown-radius", "$navbar-dropdown-z", "$navbar-dropdown-boxed-radius", "$navbar-dropdown-boxed-shadow", "$navbar-dropdown-item-hover-color", "$navbar-dropdown-item-hover-background-color", "$navbar-dropdown-item-active-color", "$navbar-dropdown-item-active-background-color", "$navbar-divider-background-color", "$navbar-divider-height", "$navbar-bottom-box-shadow-size", "$navbar-breakpoint", "$navbar-colors"], "file_path": "components/navbar.sass"}