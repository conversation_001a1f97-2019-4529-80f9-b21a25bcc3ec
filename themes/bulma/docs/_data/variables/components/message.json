{"by_name": {"$message-background-color": {"name": "$message-background-color", "value": "$background", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 96%)"}, "$message-radius": {"name": "$message-radius", "value": "$radius", "type": "variable", "computed_type": "size", "computed_value": "4px"}, "$message-header-background-color": {"name": "$message-header-background-color", "value": "$text", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 29%)"}, "$message-header-color": {"name": "$message-header-color", "value": "$text-invert", "type": "variable", "computed_type": "color", "computed_value": "#fff"}, "$message-header-weight": {"name": "$message-header-weight", "value": "$weight-bold", "type": "variable", "computed_type": "font-weight", "computed_value": "700"}, "$message-header-padding": {"name": "$message-header-padding", "value": "0.75em 1em", "type": "size"}, "$message-header-radius": {"name": "$message-header-radius", "value": "$radius", "type": "variable", "computed_type": "size", "computed_value": "4px"}, "$message-body-border-color": {"name": "$message-body-border-color", "value": "$border", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 86%)"}, "$message-body-border-width": {"name": "$message-body-border-width", "value": "0 0 0 4px", "type": "size"}, "$message-body-color": {"name": "$message-body-color", "value": "$text", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 29%)"}, "$message-body-padding": {"name": "$message-body-padding", "value": "1.25em 1.5em", "type": "size"}, "$message-body-radius": {"name": "$message-body-radius", "value": "$radius", "type": "variable", "computed_type": "size", "computed_value": "4px"}, "$message-body-pre-background-color": {"name": "$message-body-pre-background-color", "value": "$scheme-main", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 100%)"}, "$message-body-pre-code-background-color": {"name": "$message-body-pre-code-background-color", "value": "transparent", "type": "string"}, "$message-header-body-border-width": {"name": "$message-header-body-border-width", "value": "0", "type": "string"}, "$message-colors": {"name": "$message-colors", "value": "$colors", "type": "variable", "computed_type": "map", "computed_value": "Bulma colors"}}, "list": ["$message-background-color", "$message-radius", "$message-header-background-color", "$message-header-color", "$message-header-weight", "$message-header-padding", "$message-header-radius", "$message-body-border-color", "$message-body-border-width", "$message-body-color", "$message-body-padding", "$message-body-radius", "$message-body-pre-background-color", "$message-body-pre-code-background-color", "$message-header-body-border-width", "$message-colors"], "file_path": "components/message.sass"}