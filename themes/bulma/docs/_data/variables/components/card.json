{"by_name": {"$card-color": {"name": "$card-color", "value": "$text", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 29%)"}, "$card-background-color": {"name": "$card-background-color", "value": "$scheme-main", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 100%)"}, "$card-shadow": {"name": "$card-shadow", "value": "$shadow", "type": "variable", "computed_type": "shadow", "computed_value": "0 0.5em 1em -0.125em rgba($scheme-invert, 0.1), 0 0px 0 1px rgba($scheme-invert, 0.02)"}, "$card-radius": {"name": "$card-radius", "value": "0.25rem", "type": "size"}, "$card-header-background-color": {"name": "$card-header-background-color", "value": "transparent", "type": "string"}, "$card-header-color": {"name": "$card-header-color", "value": "$text-strong", "type": "variable", "computed_type": "color", "computed_value": "hsl(0, 0%, 21%)"}, "$card-header-padding": {"name": "$card-header-padding", "value": "0.75rem 1rem", "type": "size"}, "$card-header-shadow": {"name": "$card-header-shadow", "value": "0 0.125em 0.25em rgba($scheme-invert, 0.1)", "type": "shadow"}, "$card-header-weight": {"name": "$card-header-weight", "value": "$weight-bold", "type": "variable", "computed_type": "font-weight", "computed_value": "700"}, "$card-content-background-color": {"name": "$card-content-background-color", "value": "transparent", "type": "string"}, "$card-content-padding": {"name": "$card-content-padding", "value": "1.5rem", "type": "size"}, "$card-footer-background-color": {"name": "$card-footer-background-color", "value": "transparent", "type": "string"}, "$card-footer-border-top": {"name": "$card-footer-border-top", "value": "1px solid $border-light", "type": "size"}, "$card-footer-padding": {"name": "$card-footer-padding", "value": "0.75rem", "type": "size"}, "$card-media-margin": {"name": "$card-media-margin", "value": "$block-spacing", "type": "variable", "computed_type": "size", "computed_value": "1.5rem"}}, "list": ["$card-color", "$card-background-color", "$card-shadow", "$card-radius", "$card-header-background-color", "$card-header-color", "$card-header-padding", "$card-header-shadow", "$card-header-weight", "$card-content-background-color", "$card-content-padding", "$card-footer-background-color", "$card-footer-border-top", "$card-footer-padding", "$card-media-margin"], "file_path": "components/card.sass"}