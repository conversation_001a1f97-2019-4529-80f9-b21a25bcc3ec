{"by_id": {"home": {"name": "Home", "path": "/"}, "documentation": {"name": "Documentation", "subtitle": "Everything you need to <strong>create a website</strong> with <PERSON><PERSON><PERSON>", "path": "/documentation/"}, "become-sponsor": {"name": "Become a Bulma sponsor", "subtitle": "Sponsor Bulma and Open Source via <a href=\"https://www.patreon.com/jgthms\" target=\"_blank\">Patreon</a> and <a href=\"https://github.com/sponsors/jgthms\" target=\"_blank\">GitHub</a>", "path": "/become-a-bulma-sponsor/"}, "bulma-book": {"name": "The official Bulma book", "title": "Book", "subtitle": "A step-by-step guide that teaches you how to build a web interface from scratch using Bulma", "icon": "bookmark", "color": "bleeding", "path": "/the-official-bulma-book/"}, "overview": {"name": "Overview", "subtitle": "An overview of what <PERSON><PERSON><PERSON> as a <strong>framework</strong> is all about", "color": "primary", "icon": "eye", "path": "/documentation/overview/"}, "overview-start": {"name": "Start", "subtitle": "You only need <strong>1 CSS file</strong> to use Bulma", "color": "danger", "icon": "rocket", "path": "/documentation/overview/start/"}, "overview-customize": {"name": "Customize", "subtitle": "Create your <strong>own theme</strong> with a simple set of <strong>variables</strong>", "color": "purple", "icon": "paint-brush", "path": "/documentation/overview/customize/"}, "overview-classes": {"name": "CSS classes", "subtitle": "Bulma is simply a <strong>collection</strong> of CSS classes. Write the HTML code you want.", "color": "link", "icon_brand": "true", "icon": "css3", "path": "/documentation/overview/classes/"}, "overview-modular": {"name": "Modularity", "subtitle": "Just import what you <strong>need</strong>", "color": "success", "icon": "cubes", "path": "/documentation/overview/modular/"}, "overview-responsiveness": {"name": "Responsiveness", "subtitle": "Bulma is a <strong>mobile-first</strong> framework", "color": "primary", "icon": "arrows-alt-h", "path": "/documentation/overview/responsiveness/"}, "overview-variables": {"name": "Variables", "subtitle": "See how <PERSON><PERSON><PERSON> uses <strong>Sass variables</strong> to allow easy customization", "color": "grey", "icon": "cogs", "path": "/documentation/overview/variables/"}, "overview-colors": {"name": "Colors", "subtitle": "The <strong>colors</strong> that <strong>style</strong> most Bulma elements and components", "color": "info", "icon": "tint", "path": "/documentation/overview/colors/"}, "utilities": {"name": "Utilities", "subtitle": "Sass tools used by <PERSON><PERSON><PERSON> and available for you", "color": "primary", "icon": "tools", "path": "/documentation/utilities/"}, "utilities-functions": {"name": "Functions", "subtitle": "Utility functions to calculate colors and other values", "color": "orange", "icon": "code", "path": "/documentation/utilities/functions/"}, "utilities-mixins": {"name": "Mixins", "subtitle": "Utility mixins for custom elements and other CSS helpers", "color": "purple", "icon": "equals", "path": "/documentation/utilities/mixins/"}, "utilities-responsive-mixins": {"name": "Responsive mixins", "subtitle": "Mixins that allows you to define different styles for each screen size", "color": "purple", "icon": "arrows-alt-h", "path": "/documentation/utilities/responsive-mixins/"}, "utilities-extends": {"name": "Extends", "subtitle": "Sass extends to keep your CSS code DRY", "color": "purple", "icon": "percentage", "path": "/documentation/utilities/extends/"}, "utilities-control-mixins": {"name": "Form Control mixins", "subtitle": "Mixins for <PERSON><PERSON><PERSON>'s buttons and form controls", "color": "purple", "icon": "hand-pointer", "icon_regular": "true", "path": "/documentation/utilities/control-mixins/"}, "customize": {"name": "Customize", "subtitle": "Create your <strong>own theme</strong> with a simple set of <strong>variables</strong>", "color": "purple", "icon": "paint-brush", "path": "/documentation/customize/"}, "customize-concepts": {"name": "Concepts", "subtitle": "What makes <PERSON><PERSON><PERSON> <strong>customizable</strong>", "color": "info", "icon": "lightbulb", "path": "/documentation/customize/concepts/"}, "customize-variables": {"name": "Variables", "subtitle": "See how <PERSON><PERSON><PERSON> uses <strong>Sass variables</strong> to allow easy customization", "color": "grey", "icon": "cogs", "path": "/documentation/customize/variables/"}, "customize-node-sass": {"name": "With node-sass", "subtitle": "Use npm/yarn and node-sass", "color": "danger", "icon_brand": "true", "icon": "npm", "path": "/documentation/customize/with-node-sass/"}, "customize-sass-cli": {"name": "With Sass CLI", "subtitle": "Use the Sass command line", "color": "purple", "icon_brand": "true", "icon": "sass", "path": "/documentation/customize/with-sass-cli/"}, "customize-webpack": {"name": "With webpack", "subtitle": "Use Bulma with webpack", "color": "warning", "icon_brand": "true", "icon": "js", "path": "/documentation/customize/with-webpack/"}, "modifiers": {"name": "Modifiers", "subtitle": "An <strong>easy-to-read</strong> naming system designed for humans", "color": "grey", "icon": "cogs", "path": "/documentation/modifiers/"}, "overview-modifiers": {"name": "Modifiers syntax", "subtitle": "Most Bulma elements have <strong>alternative</strong> styles. To apply them, you only need to append one of the <strong>modifier classes</strong>. They all start with <code>is-</code> or <code>has-</code>.", "color": "orange", "icon": "code", "path": "/documentation/overview/modifiers/"}, "modifiers-helpers": {"name": "Helpers", "subtitle": "Apply <strong>helper classes</strong> to almost any element, in order to alter its style", "color": "danger", "icon": "medkit", "path": "/documentation/modifiers/helpers/"}, "modifiers-responsive-helpers": {"name": "Responsive helpers", "subtitle": "<strong>Show/hide content</strong> depending on the width of the viewport", "color": "primary", "icon": "arrows-alt-h", "path": "/documentation/modifiers/responsive-helpers/"}, "modifiers-color-helpers": {"name": "Color helpers", "subtitle": "Change the <strong>color</strong> of the text and/or background", "color": "info", "icon": "tint", "path": "/documentation/modifiers/color-helpers/"}, "modifiers-typography-helpers": {"name": "Typography helpers", "subtitle": "Change the <strong>size</strong> and <strong>color</strong> of the text for one or multiple viewport width", "color": "grey-dark", "icon": "font", "path": "/documentation/modifiers/typography-helpers/"}, "helpers": {"name": "Helpers", "subtitle": "Apply <strong>helper classes</strong> to almost any element, in order to alter their style", "color": "danger", "icon": "medkit", "path": "/documentation/helpers/"}, "helpers-color": {"name": "Color", "subtitle": "Change the <strong>color</strong> of the text and/or background", "color": "info", "icon": "tint", "path": "/documentation/helpers/color-helpers/"}, "helpers-typography": {"name": "Typography", "subtitle": "Change the <strong>size</strong>, weight, and other font properties of the text", "color": "grey-dark", "icon": "font", "path": "/documentation/helpers/typography-helpers/"}, "helpers-spacing": {"name": "Spacing", "subtitle": "Change the <strong>size</strong> and <strong>color</strong> of the text for one or multiple viewport width", "color": "grey-dark", "icon": "arrows-alt-h", "path": "/documentation/helpers/spacing-helpers/"}, "helpers-visibility": {"name": "Visibility", "subtitle": "<strong>Show/hide content</strong> depending on the width of the viewport", "color": "primary", "icon": "eye", "path": "/documentation/helpers/visibility-helpers/"}, "helpers-flexbox": {"name": "Flexbox", "subtitle": "Helpers for all <strong>Flexbox</strong> properties", "color": "primary", "icon": "ellipsis-h", "path": "/documentation/helpers/flexbox-helpers/"}, "helpers-other": {"name": "Other", "subtitle": "Other useful Bulma helpers", "color": "primary", "icon": "medkit", "path": "/documentation/helpers/other-helpers/"}, "columns": {"name": "Columns", "subtitle": "The power of <strong>Flexbox</strong> in a simple interface", "color": "star", "icon": "columns", "path": "/documentation/columns/"}, "columns-basics": {"name": "Basics", "subtitle": "A simple way to build <strong>responsive columns</strong>", "color": "star", "icon": "columns", "path": "/documentation/columns/basics/"}, "columns-sizes": {"name": "Sizes", "subtitle": "Define the <strong>size</strong> of each column <strong>individually</strong>", "color": "success", "icon": "expand-arrows-alt", "path": "/documentation/columns/sizes/"}, "columns-responsiveness": {"name": "Responsiveness", "subtitle": "Handle <strong>different</strong> column layouts for each <strong>breakpoint</strong>", "color": "primary", "icon": "arrows-alt-h", "path": "/documentation/columns/responsiveness/"}, "columns-nesting": {"name": "Nesting", "subtitle": "A simple way to build <strong>responsive columns</strong>", "color": "danger", "icon": "sitemap", "path": "/documentation/columns/nesting/"}, "columns-gap": {"name": "Gap", "subtitle": "Customize the <strong>gap</strong> between the columns", "color": "info", "icon": "pause", "path": "/documentation/columns/gap/"}, "columns-options": {"name": "Options", "subtitle": "Design different <strong>types</strong> of column layouts", "color": "grey", "icon": "cogs", "path": "/documentation/columns/options/"}, "layout": {"name": "Layout", "subtitle": "Design the <strong>structure</strong> of your webpage with these CSS classes", "color": "success", "icon": "warehouse", "path": "/documentation/layout/"}, "layout-container": {"name": "Container", "subtitle": "A simple <strong>container</strong> to center your content horizontally", "path": "/documentation/layout/container/", "icon": "arrows-alt-h"}, "layout-level": {"name": "Level", "subtitle": "A multi-purpose <strong>horizontal level</strong>, which can contain almost any other element", "path": "/documentation/layout/level/", "icon": "ruler-horizontal"}, "layout-media": {"name": "Media Object", "subtitle": "The famous <strong>media object</strong> prevalent in social media interfaces, but useful in any context", "path": "/documentation/layout/media-object/", "icon": "th-list"}, "layout-hero": {"name": "Hero", "subtitle": "An imposing <strong>hero banner</strong> to showcase something", "path": "/documentation/layout/hero/", "icon": "star"}, "layout-section": {"name": "Section", "subtitle": "A simple container to divide your page into <strong>sections</strong>, like the one you're currently reading", "path": "/documentation/layout/section/", "icon": "square"}, "layout-footer": {"name": "Footer", "subtitle": "A simple responsive <strong>footer</strong> which can include anything: lists, headings, columns, icons, buttons, etc.", "path": "/documentation/layout/footer/", "icon": "window-minimize"}, "layout-tiles": {"name": "Tiles", "subtitle": "A <strong>single tile</strong> element to build 2-dimensional Metro-like, Pinterest-like, or whatever-you-like grids", "path": "/documentation/layout/tiles/", "icon": "th"}, "form": {"name": "Form", "subtitle": "The indispensable <strong>form controls</strong>, designed for maximum clarity", "color": "link", "icon_brand": "true", "icon": "wpforms", "path": "/documentation/form/"}, "form-general": {"name": "General", "subtitle": "All generic <strong>form controls</strong>, designed for consistency", "color": "link", "icon": "keyboard", "icon_regular": "true", "path": "/documentation/form/general/"}, "form-input": {"name": "Input", "subtitle": "The <strong>text input</strong> and its variations", "color": "link", "icon": "minus", "path": "/documentation/form/input/"}, "form-textarea": {"name": "Textarea", "subtitle": "The multiline <strong>textarea</strong> and its variations", "color": "link", "icon": "square", "path": "/documentation/form/textarea/"}, "form-select": {"name": "Select", "subtitle": "The browser built-in <strong>select dropdown</strong>, styled accordingly", "color": "link", "icon": "mouse-pointer", "path": "/documentation/form/select/"}, "form-checkbox": {"name": "Checkbox", "subtitle": "The 2-state <strong>checkbox</strong> in its native format", "color": "link", "icon": "check-square", "path": "/documentation/form/checkbox/"}, "form-radio": {"name": "Radio", "subtitle": "The mutually exclusive <strong>radio buttons</strong> in their native format", "color": "link", "icon": "dot-circle", "path": "/documentation/form/radio/"}, "form-file": {"name": "File", "subtitle": "A custom <strong>file upload</strong> input, without JavaScript", "color": "link", "icon": "cloud-upload-alt", "path": "/documentation/form/file/"}, "elements": {"name": "Elements", "subtitle": "Essential interface elements that only require a <strong>single CSS class</strong>", "color": "orange", "icon": "cube", "path": "/documentation/elements/"}, "elements-block": {"name": "Block", "subtitle": "<PERSON><PERSON><PERSON>'s most basic spacer <strong>block</strong>", "color": "grey", "icon": "arrows-alt-v", "path": "/documentation/elements/block/"}, "elements-box": {"name": "Box", "subtitle": "A white <strong>box</strong> to contain other elements", "color": "grey", "icon": "square", "icon_regular": "true", "path": "/documentation/elements/box/"}, "elements-button": {"name": "<PERSON><PERSON>", "subtitle": "The classic <strong>button</strong>, in different colors, sizes, and states", "color": "success", "icon": "hand-pointer", "icon_regular": "true", "path": "/documentation/elements/button/"}, "elements-content": {"name": "Content", "subtitle": "A single class to handle <strong>WYSIWYG</strong> generated content, where only <strong>HTML tags</strong> are available", "color": "primary", "icon": "align-left", "path": "/documentation/elements/content/"}, "elements-delete": {"name": "Delete", "subtitle": "A versatile <strong>delete</strong> cross", "color": "danger", "icon": "times-circle", "path": "/documentation/elements/delete/"}, "elements-icon": {"name": "Icon", "subtitle": "Compatible with all icon font libraries, including <strong>Font Awesome 5</strong>", "icon": "font-awesome", "icon_brand": "true", "path": "/documentation/elements/icon/"}, "elements-image": {"name": "Image", "subtitle": "A container for <strong>responsive images</strong>", "color": "purple", "icon": "image", "path": "/documentation/elements/image/"}, "elements-notification": {"name": "Notification", "subtitle": "Bold <strong>notification</strong> blocks, to alert your users of something", "color": "orange", "icon": "exclamation-triangle", "path": "/documentation/elements/notification/"}, "elements-progress": {"name": "Progress bars", "subtitle": "Native HTML <strong>progress</strong> bars", "color": "warning", "icon": "spinner", "path": "/documentation/elements/progress/"}, "elements-table": {"name": "Table", "subtitle": "The inevitable HTML <strong>table</strong>, with special case cells", "color": "info", "icon": "table", "path": "/documentation/elements/table/"}, "elements-tag": {"name": "Tag", "subtitle": "Small <strong>tag labels</strong> to insert anywhere", "color": "success", "icon": "tag", "path": "/documentation/elements/tag/"}, "elements-title": {"name": "Title", "subtitle": "Simple <strong>headings</strong> to add depth to your page", "color": "black", "icon": "heading", "path": "/documentation/elements/title/"}, "components": {"name": "Components", "subtitle": "Advanced multi-part components with lots of possibilities", "color": "danger", "icon": "cubes", "path": "/documentation/components/"}, "components-breadcrumb": {"name": "Breadcrumb", "subtitle": "A simple <strong>breadcrumb</strong> component to improve your navigation experience", "color": "star", "icon": "ellipsis-h", "path": "/documentation/components/breadcrumb/"}, "components-card": {"name": "Card", "subtitle": "An all-around flexible and composable component", "color": "success", "icon": "id-card", "path": "/documentation/components/card/"}, "components-dropdown": {"name": "Dropdown", "subtitle": "An interactive <strong>dropdown menu</strong> for discoverable content", "color": "success", "icon": "angle-down", "path": "/documentation/components/dropdown/"}, "components-menu": {"name": "<PERSON><PERSON>", "subtitle": "A simple <strong>menu</strong>, for any type of vertical navigation", "icon": "bars", "path": "/documentation/components/menu/"}, "components-message": {"name": "Message", "subtitle": "Colored <strong>message</strong> blocks, to emphasize part of your page", "color": "info", "icon": "window-maximize", "icon_regular": "true", "path": "/documentation/components/message/"}, "components-modal": {"name": "Modal", "subtitle": "A classic <strong>modal</strong> overlay, in which you can include <em>any</em> content you want", "color": "danger", "icon": "clone", "path": "/documentation/components/modal/"}, "components-navbar": {"name": "<PERSON><PERSON><PERSON>", "subtitle": "A responsive horizontal <strong>navbar</strong> that can support images, links, buttons, and dropdowns", "color": "primary", "icon": "minus", "path": "/documentation/components/navbar/"}, "components-pagination": {"name": "Pagination", "subtitle": "A responsive, usable, and flexible <strong>pagination</strong>", "color": "orange", "icon": "caret-square-right", "path": "/documentation/components/pagination/"}, "components-panel": {"name": "Panel", "subtitle": "A composable <strong>panel</strong>, for compact controls", "color": "grey-dark", "icon": "list-alt", "icon_regular": "true", "path": "/documentation/components/panel/"}, "components-tabs": {"name": "Tabs", "subtitle": "Simple responsive horizontal navigation <strong>tabs</strong>, with different styles", "color": "purple", "icon": "folder", "path": "/documentation/components/tabs/"}, "videos": {"name": "Videos", "color": "success", "icon": "play-circle", "title": "Videos", "path": "/videos/"}, "blog": {"name": "Blog", "title": "Blog", "subtitle": "Stay updated with new features", "color": "rss", "icon": "rss", "path": "/blog/"}, "brand": {"name": "Brand", "title": "Brand", "subtitle": "The official Bulma logos", "color": "primary", "icon": "medal", "path": "/brand/"}, "expo": {"name": "Expo", "title": "Expo", "subtitle": "Official Bulma showcase", "icon": "star", "color": "expo", "path": "/expo/"}, "love": {"name": "Love", "title": "Love", "subtitle": "Fans of Bulma around the world", "color": "love", "icon": "heart", "path": "/love/"}, "backers": {"id": "backers", "name": "Backers", "title": "Backers", "color": "patreon", "icon": "patreon", "path": "/backers/", "icon_brand": true, "subtitle": "Everyone who is supporting <PERSON><PERSON><PERSON> via Patreon and GitHub", "description": "Everyone who is supporting <PERSON><PERSON><PERSON>"}, "more": {"name": "More", "path": "/more/"}, "bulma-start": {"name": "<PERSON><PERSON><PERSON> start", "title": "Start", "subtitle": "A tiny npm package to get started", "icon": "rocket", "color": "success", "path": "/bulma-start/"}, "made-with-bulma": {"name": "Made with Bulma", "title": "Badge", "subtitle": "The official community badge", "icon": "certificate", "color": "warning", "path": "/made-with-bulma/"}, "bootstrap": {"name": "Coming from Bootstrap", "subtitle": "See how Bulma is an alternative to Bootstrap", "icon": "exchange-alt", "color": "bootstrap", "path": "/alternative-to-bootstrap/"}, "patreon-backers": {"name": "<PERSON><PERSON><PERSON> and GitHub backers", "subtitle": "Everyone who is supporting <PERSON><PERSON><PERSON>", "icon_brand": "true", "icon": "patreon", "color": "patreon", "path": "/backers/"}, "extensions": {"name": "Bulma extensions", "title": "Extensions", "subtitle": "Side projects to enhance Bulma", "icon": "plug", "color": "extensions", "path": "/extensions/"}}, "navbar": ["videos", "expo"], "navbarMore": ["love", "backers", "extensions", "bulma-book", "blog", "brand", "bulma-start", "made-with-bulma"], "more": ["love", "backers", "extensions", "bulma-book", "blog", "brand", "bulma-start", "made-with-bulma", "bootstrap"], "categoryIds": ["overview", "customize", "utilities", "columns", "elements", "components", "form", "layout", "helpers"], "categories": {"overview": ["overview-start", "overview-classes", "overview-modifiers", "overview-modular", "overview-responsiveness", "overview-colors"], "utilities": ["utilities-mixins", "utilities-responsive-mixins", "utilities-control-mixins", "utilities-functions", "utilities-extends"], "customize": ["customize-concepts", "customize-variables", "customize-node-sass", "customize-sass-cli", "customize-webpack"], "helpers": ["helpers-color", "helpers-spacing", "helpers-typography", "helpers-visibility", "helpers-flexbox", "helpers-other"], "columns": ["columns-basics", "columns-sizes", "columns-responsiveness", "columns-nesting", "columns-gap", "columns-options"], "layout": ["layout-container", "layout-level", "layout-media", "layout-hero", "layout-section", "layout-footer", "layout-tiles"], "form": ["form-general", "form-input", "form-textarea", "form-select", "form-checkbox", "form-radio", "form-file"], "elements": ["elements-block", "elements-box", "elements-button", "elements-content", "elements-delete", "elements-icon", "elements-image", "elements-notification", "elements-progress", "elements-table", "elements-tag", "elements-title"], "components": ["components-breadcrumb", "components-card", "components-dropdown", "components-menu", "components-message", "components-modal", "components-navbar", "components-pagination", "components-panel", "components-tabs"]}}