{"removed": [{"file": "sass/components/message.sass", "before": "$message-body-border", "after": ["$message-body-border-color", "$message-body-border-width"]}], "updated": [{"file": "sass/utilities/initial-variables.sass", "changes": [{"variable": "$gap", "from": "32px", "to": "64px"}, {"variable": "$radius", "from": "3px", "to": "4px"}, {"variable": "$radius-large", "from": "5px", "to": "6px"}]}, {"file": "sass/base/generic.sass", "changes": [{"variable": "$hr-background-color", "from": "$border", "to": "$background"}, {"variable": "$hr-height", "from": "1px", "to": "2px"}]}, {"file": "sass/elements/content.sass", "changes": [{"variable": "$content-heading-weight", "from": "$weight-normal", "to": "$weight-semibold"}]}, {"file": "sass/components/message.sass", "changes": [{"variable": "$message-header-padding", "from": "0.5em 0.75em", "to": "0.75em 1em"}, {"variable": "$message-body-padding", "from": "1em 1.25em", "to": "1.25em 1.5em"}]}, {"file": "sass/components/navbar.sass", "changes": [{"variable": "$navbar-item-hover-background-color", "from": "$background", "to": "$white-bis"}, {"variable": "$navbar-dropdown-border-top", "from": "1px solid $border", "to": "2px solid $border"}, {"variable": "$navbar-divider-background-color", "from": "$border", "to": "$background"}]}, {"file": "sass/layout/footer.sass", "changes": [{"variable": "$footer-background-color", "from": "$background", "to": "$white-bis"}]}], "new": [{"file": "sass/components/breadcrumb.sass", "newcomers": [{"name": "$breadcrumb-item-padding-vertical", "value": "0"}, {"name": "$breadcrumb-item-padding-horizontal", "value": "0.75em"}]}, {"file": "sass/components/message.sass", "newcomers": [{"name": "$message-body-border-color", "value": "$border"}, {"name": "$message-body-border-width", "value": "0 0 0 4px"}, {"name": "$message-header-weight", "value": "$weight-bold"}, {"name": "$message-header-body-border-width", "value": "0"}]}, {"file": "sass/components/navbar.sass", "newcomers": [{"name": "$navbar-box-shadow-size", "value": "0 2px 0 0"}, {"name": "$navbar-box-shadow-color", "value": "$background"}, {"name": "$navbar-padding-vertical", "value": "1rem"}, {"name": "$navbar-padding-horizontal", "value": "2rem"}, {"name": "$navbar-z", "value": "30"}]}, {"file": "sass/elements/title.sass", "newcomers": [{"name": "$title-line-height", "value": "1.125"}, {"name": "$subtitle-line-height", "value": "1.25"}, {"name": "$subtitle-negative-margin", "value": "-1.25rem"}]}]}