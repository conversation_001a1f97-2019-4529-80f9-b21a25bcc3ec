{
  "navbar_items": [
    {
      "id": "videos",
      "color": "has-text-success",
      "fa_type": "fas",
      "fa_icon": "fa-play-circle",
      "title": "Videos"
    },
    {
      "id": "expo",
      "color": "has-text-star",
      "fa_type": "fas",
      "fa_icon": "fa-star",
      "title": "Expo"
    },
    {
      "id": "love",
      "color": "has-text-danger",
      "fa_type": "fas",
      "fa_icon": "fa-heart",
      "title": "Love"
    },
    {
      "id": "blog",
      "color": "bd-has-text-rss",
      "fa_type": "fas",
      "fa_icon": "fa-rss",
      "title": "Blog"
    }
  ],
  "other_items": [
    {
      "id": "templates",
      "color": "has-text-info",
      "fa_type": "fas",
      "fa_icon": "fa-code",
      "title": "Templates"
    },
    {
      "id": "recipes",
      "color": "has-text-purple",
      "fa_type": "fas",
      "fa_icon": "fa-utensils",
      "title": "Recipes"
    },
  ],
  "more_items": [
    {
      "id": "bulma-start",
      "color": "success",
      "fa_type": "fas",
      "fa_icon": "fa-rocket",
      "title": "Bulma start",
      "description": "A tiny npm package to get started"
    },
    {
      "id": "made-with-bulma",
      "color": "primary",
      "fa_type": "fas",
      "fa_icon": "fa-certificate",
      "title": "Made with Bulma",
      "description": "The official community badge"
    },
    {
      "id": "alternative-to-bootstrap",
      "color": "bootstrap",
      "fa_type": "fas",
      "fa_icon": "fa-exchange-alt",
      "title": "Coming from Bootstrap",
      "description": "See how Bulma is an alternative to Bootstrap"
    },
    {
      "id": "backers",
      "color": "patreon",
      "fa_type": "fab",
      "fa_icon": "fa-patreon",
      "title": "Patreon and GitHub backers",
      "description": "Everyone who is supporting Bulma"
    },
    {
      "id": "extensions",
      "color": "danger",
      "fa_type": "fas",
      "fa_icon": "fa-plug",
      "title": "Extensions",
      "description": "Side projects to enhance Bulma",
    }
  ]
}
