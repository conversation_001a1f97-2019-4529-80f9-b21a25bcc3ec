{"tweets_by_id": {"779966186121560064": {"id": "779966186121560064", "date": "9:50 AM - 25 Sep 2016", "content": "Thanks <a href=\"https://twitter.com/jgthms\">@jgthms</a> for updating my favourite css framework <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a>", "fullname": "<PERSON><PERSON><PERSON>", "username": "juampe84", "avatar": "https://pbs.twimg.com/profile_images/986537531251535877/sYSfyjrk_normal.jpg", "hearts": 3, "retweets": 0}, "783630950718504960": {"id": "783630950718504960", "date": "12:32 PM - 5 Oct 2016", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> love your work on bulma, you rock", "fullname": "<PERSON>", "username": "and<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1342762988168642560/TebKmzQS_normal.jpg", "hearts": 2, "retweets": 0}, "808825432233558016": {"id": "808825432233558016", "date": "12:06 AM - 14 Dec 2016", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> <PERSON><PERSON><PERSON> is badass. Nice work all around!", "fullname": "Shai Creates", "username": "shaicreates", "avatar": "https://pbs.twimg.com/profile_images/1367146951091707904/AvPBWooo_normal.jpg", "hearts": 1, "retweets": 0}, "819710615337857024": {"id": "819710615337857024", "date": "1:00 AM - 13 Jan 2017", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> Hey.I just stopped by here to say thank you so much for <PERSON><PERSON><PERSON>. It made my website looks 100 times better 😍", "fullname": "juliooooo", "username": "juliooooo000", "avatar": "https://pbs.twimg.com/profile_images/1081851255087976448/RIPxivo3_normal.jpg", "hearts": 1, "retweets": 0}, "831866770755579904": {"id": "831866770755579904", "date": "2:04 PM - 15 Feb 2017", "content": "Jeremys reference sites are just so friendly. Love them. <a href=\"https://t.co/FhNs944XH6\">twitter.com/jgthms/status/…</a>", "fullname": "Ville V<PERSON>", "username": "sakamies", "avatar": "https://pbs.twimg.com/profile_images/816420698017189889/nuT038KN_normal.jpg", "hearts": 1, "retweets": 0}, "834030605847326726": {"id": "834030605847326726", "date": "1:22 PM - 21 Feb 2017", "content": "Reply to <a href=\"https://twitter.com/jgthms\">@jgthms</a> Your framework is very good and lightweight compared to Twitter Bootstrap. I really like it.", "fullname": "<PERSON><PERSON> 🇵🇭", "username": "raniesantos32", "avatar": "https://pbs.twimg.com/profile_images/1260712159543091203/a-bqsYQp_normal.jpg", "hearts": 2, "retweets": 0}, "834140257054502913": {"id": "834140257054502913", "date": "8:38 PM - 21 Feb 2017", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> Hey <PERSON>! Been putting <PERSON><PERSON><PERSON> into a project during the last days - absolutely love it! Well done! 😍", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/794696494091792384/V5WGxQTk_normal.jpg", "hearts": 1, "retweets": 0}, "835834634655174658": {"id": "835834634655174658", "date": "12:51 PM - 26 Feb 2017", "content": "<a href=\"https://t.co/ClYmBd8tGR\">bulma.io</a> is an impressive CSS framework.  Flexbox grid, no JS, modular components (use what you need), Sass.<br><br>💯  <a href=\"https://twitter.com/jgthms\">@jgthms</a>.", "fullname": "scottgallant", "username": "scottgallant", "avatar": "https://pbs.twimg.com/profile_images/1400472871735005184/8j-O4tW3_normal.jpg", "hearts": 19, "retweets": 1}, "839423865205977088": {"id": "839423865205977088", "date": "10:33 AM - 8 Mar 2017", "content": ".<a href=\"https://twitter.com/jgthms\">@jgthms</a> I've just discovered your suite of tools for web development: bulma.io &amp; cssreference.io among others. Fabulous work - thanks!", "fullname": "<PERSON>", "username": "vicver82", "avatar": "https://pbs.twimg.com/profile_images/778591376640471040/pm6XotFO_normal.jpg", "hearts": 2, "retweets": 0}, "857590406724243456": {"id": "857590406724243456", "date": "2:41 PM - 27 Apr 2017", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> <PERSON> Bulma! This is making my day right now. Imported it into the starter theme I'm using.", "fullname": "<PERSON>", "username": "CarlisleStewart", "avatar": "https://pbs.twimg.com/profile_images/846005263693852672/f6r2OsA1_normal.jpg", "hearts": 1, "retweets": 0}, "860885116909998080": {"id": "860885116909998080", "date": "4:53 PM - 6 May 2017", "content": "<a href=\"https://t.co/vY1ZsRScYM\">bulma.io</a> -- a CSS framework by <a href=\"https://twitter.com/jgthms\">@jgthms</a> -- is lovely! No JS included, which means no JS opinions included!", "fullname": "<PERSON>", "username": "RobStuttaford", "avatar": "https://pbs.twimg.com/profile_images/1425117443417051138/c_4F4BFo_normal.jpg", "hearts": 115, "retweets": 27}, "862586112770023425": {"id": "862586112770023425", "date": "9:32 AM - 11 May 2017", "content": "Simple, beautiful and most importantly, very light. Bulma: a modern CSS framework based on Flexbox <a href=\"https://t.co/uv6JF2dDGJ\">bulma.io</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1369544677700087808/8fWPfoPu_normal.jpg", "hearts": 1, "retweets": 0}, "868730725926711296": {"id": "868730725926711296", "date": "8:28 AM - 28 May 2017", "content": "Just found <a href=\"https://twitter.com/jgthms\">@jgthms</a>'s <a href=\"https://t.co/sa8Hy25rDH\">bulma.io</a>, an amazing css framework. Officially switching all new front-end work to this, this is amazing", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/492886054232289280/z7Ha8I3o_normal.jpeg", "hearts": 1, "retweets": 0}, "869284735440363520": {"id": "869284735440363520", "date": "9:10 PM - 29 May 2017", "content": "<PERSON><PERSON><PERSON>, I think I'm in love. <a href=\"https://t.co/APYefmC1Bs\">bulma.io</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/663874141854961666/HXz-_UA4_normal.jpg", "hearts": 23, "retweets": 1}, "871163877622460417": {"id": "871163877622460417", "date": "1:37 AM - 4 Jun 2017", "content": "Having a lot of fun building a website with <a href=\"https://twitter.com/GoHugoIO\">@GoHugoIO</a> and <a href=\"https://twitter.com/jgthms\">@jgthms</a> 's bulma!", "fullname": "<PERSON>", "username": "___mna___", "avatar": "https://pbs.twimg.com/profile_images/560632881941188609/OR9pQ-sK_normal.jpeg", "hearts": 3, "retweets": 1}, "871410394622865408": {"id": "871410394622865408", "date": "5:56 PM - 4 Jun 2017", "content": "Redesigning a website with 500K+ visitors/yr, going to give a Bulma a try <a href=\"https://twitter.com/jgthms\">@jgthms</a> <a href=\"https://t.co/IcuGfvTQrI\">bulma.io</a>", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1480962423829893120/RUvQSGFs_normal.jpg", "hearts": 1, "retweets": 0}, "874200312378269696": {"id": "874200312378269696", "date": "10:42 AM - 12 Jun 2017", "content": "Bulma framework just made my weekend better :) <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON> 🛸", "username": "victor<PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1391679914483535877/E6C8-NMd_normal.jpg", "hearts": 1, "retweets": 0}, "874925154475929602": {"id": "874925154475929602", "date": "10:43 AM - 14 Jun 2017", "content": "I usually hate having to write CSS, but I really like working with the lightweight Bulma (<a href=\"https://t.co/OAMLjKWzak\">bulma.io</a>) by <a href=\"https://twitter.com/jgthms\">@jgthms</a> so far 👍", "fullname": "mario <PERSON>", "username": "mzupzup", "avatar": "https://pbs.twimg.com/profile_images/378800000350936693/8ce343d2944ad08a0935d0e485c82572_normal.png", "hearts": 10, "retweets": 1}, "875511410008219649": {"id": "875511410008219649", "date": "1:32 AM - 16 Jun 2017", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> <PERSON><PERSON><PERSON> might be <PERSON><PERSON><PERSON> killer. I'm really liking it.", "fullname": "<PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/378800000093659634/8e0f6f6bfe9858fe4c4be2d6975ba6e0_normal.jpeg", "hearts": 1, "retweets": 0}, "877010184463294465": {"id": "877010184463294465", "date": "4:48 AM - 20 Jun 2017", "content": "Really Appreciate <a href=\"https://twitter.com/jgthms\">@jgthms</a>. <br>As I beginner, because of bulma.io, I could write the code and I get self‐confidence! <br><a href=\"https://twitter.com/search?q=%23bulma\">#bulma</a> <a href=\"https://twitter.com/search?q=%23css\">#css</a> <a href=\"https://twitter.com/search?q=%23framework\">#framework</a>", "fullname": "byungwoo yoon｜Ubie Discovery", "username": "__bebold__", "avatar": "https://pbs.twimg.com/profile_images/1108751018529972224/hYiJCuW9_normal.png", "hearts": 1, "retweets": 0}, "877691760226562048": {"id": "877691760226562048", "date": "1:56 AM - 22 Jun 2017", "content": "Best Flexbox CSS Framework:<br>B<PERSON><PERSON> <a href=\"https://t.co/3BtGymZWMB\">bulma.io</a><br><br>Thank you <a href=\"https://twitter.com/jgthms\">@jgthms</a>, thank you.", "fullname": "SethDavis.tsx", "username": "sethdavis512", "avatar": "https://pbs.twimg.com/profile_images/1500300746247524353/TOUgHnXR_normal.jpg", "hearts": 1, "retweets": 2}, "877764422466322432": {"id": "877764422466322432", "date": "6:45 AM - 22 Jun 2017", "content": "<a href=\"https://twitter.com/rafaelpimpa\">@rafaelpimpa</a> <a href=\"https://twitter.com/vuejs\">@vuejs</a> <a href=\"https://twitter.com/jgthms\">@jgthms</a> Best framework on the web right now. Well done, please keep it up. World needs it!", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/946310289741381632/ofqWzQcd_normal.jpg", "hearts": 3, "retweets": 0}, "878220062007504897": {"id": "878220062007504897", "date": "12:55 PM - 23 Jun 2017", "content": "I just discovered <a href=\"https://t.co/5xcp1Bwfpz\">bulma.io</a> by <a href=\"https://twitter.com/jgthms\">@jgthms</a> - looks great. I'm gonna take it out for a test drive <a href=\"https://t.co/E5bbXjCD5H\">pic.twitter.com/E5bbXjCD5H</a>", "fullname": "<PERSON>", "username": "JeffKelleyBV", "avatar": "https://pbs.twimg.com/profile_images/1390356161891868677/ul-qTNo__normal.jpg", "hearts": 2, "retweets": 0}, "879294487301718016": {"id": "879294487301718016", "date": "12:05 PM - 26 Jun 2017", "content": "Been spending an inordinate amount of time playing w/ various css frameworks for my <a href=\"https://twitter.com/search?q=%23reactjs\">#reactjs</a> site. Settled on <a href=\"https://t.co/KoRlyeoaI1\">bulma.io</a>", "fullname": "<PERSON>", "username": "julian_currie", "avatar": "https://pbs.twimg.com/profile_images/1085475017247223810/3y5N7LeB_normal.jpg", "hearts": 2, "retweets": 2}, "879725003306147840": {"id": "879725003306147840", "date": "4:35 PM - 27 Jun 2017", "content": "This looks awesome. Espec. horiz. &amp; vert. alignments  Bulma: a modern CSS framework based on Flexbox : <a href=\"https://t.co/iF8LQye3TD\">bulma.io</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "Brentley Broughton", "username": "b<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1505997188383584269/V1W1ifR5_normal.jpg", "hearts": 2, "retweets": 1}, "880554577187266560": {"id": "880554577187266560", "date": "11:32 PM - 29 Jun 2017", "content": "today I've created a reasonable static website via bulma.io by <a href=\"https://twitter.com/jgthms\">@jgthms</a><br>I've used 3 little JS scripts to handle classes and it was smooth AF!", "fullname": "<PERSON> 🍥", "username": "WebReflection", "avatar": "https://pbs.twimg.com/profile_images/1266010551374798849/I0vP8ntb_normal.jpg", "hearts": 2, "retweets": 0}, "884443272948658176": {"id": "884443272948658176", "date": "5:04 PM - 10 Jul 2017", "content": "Updated my Bulma css framework to the latest and used the new Navbar! Good stuff! <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON> 🥨", "username": "3cordguy", "avatar": "https://pbs.twimg.com/profile_images/1463499022317015043/OTCPf4zn_normal.jpg", "hearts": 1, "retweets": 0}, "884449239291580416": {"id": "884449239291580416", "date": "5:28 PM - 10 Jul 2017", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> Thanks for the dropdowns man, I love 'em", "fullname": "<PERSON> 🍪", "username": "<PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1047076804580859905/vumzSqwR_normal.jpg", "hearts": 1, "retweets": 0}, "888602569148211203": {"id": "888602569148211203", "date": "4:32 AM - 22 Jul 2017", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> I got to know about <PERSON><PERSON><PERSON> today and love it already. Thank you for your work! Exactly I was looking for to build a landing page.", "fullname": "<PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1014086340093177856/T0vrq8_r_normal.jpg", "hearts": 1, "retweets": 0}, "889499262136045569": {"id": "889499262136045569", "date": "3:55 PM - 24 Jul 2017", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> <PERSON> using <PERSON><PERSON><PERSON> for my last <a href=\"https://twitter.com/rails\">@rails</a> project. What I like the most is that it shipped without js so it works easily with Turbolinks.", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1368994988684697612/hiy7lbbF_normal.jpg", "hearts": 2, "retweets": 1}, "892184654501076994": {"id": "892184654501076994", "date": "1:46 AM - 1 Aug 2017", "content": "in all honesty, i heart bulma simple and getting better <a href=\"https://t.co/YRrzTt9wew\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON><PERSON><PERSON>", "username": "itsscalla", "avatar": "https://pbs.twimg.com/profile_images/863915302043742209/ByYHVWQN_normal.jpg", "hearts": 3, "retweets": 0}, "892273691563896832": {"id": "892273691563896832", "date": "7:40 AM - 1 Aug 2017", "content": "<a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> makes me so happy. Thank you <a href=\"https://twitter.com/jgthms\">@jgthms</a>!", "fullname": "yokim", "username": "Yo<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1326065645084168192/L8LbleNg_normal.jpg", "hearts": 2, "retweets": 0}, "892362154610917378": {"id": "892362154610917378", "date": "1:31 PM - 1 Aug 2017", "content": "<a href=\"https://twitter.com/search?q=%23Bulma\">#Bulma</a> helps make front-end dev fun again :) Great job <a href=\"https://twitter.com/jgthms\">@jgthms</a>!", "fullname": "<PERSON>", "username": "ocrampete16", "avatar": "https://pbs.twimg.com/profile_images/892063521055420416/Y1PJ0K4Y_normal.jpg", "hearts": 1, "retweets": 0}, "892588073950773248": {"id": "892588073950773248", "date": "4:29 AM - 2 Aug 2017", "content": "Finally had some free time to start, but I've really been enjoying converting my personal site to <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> by <a href=\"https://twitter.com/jgthms\">@jgthms</a>. Can't wait to finish.", "fullname": "<PERSON>", "username": "afomera", "avatar": "https://pbs.twimg.com/profile_images/1466254583986987011/k0_fSd7X_normal.jpg", "hearts": 1, "retweets": 0}, "892691375715745792": {"id": "892691375715745792", "date": "11:19 AM - 2 Aug 2017", "content": "Well, because of <a href=\"https://twitter.com/jgthms\">@jgthms</a>, now all my websites are using <a href=\"https://twitter.com/search?q=%23bulmacss\">#bulmacss</a> Thank you &lt;3", "fullname": "<PERSON><PERSON> (Cadox8) 🔭🔭🔭", "username": "cadox8", "avatar": "https://pbs.twimg.com/profile_images/1441305268361437184/hlo-L686_normal.jpg", "hearts": 1, "retweets": 0}, "892798375753592836": {"id": "892798375753592836", "date": "6:24 PM - 2 Aug 2017", "content": "The new file input styles with <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> were introduced just before i thought i have to do it on my own. Perfect timing <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "ste_schwa", "avatar": "https://pbs.twimg.com/profile_images/1324413137299361792/dpIuAuqv_normal.jpg", "hearts": 1, "retweets": 0}, "893112719519481856": {"id": "893112719519481856", "date": "3:14 PM - 3 Aug 2017", "content": "Actually making my own website easily &amp; entirely with <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> Thanks <a href=\"https://twitter.com/jgthms\">@jgthms</a> for a great framework <a href=\"https://t.co/27dMWIozmi\">bulma.io</a>", "fullname": "💻El Aguacate Programador🥑", "username": "ch1nux", "avatar": "https://pbs.twimg.com/profile_images/1491953029871984650/_AeOKeRK_normal.jpg", "hearts": 2, "retweets": 1}, "893176202210553857": {"id": "893176202210553857", "date": "7:26 PM - 3 Aug 2017", "content": "Decided to use <PERSON><PERSON><PERSON> on this project I’m working on. I am happy to use it and happy to see the docs in tact too. Great work <a href=\"https://twitter.com/jgthms\">@jgthms</a> !", "fullname": "<PERSON>", "username": "jay_wilburn", "avatar": "https://pbs.twimg.com/profile_images/1092186785160015872/0agOh_Wz_normal.jpg", "hearts": 1, "retweets": 0}, "893193878685220864": {"id": "893193878685220864", "date": "8:36 PM - 3 Aug 2017", "content": "Relaunched my personal site with <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a>. The best css framework I've used yet. Thanks <a href=\"https://twitter.com/jgthms\">@jgthms</a> 🤜", "fullname": "L A N C E", "username": "<PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/710909268333977600/9f3zOCBo_normal.jpg", "hearts": 1, "retweets": 0}, "893410677196378112": {"id": "893410677196378112", "date": "10:57 AM - 4 Aug 2017", "content": "Working on upgrading a site,replaced over 70 bootstrap rows/col-xs/md/lg-* etc with a few bulma classes for the same result <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "Waffy Wafs", "username": "wafiks", "avatar": "https://pbs.twimg.com/profile_images/3475223564/3772a121676f6089f3302d2a7213f56c_normal.jpeg", "hearts": 1, "retweets": 0}, "893820545141280769": {"id": "893820545141280769", "date": "2:06 PM - 5 Aug 2017", "content": "Bulma.io, The best css framework currently. Thank you <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/302344885/GEN_normal.png", "hearts": 2, "retweets": 0}, "894338592796966912": {"id": "894338592796966912", "date": "12:25 AM - 7 Aug 2017", "content": "Amazing CSS framework! Best I've worked with so far. Excellent integration with VueJS <a href=\"https://t.co/kLAkDGnppl\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON><PERSON>", "username": "eaguilar_88", "avatar": "https://pbs.twimg.com/profile_images/434101858416992256/9_dtzVoX_normal.jpeg", "hearts": 2, "retweets": 0}, "894941805208158213": {"id": "894941805208158213", "date": "4:22 PM - 8 Aug 2017", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> <a href=\"https://twitter.com/LottieFiles\">@LottieFiles</a> Working on making <a href=\"https://twitter.com/LottieFiles\">@LottieFiles</a> even more prettier.  <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> is amazing. Love how small and simple it is.  <a href=\"https://twitter.com/search?q=%23bulmacss\">#bulmacss</a>", "fullname": "<PERSON><PERSON> 🇲🇻", "username": "<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1360267204818653184/2xzJY9w6_normal.jpg", "hearts": 1, "retweets": 0}, "896030502544707584": {"id": "896030502544707584", "date": "4:28 PM - 11 Aug 2017", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> absolutely love what you've done with the documentation in latest releases of bulma!! 👌", "fullname": "<PERSON><PERSON><PERSON><PERSON>", "username": "sobers_2002", "avatar": "https://pbs.twimg.com/profile_images/1242538857981079553/TCq2Bz5x_normal.jpg", "hearts": 1, "retweets": 0}, "896363657029980160": {"id": "896363657029980160", "date": "2:32 PM - 12 Aug 2017", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> using <a href=\"https://twitter.com/search?q=%23bulma\">#bulma</a> on side project. Remarkable how this little beauty goes out of your way! Use, modify, extend. No problem! <a href=\"https://twitter.com/search?q=%23IAmInLove\">#IAmInLove</a> ❤️", "fullname": "<PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/738064268252348416/97wLt75g_normal.jpg", "hearts": 2, "retweets": 0}, "897496105562320896": {"id": "897496105562320896", "date": "6:32 PM - 15 Aug 2017", "content": "Finally, I've completed one side project! A poker app with <a href=\"https://twitter.com/angular\">@angular</a> and <a href=\"https://twitter.com/electronjs\">@electronjs</a> .Thank you <a href=\"https://twitter.com/jgthms\">@jgthms</a> for <PERSON><PERSON><PERSON> ;-) <a href=\"https://t.co/wYXB9xhOAN\">furaxpoker.com</a>", "fullname": "<PERSON>", "username": "EtienneCrb", "avatar": "https://pbs.twimg.com/profile_images/672439335531249664/0F02lmhc_normal.jpg", "hearts": 2, "retweets": 0}, "898167777718087684": {"id": "898167777718087684", "date": "3:01 PM - 17 Aug 2017", "content": "Hey <a href=\"https://twitter.com/jgthms\">@jgthms</a> Lovin' <PERSON><PERSON><PERSON> so much it's become our de-facto official CSS framework here <a href=\"https://twitter.com/launchMii\">@launchMii</a> <a href=\"https://t.co/gAsOK3Ffvo\">github.com/launchmii/style</a>", "fullname": "<PERSON><PERSON> 🕊️", "username": "<PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1185147282821271552/ylOpelVI_normal.jpg", "hearts": 2, "retweets": 0}, "899981663358373888": {"id": "899981663358373888", "date": "2:08 PM - 22 Aug 2017", "content": "<a href=\"https://t.co/V3HGV7lLFw\">bulma.io</a> - just WOW...  <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "jntme", "avatar": "https://pbs.twimg.com/profile_images/1146701874218831873/pkII4n5Z_normal.png", "hearts": 1, "retweets": 1}, "900088665292115972": {"id": "900088665292115972", "date": "9:13 PM - 22 Aug 2017", "content": "i fell in love with bulma.io by <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "g<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1302249140433412096/SD9wfcgE_normal.jpg", "hearts": 2, "retweets": 0}, "903629781744439297": {"id": "903629781744439297", "date": "3:45 PM - 1 Sep 2017", "content": "Still like <a href=\"https://twitter.com/search?q=%23bulma\">#bulma</a>, more than <a href=\"https://twitter.com/search?q=%23bootstrap\">#bootstrap</a>. Even the syntax is cleaner. <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1453890688836329473/AEkIuL3k_normal.jpg", "hearts": 14, "retweets": 0}, "904544458431188992": {"id": "904544458431188992", "date": "4:19 AM - 4 Sep 2017", "content": "Been using bulma for a few of my projects. Remarkably simple and amazingly powerful. Fantastic! <a href=\"https://twitter.com/jgthms\">@jgthms</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a>", "fullname": "stephen.", "username": "S_Prizio", "avatar": "https://pbs.twimg.com/profile_images/905517131554541569/Cs6KWJa9_normal.jpg", "hearts": 2, "retweets": 0}, "904875370842689536": {"id": "904875370842689536", "date": "2:14 AM - 5 Sep 2017", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> <PERSON>. Thanks for that!", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/819241645451321345/92dF543M_normal.jpg", "hearts": 2, "retweets": 0}, "905126100250103813": {"id": "905126100250103813", "date": "6:50 PM - 5 Sep 2017", "content": "I love it, the best framework and expanding since start. <a href=\"https://t.co/GxtYjDogTT\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/515958131063402496/Lbf2TBcl_normal.jpeg", "hearts": 2, "retweets": 0}, "907312535396929537": {"id": "907312535396929537", "date": "7:39 PM - 11 Sep 2017", "content": "Just finished rewriting a whole web project from bootstrap to bulma.io<br>Such a nice and clean css framework. Thanks <a href=\"https://twitter.com/jgthms\">@jgthms</a> 😁", "fullname": "<PERSON>", "username": "tib<PERSON><PERSON>gabriel", "avatar": "https://pbs.twimg.com/profile_images/754369619100508160/uyyeKLk1_normal.jpg", "hearts": 2, "retweets": 0}, "907551723459416071": {"id": "907551723459416071", "date": "11:29 AM - 12 Sep 2017", "content": "Falling in love with <PERSON><PERSON><PERSON> <a href=\"https://twitter.com/search?q=%23CSS\">#CSS</a> framework by <a href=\"https://twitter.com/jgthms\">@jgthms</a> - Very complete and easy to use - Check it out <a href=\"https://t.co/9Df8Kf7KTa\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a>", "fullname": "<PERSON> 🧡 Node.js", "username": "loige", "avatar": "https://pbs.twimg.com/profile_images/1294586885726240770/Dx7103Wv_normal.jpg", "hearts": 6, "retweets": 3}, "908286434607542274": {"id": "908286434607542274", "date": "12:08 PM - 14 Sep 2017", "content": "Love the simplicity of <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> by <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1359127036493758465/-uwqDzhY_normal.jpg", "hearts": 1, "retweets": 0}, "908734745994969089": {"id": "908734745994969089", "date": "5:50 PM - 15 Sep 2017", "content": ".<a href=\"https://twitter.com/bulmaio\">@bulmaio</a> is amazing. Had a coming soon page up and running in 10 minutes. Thanks <a href=\"https://twitter.com/jgthms\">@jgthms</a> 🙌 I'm a fan now.", "fullname": "saif.dev 👨🏻‍💻", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/957689826114981893/bh3KU3Dq_normal.jpg", "hearts": 2, "retweets": 0}, "909242618969804800": {"id": "909242618969804800", "date": "3:28 AM - 17 Sep 2017", "content": "Started using <PERSON><PERSON><PERSON> in a real project for a client, so far it's been an easy and pleasant transition from Bootstrap. Great job <a href=\"https://twitter.com/jgthms\">@jgthms</a>!", "fullname": "<PERSON>", "username": "chris_k_brewer", "avatar": "https://pbs.twimg.com/profile_images/1053667496039931904/QLVk1gvw_normal.jpg", "hearts": 1, "retweets": 0}, "909653512010833920": {"id": "909653512010833920", "date": "6:41 AM - 18 Sep 2017", "content": "Interesting Twitter Bootstrap alternative: <a href=\"https://t.co/DhnIIMHZLu\">bulma.io</a><br>by <a href=\"https://twitter.com/jgthms\">@jgthms</a><br>My favourite part of it? No JavaScript by default.", "fullname": "<PERSON>", "username": "wintermeyer", "avatar": "https://pbs.twimg.com/profile_images/1485874500055011328/bn_KwbXA_normal.jpg", "hearts": 29, "retweets": 13}, "910956939886043136": {"id": "910956939886043136", "date": "9:00 PM - 21 Sep 2017", "content": "Want a change of pace from Bootstrap? Try <PERSON>ul<PERSON>. It's my BFF right now <a href=\"https://twitter.com/search?q=%23bestframeworkforever\">#bestframeworkforever</a> <a href=\"https://t.co/5s3H1kU5rZ\">scotch.io/bar-talk/get-t…</a>", "fullname": "chris sev", "username": "chris__sev", "avatar": "https://pbs.twimg.com/profile_images/1397546328507355147/7EkFZmd3_normal.jpg", "hearts": 86, "retweets": 17}, "910989105172852737": {"id": "910989105172852737", "date": "11:08 PM - 21 Sep 2017", "content": "<a href=\"https://twitter.com/ammezie\">@ammezie</a> Agreed. Easy to read docs are a huge positive also. Great work by <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "chris sev", "username": "chris__sev", "avatar": "https://pbs.twimg.com/profile_images/1397546328507355147/7EkFZmd3_normal.jpg", "hearts": 2, "retweets": 0}, "911062230711640064": {"id": "911062230711640064", "date": "3:58 AM - 22 Sep 2017", "content": "Learning Bulma <a href=\"https://twitter.com/jgthms\">@jgthms</a> has been a lot of fun.  Check it out <a href=\"https://t.co/WtXIzMywVS\">bulma.io</a>.  I just finished a is-rounded mixin for buttons.", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1321075296653762560/jvJFfJMe_normal.jpg", "hearts": 2, "retweets": 0}, "912690697416753152": {"id": "912690697416753152", "date": "3:49 PM - 26 Sep 2017", "content": "Giving bulma.io a try for a new site and I’m loving its simplicity. Thanks <a href=\"https://twitter.com/jgthms\">@jgthms</a> for a great framework.", "fullname": "<PERSON><PERSON><PERSON>", "username": "arlo_vance", "avatar": "https://pbs.twimg.com/profile_images/902215209862955012/YQc94OEQ_normal.jpg", "hearts": 4, "retweets": 0}, "912727491177013248": {"id": "912727491177013248", "date": "6:16 PM - 26 Sep 2017", "content": "I've been playing with <a href=\"https://twitter.com/vuejs\">@vuejs</a> for a bit and just learned about conditional formatting. I'm in love. Add in Bulma <a href=\"https://twitter.com/jgthms\">@jgthms</a> == heaven!", "fullname": "<PERSON>", "username": "CyanTrox", "avatar": "https://pbs.twimg.com/profile_images/864590743037292544/kIyDwPWh_normal.jpg", "hearts": 3, "retweets": 0}, "915316133904908293": {"id": "915316133904908293", "date": "9:42 PM - 3 Oct 2017", "content": "Bulma might be the best CSS framework for fast Angular2 development. <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON><PERSON><PERSON>", "username": "brunsauce", "avatar": "https://pbs.twimg.com/profile_images/1356209396875980800/3Gyhlf7q_normal.jpg", "hearts": 4, "retweets": 1}, "915580081938018304": {"id": "915580081938018304", "date": "3:11 PM - 4 Oct 2017", "content": "Best css framework, amazing implementation. No idea how I lived without this.<br><a href=\"https://t.co/FhBPLhWVIU\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON> ᓚᘏᗢ", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/530443756114153472/nSmN8eUj_normal.jpeg", "hearts": 6, "retweets": 0}, "915607553693888513": {"id": "915607553693888513", "date": "5:00 PM - 4 Oct 2017", "content": "I’ve finished the Bootstrap to <PERSON><PERSON><PERSON> conversion for one of my biggest apps.  I love it!  Thanks <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1321075296653762560/jvJFfJMe_normal.jpg", "hearts": 2, "retweets": 0}, "916613798261293056": {"id": "916613798261293056", "date": "11:38 AM - 7 Oct 2017", "content": "<a href=\"https://twitter.com/mojavelinux\">@mojavelinux</a> We're using <a href=\"https://twitter.com/jgthms\">@jgthms</a> <PERSON><PERSON><PERSON> successfully for a communication facility dashboard together with <a href=\"https://twitter.com/vuejs\">@vuejs</a>: Loving the KISS codestyle and clean L&amp;F", "fullname": "<PERSON>", "username": "bentolor", "avatar": "https://pbs.twimg.com/profile_images/1392400883556753410/_bjOHFcT_normal.jpg", "hearts": 1, "retweets": 0}, "918246391440228353": {"id": "918246391440228353", "date": "11:46 PM - 11 Oct 2017", "content": "Bulma is easy and fun to use!! Awesome style too <a href=\"https://t.co/rkeMjcVZz0\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON> 🚀 🌕", "username": "zexingguo", "avatar": "https://pbs.twimg.com/profile_images/1486047388129062915/CvYKO6_0_normal.jpg", "hearts": 0, "retweets": 0}, "918832236249657344": {"id": "918832236249657344", "date": "2:34 PM - 13 Oct 2017", "content": "Nice to see a css framework that keeps your markup clean.  Will definitely be using. <a href=\"https://t.co/2sotwt58tB\">bulma.io</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/378800000600432219/183f2dd409d8549e70e9e4e77140359f_normal.jpeg", "hearts": 0, "retweets": 0}, "919669366781706240": {"id": "919669366781706240", "date": "10:00 PM - 15 Oct 2017", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> really beautiful work on Bulma... building my wedding website with it, and I couldn't be happier with what I am getting!", "fullname": "<PERSON>", "username": "JpPope", "avatar": "https://pbs.twimg.com/profile_images/886609660240740353/q3pKTEt1_normal.jpg", "hearts": 1, "retweets": 0}, "920269157647527938": {"id": "920269157647527938", "date": "1:43 PM - 17 Oct 2017", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> well well well! very impressive css framework bulma loving it. so simple and sober.....", "fullname": "<PERSON><PERSON><PERSON>", "username": "pedropankaj", "avatar": "https://pbs.twimg.com/profile_images/765024424403804160/R6NT7guZ_normal.jpg", "hearts": 0, "retweets": 0}, "920572900440199168": {"id": "920572900440199168", "date": "9:50 AM - 18 Oct 2017", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> bulma is so freakin amazing, loving it already <a href=\"https://t.co/iPX4mXUDJN\">pic.twitter.com/iPX4mXUDJN</a>", "fullname": "<PERSON><PERSON>", "username": "navin<PERSON>h", "avatar": "https://pbs.twimg.com/profile_images/1267599394536394753/jRYJBbrE_normal.jpg", "hearts": 1, "retweets": 0}, "920660006831382528": {"id": "920660006831382528", "date": "3:37 PM - 18 Oct 2017", "content": "My son wants to make his website look nice. I obviously told him to use: bulma.io. Lightweight, intuitive and readable. Thanks <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1020747664034430976/JPVqzg4W_normal.jpg", "hearts": 2, "retweets": 0}, "922849122008354817": {"id": "922849122008354817", "date": "4:35 PM - 24 Oct 2017", "content": "Spent months comparing frontend frameworks for both <a href=\"https://twitter.com/search?q=%23css\">#css</a> and <a href=\"https://twitter.com/search?q=%23javascript\">#javascript</a>. Settled on <a href=\"https://twitter.com/jgthms\">@jgthms</a>'s <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> for CSS and <a href=\"https://twitter.com/<PERSON>_Harris\">@<PERSON>_<PERSON></a>'s <a href=\"https://twitter.com/sveltejs\">@sveltejs</a> for Javascript. Works great, there is a clear separation of look and function, and the resulting code is super clean. Perfect combo ! 🚀", "fullname": "<PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1024386706307510272/5a67Z2sL_normal.jpg", "hearts": 22, "retweets": 7}, "923223339388297216": {"id": "923223339388297216", "date": "5:22 PM - 25 Oct 2017", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> 100 times yes! <PERSON><PERSON><PERSON> is easily my favorite CSS framework", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1281835358733438978/IaNaRs8M_normal.jpg", "hearts": 1, "retweets": 0}, "923230419746803713": {"id": "923230419746803713", "date": "5:50 PM - 25 Oct 2017", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> Nice! <PERSON><PERSON><PERSON> just keeps getting better.", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1020747664034430976/JPVqzg4W_normal.jpg", "hearts": 1, "retweets": 0}, "923248261519040514": {"id": "923248261519040514", "date": "7:01 PM - 25 Oct 2017", "content": "I’ve finished the Bootstrap to <PERSON><PERSON><PERSON> conversion for one of my biggest apps. I love it! Thanks <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "daniel rod<PERSON><PERSON>z ly", "username": "danielr89411235", "avatar": "https://pbs.twimg.com/profile_images/793254118781493248/KrBe2lk2_normal.jpg", "hearts": 1, "retweets": 1}, "923266288830636044": {"id": "923266288830636044", "date": "8:13 PM - 25 Oct 2017", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> previously used bootstrap to throw things together, but I'm completely sold on <a href=\"https://twitter.com/search?q=%23bulma\">#bulma</a>, it's awesome, well done and thank-you!", "fullname": "<PERSON>", "username": "d<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/3120867357/eb77759d9d844a93cf3bd78f6a4fd630_normal.png", "hearts": 2, "retweets": 0}, "923439846089113600": {"id": "923439846089113600", "date": "7:43 AM - 26 Oct 2017", "content": "Bulma is ❤️ h<a href=\"https://t.co/nbhmUp6t1E\">twitter.com/jgthms/status/…</a>", "fullname": "<PERSON><PERSON> 🇲🇻", "username": "<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1360267204818653184/2xzJY9w6_normal.jpg", "hearts": 0, "retweets": 0}, "924319630398775303": {"id": "924319630398775303", "date": "5:59 PM - 28 Oct 2017", "content": "Falling in love with <a href=\"https://twitter.com/search?q=%23Bulma\">#Bulma</a> 💙<a href=\"https://t.co/pAMWa22RWW\">bulma.io</a> // <a href=\"https://twitter.com/jgthms\">@jgthms</a> <a href=\"https://twitter.com/search?q=%23css\">#css</a>  <a href=\"https://twitter.com/search?q=%23html\">#html</a> <a href=\"https://twitter.com/search?q=%23cssframework\">#cssframework</a> <a href=\"https://twitter.com/search?q=%23webdesign\">#webdesign</a> <a href=\"https://twitter.com/search?q=%23rocks\">#rocks</a>", "fullname": "XΛVΛ\\du", "username": "xavadu", "avatar": "https://pbs.twimg.com/profile_images/595714227169525760/RuLICzP-_normal.jpg", "hearts": 3, "retweets": 0}, "927566369616515072": {"id": "927566369616515072", "date": "4:00 PM - 6 Nov 2017", "content": "Really love what <a href=\"https://twitter.com/jgthms\">@jgthms</a> is doing with <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> - if you haven't seen it, try it! <a href=\"https://t.co/NQdGSaNtKf\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23webdev\">#webdev</a>", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/794696494091792384/V5WGxQTk_normal.jpg", "hearts": 4, "retweets": 2}, "930438052610265088": {"id": "930438052610265088", "date": "2:11 PM - 14 Nov 2017", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> <a href=\"https://twitter.com/Patreon\">@Patreon</a> Amazing framework, easy to integrate! I’m going forward with <a href=\"https://twitter.com/search?q=%23Bulma\">#Bulma</a> on all of my future websites!", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1046744938883338241/kHChabPa_normal.jpg", "hearts": 1, "retweets": 0}, "932859950103236609": {"id": "932859950103236609", "date": "6:35 AM - 21 Nov 2017", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> I was hooked with your framework. So simple to use... Bulma ia great framework for CSS, it's lightweight compared to bootstrap.", "fullname": "<PERSON><PERSON> 🇹🇿", "username": "<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1370354940816818186/E6lavNt9_normal.jpg", "hearts": 0, "retweets": 0}, "938770201344200704": {"id": "938770201344200704", "date": "2:00 PM - 7 Dec 2017", "content": "Loving <PERSON><PERSON><PERSON> from <a href=\"https://twitter.com/jgthms\">@jgthms</a>. Easy to get into. Also love that there's no JS attached.", "fullname": "<PERSON>", "username": "thesapper", "avatar": "https://pbs.twimg.com/profile_images/3506183149/f595a769ff1a1e24ca11bae22c2e33b5_normal.jpeg", "hearts": 0, "retweets": 0}, "940986690969194497": {"id": "940986690969194497", "date": "4:48 PM - 13 Dec 2017", "content": "Heard about <PERSON><PERSON><PERSON> through a developer friend, never looked back! <a href=\"https://t.co/H31suhOSQc\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "Revoltionary Developers", "username": "redevsorg", "avatar": "https://pbs.twimg.com/profile_images/940986784443518976/EgkdGR8B_normal.jpg", "hearts": 1, "retweets": 0}, "941217999981301762": {"id": "941217999981301762", "date": "8:07 AM - 14 Dec 2017", "content": "Thank you for creating a CSS Framework that makes development fun again! <a href=\"https://t.co/D3tNADOjNf\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/881516403844579334/UgWdAJvY_normal.jpg", "hearts": 1, "retweets": 0}, "941263756402798592": {"id": "941263756402798592", "date": "11:09 AM - 14 Dec 2017", "content": "I failing into the ocean of love with <PERSON><PERSON><PERSON> 😍😍😍<br>Hope the ocean is not deep.<br> <a href=\"https://t.co/7aBr8h32kC\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "Judah🔥🚀", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1374025372942094339/tK4WUjwv_normal.jpg", "hearts": 2, "retweets": 0}, "941417798999379969": {"id": "941417798999379969", "date": "9:21 PM - 14 Dec 2017", "content": "I made my kid a times-tables practice thingy using <a href=\"https://twitter.com/vuejs\">@vuejs</a> and Bulma.css.  <a href=\"https://twitter.com/jgthms\">@jgthms</a>: <a href=\"https://t.co/BCvZsfPB3d\">bacalj.github.io/funtimes/</a>", "fullname": "<PERSON>", "username": "joe_bacal", "avatar": "https://pbs.twimg.com/profile_images/1485776968159305730/ryNaKUi4_normal.png", "hearts": 5, "retweets": 0}, "943018433427443713": {"id": "943018433427443713", "date": "7:21 AM - 19 Dec 2017", "content": "Here at Yard8 we are definitely huge fans of <PERSON><PERSON><PERSON>! <a href=\"https://t.co/NChshOGvYo\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "Yard8", "username": "yard8za", "avatar": "https://pbs.twimg.com/profile_images/940235593803489280/b_MCvFdN_normal.jpg", "hearts": 3, "retweets": 0}, "943109273776807938": {"id": "943109273776807938", "date": "1:22 PM - 19 Dec 2017", "content": "I love this guy | Bulma: a modern CSS framework based on Flexbox <a href=\"https://t.co/uH0Pe4K0Ne\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON><PERSON> Evangelist<PERSON> 🇺🇦🕊️", "username": "<PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1441188466415443976/Q2J5osmn_normal.jpg", "hearts": 1, "retweets": 0}, "944323985805922305": {"id": "944323985805922305", "date": "9:49 PM - 22 Dec 2017", "content": "I was a big fan of <a href=\"https://twitter.com/zurbfoundation\">@zurbfoundation</a>, but I would say that I prefer <a href=\"https://twitter.com/bulmaio\">@bulmaio</a> at least for simple applications.", "fullname": "<PERSON>", "username": "martinosis", "avatar": "https://pbs.twimg.com/profile_images/1169365219/Californie_161_normal.JPG", "hearts": 0, "retweets": 0}, "946165040243249152": {"id": "946165040243249152", "date": "11:44 PM - 27 Dec 2017", "content": "I really like <a href=\"https://twitter.com/jgthms\">@jgthms</a>’ <a href=\"https://twitter.com/search?q=%23Bulma\">#Bulma</a> <a href=\"https://twitter.com/search?q=%23Flexbox\">#Flexbox</a> framework. It just makes sense!", "fullname": "<PERSON>", "username": "da<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1361420957265629188/dybv0CKq_normal.jpg", "hearts": 1, "retweets": 0}, "948498673247358977": {"id": "948498673247358977", "date": "10:18 AM - 3 Jan 2018", "content": "Definitely using <a href=\"https://twitter.com/bulmaio\">@bulmaio</a> for my next front-end project, that shit it cleannnn", "fullname": "<PERSON><PERSON>", "username": "mrs<PERSON><PERSON>b", "avatar": "https://pbs.twimg.com/profile_images/850805857440935939/2dFeSRP3_normal.jpg", "hearts": 3, "retweets": 1}, "949253263987216384": {"id": "949253263987216384", "date": "12:16 PM - 5 Jan 2018", "content": "I highly recommend <a href=\"https://t.co/5EZaOSkJV0\">bulma.io</a> to those looking for a lightweight CSS framework. Used it in my most recent project and have enjoyed its straightforward approach. <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a>", "fullname": "mattk", "username": "_howlCode", "avatar": "https://pbs.twimg.com/profile_images/904455798071455744/c0bZQwrk_normal.jpg", "hearts": 0, "retweets": 0}, "949293612990386177": {"id": "949293612990386177", "date": "2:56 PM - 5 Jan 2018", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> i love bulma :) became my favorite css framework after 1st use. thanks for your work!", "fullname": "Drago<PERSON>", "username": "dragos_dydy", "avatar": "https://pbs.twimg.com/profile_images/1460970724806606853/Juh4KyF0_normal.png", "hearts": 2, "retweets": 0}, "949986519250624513": {"id": "949986519250624513", "date": "12:50 PM - 7 Jan 2018", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> Thank you <PERSON>. I really appreciate your work. It means a lot to me and my small business.", "fullname": "дан Аллен 🇺🇦 ☮️", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1176696426895675399/lMcDKZ6Q_normal.jpg", "hearts": 6, "retweets": 0}, "951098342158807041": {"id": "951098342158807041", "date": "2:28 PM - 10 Jan 2018", "content": "Just wanted to give a shoutout to <a href=\"https://twitter.com/jgthms\">@jgthms</a> for creating the nicest, most thoughtful CSS-only framework <a href=\"https://t.co/zyMpDP5TtR\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a>. In this age of bloated frameworks, it has everything I need and nothing that I don't.", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1261286151442313216/5pXjblqT_normal.jpg", "hearts": 8, "retweets": 2}, "951132634083545088": {"id": "951132634083545088", "date": "4:44 PM - 10 Jan 2018", "content": "Just discovered <PERSON><PERSON><PERSON> last night, and I think my Bootstrap days are numbered!  This looks awesome.  Time to start coding. <a href=\"https://t.co/REqvj5Ju1t\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON>_<PERSON>", "avatar": "https://pbs.twimg.com/profile_images/975817452561956864/SeaTeHEd_normal.jpg", "hearts": 6, "retweets": 1}, "959005382273355776": {"id": "959005382273355776", "date": "10:07 AM - 1 Feb 2018", "content": "I've been following <PERSON><PERSON><PERSON> for a while now and it's time to use it for real in a customized dashboard and I have to say: it is neat, sexy and lightweight (compared to Bootstrap). Thx <a href=\"https://twitter.com/jgthms\">@jgthms</a> ! 😍  <a href=\"https://t.co/tmw8xIVWqs\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a>", "fullname": "<PERSON>", "username": "UnMecRandom", "avatar": "https://pbs.twimg.com/profile_images/738358155634200576/B8NIIGGE_normal.jpg", "hearts": 5, "retweets": 0}, "962366768260972544": {"id": "962366768260972544", "date": "4:44 PM - 10 Feb 2018", "content": "<PERSON><PERSON><PERSON> is awesome... is sexy and strong <a href=\"https://t.co/11qVjgmmuo\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> vía <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "G<PERSON>bso", "username": "globso_", "avatar": "https://pbs.twimg.com/profile_images/946965795686506500/-XCjT3eh_normal.jpg", "hearts": 4, "retweets": 0}, "963476902324391937": {"id": "963476902324391937", "date": "6:16 PM - 13 Feb 2018", "content": "I think I'm in love with <a href=\"https://t.co/EEXjQaZo2X\">bulma.io</a>. The syntax, especially modifiers, is way better than the Bootstrap's one, easy to learn and remember. Awesome work <a href=\"https://twitter.com/jgthms\">@jgthms</a>! <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a>", "fullname": "<PERSON>", "username": "Nicuz95", "avatar": "https://pbs.twimg.com/profile_images/932251438318870529/GrHe0uTP_normal.jpg", "hearts": 3, "retweets": 0}, "966731525709619200": {"id": "966731525709619200", "date": "5:48 PM - 22 Feb 2018", "content": "I've spent a few hours now with the Bulma CSS framework and I have to say it's the most beautifully written framework I've ever had the pleasure of working with. Nice work <a href=\"https://twitter.com/jgthms\">@jgthms</a> 👊 <a href=\"https://t.co/W9GKZ0GZoV\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a>", "fullname": "<PERSON>", "username": "axbom", "avatar": "https://pbs.twimg.com/profile_images/1249988844029706242/OwW7i_6U_normal.jpg", "hearts": 18, "retweets": 4}, "975991513489567744": {"id": "975991513489567744", "date": "7:04 AM - 20 Mar 2018", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> <a href=\"https://twitter.com/waynefederigan\">@waynefederigan</a> That’s right 👍 I kicked Bootstra<PERSON> since Bulma 0.3.x 🔥 and never change my choice 😌", "fullname": "<PERSON>", "username": "true_koddr", "avatar": "https://pbs.twimg.com/profile_images/1441795827807178759/tHbWZxav_normal.jpg", "hearts": 1, "retweets": 0}, "977974559671504896": {"id": "977974559671504896", "date": "8:24 PM - 25 Mar 2018", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> <PERSON><PERSON><PERSON> is amazing!", "fullname": "<PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1171825692549537792/acCG-ydD_normal.jpg", "hearts": 2, "retweets": 0}, "978285770992685056": {"id": "978285770992685056", "date": "4:01 PM - 26 Mar 2018", "content": "Been using bulma framework for a while now, the simplicity of it all is lit 👌👌<br><a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "añ", "username": "6thcode", "avatar": "https://pbs.twimg.com/profile_images/1509158576652275719/kphPzpm0_normal.jpg", "hearts": 2, "retweets": 0}, "978391597799886848": {"id": "978391597799886848", "date": "11:01 PM - 26 Mar 2018", "content": "So \"The official Bulma book\" is out now by <a href=\"https://twitter.com/jgthms\">@jgthms</a>.🙌 You will learn how to build interfaces with Bulma. I'm a huge Bulma fan and if you're looking for an alternative to <a href=\"https://twitter.com/getbootstrap\">@getbootstrap</a> then this is for you. I used Bulma for <a href=\"https://t.co/5wStOb9olM\">cronhub.io</a>.<br><br>➡️ h<a href=\"https://t.co/9IHrc9woie\">bleedingedgepress.com/creating-inter…</a>", "fullname": "<PERSON><PERSON><PERSON>", "username": "tiggreen", "avatar": "https://pbs.twimg.com/profile_images/1345405328649838592/ckQ2cNe9_normal.jpg", "hearts": 8, "retweets": 0}, "979267491590483971": {"id": "979267491590483971", "date": "9:02 AM - 29 Mar 2018", "content": "<a href=\"https://twitter.com/andrew<PERSON>t<PERSON>bson\">@andrew<PERSON><PERSON><PERSON><PERSON></a> <a href=\"https://twitter.com/smashingmag\">@smashingmag</a> <PERSON><PERSON><PERSON> by <a href=\"https://twitter.com/jgthms\">@jgthms</a> is a great front-end framework with equally great documentation. Very clearly thought out class names and structure.", "fullname": "C.S. Rhymes", "username": "chrisr<PERSON>mes", "avatar": "https://pbs.twimg.com/profile_images/1241381821738336257/VeyVE2h0_normal.jpg", "hearts": 3, "retweets": 0}, "979328804953849857": {"id": "979328804953849857", "date": "1:05 PM - 29 Mar 2018", "content": "I'm in love with <a href=\"https://t.co/I1D0FUHMz3\">bulma.io</a>. The syntax, especially modifiers, is way better than the Bootstrap's one, easy to learn and remember. Awesome work <a href=\"https://twitter.com/jgthms\">@jgthms</a>! <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a>", "fullname": "<PERSON><PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1246834527432888320/Dwj5nYFG_normal.jpg", "hearts": 3, "retweets": 0}, "979645983637364738": {"id": "979645983637364738", "date": "10:06 AM - 30 Mar 2018", "content": "Bulma: A Fantastic CSS Framework You May Not Have Heard Of - <a href=\"https://t.co/9NdytWTTlG\">hostpresto.com/blog/bulma-a-f…</a> <a href=\"https://twitter.com/search?q=%23css\">#css</a> <a href=\"https://twitter.com/search?q=%23framework\">#framework</a> <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "HostPresto", "username": "hostpresto", "avatar": "https://pbs.twimg.com/profile_images/799162587434848256/3EQL-csn_normal.jpg", "hearts": 1, "retweets": 0}, "982215750432604160": {"id": "982215750432604160", "date": "12:17 PM - 6 Apr 2018", "content": "<a href=\"https://twitter.com/perborgen\">@perborgen</a> <a href=\"https://twitter.com/jgthms\">@jgthms</a> @scrimba_com Elegant, lightweight, great documentation, sass... The best framework by mile in my opinion.", "fullname": "<PERSON><PERSON><PERSON>", "username": "DjordjeVasilic", "avatar": "https://pbs.twimg.com/profile_images/1362678018779582466/w3mP46wo_normal.jpg", "hearts": 2, "retweets": 0}, "868829487072464897": {"id": "868829487072464897", "date": "3:01 PM - 28 May 2017", "content": "So, I was making an exam and in a matter of 30 minutes I had my structure complete with responsive, <PERSON><PERSON><PERSON> is crazy. Thanks <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "atFranCruz", "avatar": "/images/twitter/atFranCruz.jpg", "hearts": 8, "retweets": 0}, "1012427478915256320": {"id": "1012427478915256320", "date": "9:08 PM - 28 Jun 2018", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> i ❤️bulma, thank you for making this nice project!", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1016327398533541889/fwsptQcW_normal.jpg", "hearts": 1, "retweets": 0}, "1012491101264064512": {"id": "1012491101264064512", "date": "1:21 AM - 29 Jun 2018", "content": "<a href=\"https://twitter.com/rafaelpimpa\">@rafaelpimpa</a> <a href=\"https://twitter.com/vuejs\">@vuejs</a> <a href=\"https://twitter.com/jgthms\">@jgthms</a> Thanks for the awesome framework :)", "fullname": "<PERSON>", "username": "blini369", "avatar": "https://pbs.twimg.com/profile_images/1012491675531448321/NoVXfH9k_normal.jpg", "hearts": 2, "retweets": 0}, "1012670247323697155": {"id": "1012670247323697155", "date": "2:12 PM - 29 Jun 2018", "content": "Started a project using Bulma today, a CSS Framework that I always wanted to try... Until now, the experience is being very nice! :D <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1402344002981187589/ww6ZQOzy_normal.jpg", "hearts": 1, "retweets": 0}, "1012674477962944513": {"id": "1012674477962944513", "date": "1:29 PM - 29 Jun 2018", "content": "How have I not heard of <PERSON><PERSON><PERSON> before? <a href=\"https://twitter.com/jgthms\">@jgthms</a> 🤯<br><br>It's a CSS framework 🛠️ that's really easy to use and well-designed, which is good because I'm way too lazy to design my own shit ✨<br><br>Highly recommended, check it out 👉 h<a href=\"https://t.co/ijAKQHZhwx\">bulma.io</a><br>#<a href=\"https://twitter.com/search?q=%23100DaysOfCode\">#100DaysOfCode</a>", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1182918448965279744/JmA2GmAP_normal.jpg", "hearts": 3, "retweets": 2}, "1013079944178458624": {"id": "1013079944178458624", "date": "5:20 PM - 30 Jun 2018", "content": "<a href=\"https://twitter.com/search?q=%23bulma\">#bulma</a> seems to be so popular now a days. Decided to use in next project instead of <a href=\"https://twitter.com/search?q=%23bootstrap\">#bootstrap</a> .<a href=\"https://twitter.com/search?q=%23madewithbulma\">#madewithbulma</a>", "fullname": "PRAVΞΞN", "username": "aspraveen", "avatar": "https://pbs.twimg.com/profile_images/1375801479378898944/6SKC3pN4_normal.jpg", "hearts": 1, "retweets": 0}, "1013507295739707393": {"id": "1013507295739707393", "date": "8:39 PM - 1 Jul 2018", "content": "I'm an early adopter of <a href=\"https://t.co/6h8QENGyJ9\">bulma.io</a> It's been awesome since the start. I knew it would be big. Amazing work. It saves time and brain power lol.<br><a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "TristanPreaches", "avatar": "https://pbs.twimg.com/profile_images/1269174979570081792/gg6ZM-NS_normal.jpg", "hearts": 2, "retweets": 0}, "1013700257920634880": {"id": "1013700257920634880", "date": "10:25 AM - 2 Jul 2018", "content": "What a beautiful framework Bulma is 😍!!! I am totally addicted to it. The best part? No Javascript!!!<br><a href=\"https://t.co/TV1VmkGk9T\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "TechnoCombo", "username": "TechnoCombo", "avatar": "https://pbs.twimg.com/profile_images/918771091333570560/8ci7_nFr_normal.jpg", "hearts": 1, "retweets": 0}, "1013933065096159233": {"id": "1013933065096159233", "date": "12:50 AM - 3 Jul 2018", "content": "<a href=\"https://twitter.com/zeithq\">@zeithq</a> Next.js + <a href=\"https://twitter.com/jgthms\">@jgthms</a>' <PERSON><PERSON><PERSON> + <a href=\"https://twitter.com/AlgusDark\">@AlgusDark</a>'s Bloomer = 🔥", "fullname": "<PERSON><PERSON><PERSON>", "username": "griko_nibras", "avatar": "https://pbs.twimg.com/profile_images/1002256907099193345/vhRKrQvs_normal.jpg", "hearts": 4, "retweets": 1}, "1016062253349601280": {"id": "1016062253349601280", "date": "10:51 PM - 8 Jul 2018", "content": "I like Flexbox.. I also like Sass. and boom! <a href=\"https://twitter.com/jgthms\">@jgthms</a> came out with <PERSON><PERSON><PERSON>.<br><br>Nice.", "fullname": "<PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1027990059184783360/1nuiOkjq_normal.jpg", "hearts": 1, "retweets": 0}, "1016398509493374976": {"id": "1016398509493374976", "date": "9:07 PM - 9 Jul 2018", "content": "I'm generally not the biggest fan of using front-end frameworks 🤷‍♂️, but @bu<a href=\"https://twitter.com/bulmaio\">@bulmaio</a>is just something I've fallen in love with 💝! Beautiful yet highly customisable 👨‍💻. Awesome work @jgth<a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "ronaldlangeveld", "avatar": "https://pbs.twimg.com/profile_images/1176308805342097409/AZ11imTV_normal.jpg", "hearts": 3, "retweets": 0}, "1016508485738131458": {"id": "1016508485738131458", "date": "4:24 AM - 10 Jul 2018", "content": "May be moving from Bootstrap to Bulma ( by <a href=\"https://twitter.com/jgthms\">@jgthms</a> ) for personal projects. From a developer perspective, it seems to be just as good as Bootstrap, but with more intuitive class names <a href=\"https://t.co/b5IjJMEAjA\">bulma.io</a> <a href=\"https://t.co/0SZe799SEn\">pic.twitter.com/0SZe799SEn</a>", "fullname": "<PERSON>", "username": "sto<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1021150483715518464/WkUA4ty3_normal.jpg", "hearts": 1, "retweets": 0}, "824053457527209984": {"id": "824053457527209984", "date": "12:37 AM - 25 Jan 2017", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> Are you the one behind <PERSON><PERSON><PERSON>? I am using it in one project and I am completely delightful. Thank you so much!!", "fullname": "<PERSON>", "username": "atFranCruz", "avatar": "https://pbs.twimg.com/profile_images/802607347306594304/siN1Iznd_normal.jpg", "hearts": 1, "retweets": 0}, "989986709801664518": {"id": "989986709801664518", "date": "10:56 PM - 27 Apr 2018", "content": "Elegant, lightweight, sass, great documentation, ... The syntax, especially modifiers, is easy to learn and remember.  <a href=\"https://t.co/LhnUtOd9wa\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON><PERSON>", "username": "Hannoma_", "avatar": "https://pbs.twimg.com/profile_images/952282303086190598/9SVBE-P1_normal.jpg", "hearts": 0, "retweets": 0}, "993132960718012417": {"id": "993132960718012417", "date": "3:18 PM - 6 May 2018", "content": "<a href=\"https://t.co/1IbaI8kZHq\">bulma.io</a> is my new preferred css kit, transforms your web apps from something bland and plain into vibrant and engaging!<br><br><a href=\"https://twitter.com/jgthms\">@jgthms</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a>", "fullname": "<PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1453515916306628609/sU26WiiE_normal.jpg", "hearts": 3, "retweets": 0}, "996263346189094916": {"id": "996263346189094916", "date": "6:37 AM - 15 May 2018", "content": "<a href=\"https://t.co/ytJazrHSVu\">playbookhub.com</a> <a href=\"https://twitter.com/search?q=%23madewithbulma\">#madewithbulma</a>", "fullname": "<PERSON><PERSON>", "username": "billowza", "avatar": "https://pbs.twimg.com/profile_images/645872713945362432/uvlh99Ll_normal.jpg", "hearts": 2, "retweets": 0}, "997455495156895744": {"id": "997455495156895744", "date": "1:34 PM - 18 May 2018", "content": "Day 6. Created a personal website with <a href=\"https://twitter.com/search?q=%23Bulma\">#Bulma</a>. Now I have my own place on the Internet 🙃 Really like it <a href=\"https://twitter.com/search?q=%23madewithbulma\">#madewithbulma</a> <a href=\"https://twitter.com/search?q=%23100DaysOfCode\">#100DaysOfCode</a> <br>Here's the link: <a href=\"https://t.co/fvcyowV4tJ\">nikrudenko.github.io/home/<USER>/a> <a href=\"https://t.co/YoybGALYnG\">pic.twitter.com/YoybGALYnG</a>", "fullname": "<PERSON><PERSON> 🇺🇦", "username": "rdnkta", "avatar": "https://pbs.twimg.com/profile_images/1035176676148301824/QDHWMnWW_normal.jpg", "hearts": 7, "retweets": 1}, "997518915268939776": {"id": "997518915268939776", "date": "5:46 PM - 18 May 2018", "content": "My favorite CSS Framework 2018 ❤<br><a href=\"https://t.co/aQ2eGlMUta\">bulma.io</a> by <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "◢◢", "username": "marlus", "avatar": "https://pbs.twimg.com/profile_images/1450953227692871688/Lvd7hpFz_normal.jpg", "hearts": 1, "retweets": 1}, "997733530103824384": {"id": "997733530103824384", "date": "7:59 AM - 19 May 2018", "content": "<a href=\"https://twitter.com/search?q=%23Bulma\">#Bulma</a> is simple yet awesome. It is light, feature packed and very easy to use. I really love how well the column system works. Beautifully done.<br>Thumbs up to <a href=\"https://twitter.com/jgthms\">@jgthms</a> for the great work!!<br><a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> <a href=\"https://twitter.com/search?q=%23css\">#css</a> <a href=\"https://twitter.com/search?q=%23webdevelopment\">#webdevelopment</a>", "fullname": "<PERSON>", "username": "DSHowson", "avatar": "https://pbs.twimg.com/profile_images/1090279712922845184/AVDcTr06_normal.jpg", "hearts": 2, "retweets": 0}, "998600110144675840": {"id": "998600110144675840", "date": "5:23 PM - 21 May 2018", "content": "I really think that Bulma is the best CSS framework.  <a href=\"https://twitter.com/jgthms\">@jgthms</a> did a great job in making a light &amp; powerful tool.", "fullname": "ummerr", "username": "ummerrr", "avatar": "https://pbs.twimg.com/profile_images/777300595027816448/3QPXJ2L9_normal.jpg", "hearts": 1, "retweets": 0}, "1020920571167531008": {"id": "1020920571167531008", "date": "8:36 AM - 22 Jul 2018", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> <a href=\"https://twitter.com/search?q=%23bulma\">#bulma</a> is a delight to work with.Great work man, I love it ❤️", "fullname": "Sachin 🍃", "username": "thetinygoat", "avatar": "https://pbs.twimg.com/profile_images/1506539050513035265/4B1BGZtG_normal.jpg", "hearts": 1, "retweets": 0}, "1024739774534303744": {"id": "1024739774534303744", "date": "8:32 PM - 1 Aug 2018", "content": "OK, I was diggin' it before, but now it's official. <a href=\"https://twitter.com/search?q=%23bulma\">#bulma</a> <a href=\"https://twitter.com/search?q=%23cssframework\">#cssframework</a> has now permanently replaced \"the other guy.\" Both are great of course, but for me, <PERSON><PERSON><PERSON> offers a better customization experience. <a href=\"https://twitter.com/search?q=%23Winning\">#Winning</a>. New site launched. Thanks <a href=\"https://twitter.com/jgthms\">@jgthms</a>  for the great work!", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/978664275127316481/cmsQFeGI_normal.jpg", "hearts": 2, "retweets": 0}, "1025375916367794176": {"id": "1025375916367794176", "date": "3:40 PM - 3 Aug 2018", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> Hey! You're the best CSS Guru men!! I'm going to start using <a href=\"https://t.co/C7J7PPpI4T\">Bulma.io</a> !!! 😊", "fullname": "<PERSON>", "username": "xLukii_", "avatar": "https://pbs.twimg.com/profile_images/3019870303/4dba233a7fe9093f6a4e3f24ab15c20a_normal.jpeg", "hearts": 1, "retweets": 0}, "1032476756308119554": {"id": "1032476756308119554", "date": "4:56 AM - 23 Aug 2018", "content": "Okay, <PERSON><PERSON><PERSON> is life changing. Thank you <a href=\"https://twitter.com/jgthms\">@jgthms</a> for this.", "fullname": "<PERSON>", "username": "mart<PERSON><PERSON>p", "avatar": "https://pbs.twimg.com/profile_images/969416395753775106/h4pvENm5_normal.jpg", "hearts": 3, "retweets": 0}, "1033211102245646337": {"id": "1033211102245646337", "date": "6:34 AM - 25 Aug 2018", "content": "Very impressed with <PERSON><PERSON><PERSON> as a css framework. It feels more intuitive and natural than other frameworks... Will be using it to revamp my website which is currently using Bootstrap. Great work <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "lukemoore77", "avatar": "https://pbs.twimg.com/profile_images/1040415199403106304/rXNfPp85_normal.jpg", "hearts": 1, "retweets": 0}, "1037655983663861760": {"id": "1037655983663861760", "date": "12:57 PM - 6 Sep 2018", "content": "Bulma: i love <PERSON><PERSON><PERSON>, it's so awesome <a href=\"https://t.co/2nmnFiVkc4\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "Chapdel KAMGA", "username": "iamchapdel", "avatar": "https://pbs.twimg.com/profile_images/1404689515483119618/lfOZkxDc_normal.jpg", "hearts": 1, "retweets": 0}, "1038461036037324802": {"id": "1038461036037324802", "date": "6:16 PM - 8 Sep 2018", "content": "Hello <a href=\"https://twitter.com/jgthms\">@jgthms</a>!! <a href=\"https://t.co/AI3N0zq6im\">bulma.io</a> is the best, best, best CSS framework in the entire universe!!!!! Good Job!!!!!!", "fullname": "<PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1514810127164928001/CxDU0N3j_normal.jpg", "hearts": 0, "retweets": 0}, "1039577420053921792": {"id": "1039577420053921792", "date": "8:12 PM - 11 Sep 2018", "content": "<a href=\"https://t.co/SaUZYtugLV\">bulma.io</a> by <a href=\"https://twitter.com/jgthms\">@jgthms</a> looks very interesting . A css framework without any JS opinion. Finally I can say good bye to <PERSON><PERSON><PERSON> (&amp; Bootstrap).", "fullname": "<PERSON><PERSON>", "username": "tsaha", "avatar": "https://pbs.twimg.com/profile_images/1184958552730951681/_3Sn1yU-_normal.jpg", "hearts": 0, "retweets": 0}, "1039625285640118272": {"id": "1039625285640118272", "date": "10:22 PM - 11 Sep 2018", "content": "<a href=\"https://twitter.com/imkmf\">@imkmf</a> <PERSON><PERSON><PERSON>'s \"bring your own JavaScript\" has been a breath of fresh air compared to most other CSS frameworks I've used.", "fullname": "<PERSON><PERSON>", "username": "jody_lecompte", "avatar": "https://pbs.twimg.com/profile_images/1131246846477787136/bJ1ymDE__normal.jpg", "hearts": 11, "retweets": 0}, "1044117881582059520": {"id": "1044117881582059520", "date": "8:54 AM - 24 Sep 2018", "content": "Just wow! <br><a href=\"https://t.co/AubuenzIZ9\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/534824981461336064/oVXzUXdc_normal.jpeg", "hearts": 1, "retweets": 0}, "1047846962412900352": {"id": "1047846962412900352", "date": "2:52 PM - 4 Oct 2018", "content": "It's been a while since I had to touch CSS frameworks but <a href=\"https://t.co/UGVOuZaLQh\">bulma.io</a> really stands out! <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON><PERSON>", "username": "_niwilai", "avatar": "https://pbs.twimg.com/profile_images/964219682445250560/92M8M1yf_normal.jpg", "hearts": 2, "retweets": 1}, "1042348474002550784": {"id": "1042348474002550784", "date": "11:43 AM - 19 Sep 2018", "content": "Well, since I wasn't satisfied with 💯, I tweaked <PERSON><PERSON> a little and finally I can say my side project is super fast and SEO friendly, thanks to <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> <a href=\"https://twitter.com/search?q=%23madewithbulma\">#madewithbulma</a> <a href=\"https://t.co/pNDywukYf1\">pic.twitter.com/pNDywukYf1</a>", "fullname": "Θεόδωρος", "username": "kouna_to", "avatar": "https://pbs.twimg.com/profile_images/974950257275621376/br7e5UFB_normal.jpg", "hearts": 0, "retweets": 0}, "1051534293665808390": {"id": "1051534293665808390", "date": "8:04 PM - 14 Oct 2018", "content": "Man! &amp; then I find <a href=\"https://t.co/W44CpjNIxM\">Bulma.io</a> css Framework. As a new front end programmer I have  built  some stuff with css from scratch and yes, ordinary html &amp; css is fine of course, but hey, let's be honest... Once you go <PERSON><PERSON><PERSON> you don't go back!  <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "<PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1515047893618638860/Ko9kDIwN_normal.jpg", "hearts": 3, "retweets": 1}, "1051978773174734848": {"id": "1051978773174734848", "date": "1:30 AM - 16 Oct 2018", "content": "Shout out to <a href=\"https://twitter.com/jgthms\">@jgthms</a> for creating and maintaining Bulma. It is such a nice framework to work with.<br><br><a href=\"https://t.co/9oO3kHUyBA\">bulma.io</a>", "fullname": "<PERSON>", "username": "timacdonald87", "avatar": "https://pbs.twimg.com/profile_images/1495883253445959681/xLo3tosq_normal.jpg", "hearts": 3, "retweets": 0}, "1062394741092950018": {"id": "1062394741092950018", "date": "5:20 PM - 13 Nov 2018", "content": "<a href=\"https://twitter.com/SandricoP\">@SandricoP</a> <a href=\"https://twitter.com/treehouse\">@treehouse</a> Bulma created by <a href=\"https://twitter.com/jgthms\">@jgthms</a>. It's a slimmer and a more modern version of bootstrap. CSS only based off flex box. Check it out @ <a href=\"https://t.co/w8SDT2Qywd\">bulma.io</a><br><br>Now that React changed its license, it has been dominating. Easy to pick up as well. The docs are good too unlike <a href=\"https://twitter.com/angular\">@angular</a>", "fullname": "<PERSON>", "username": "extrogenesis", "avatar": "https://pbs.twimg.com/profile_images/1048344176608382977/zC1rmMQh_normal.jpg", "hearts": 2, "retweets": 0}, "1064150088480710657": {"id": "1064150088480710657", "date": "2:35 PM - 18 Nov 2018", "content": "Just showing my love ❤️to <PERSON><PERSON><PERSON>. It's so simple and clean I'd like to stay to live inside it. h<a href=\"https://t.co/KhJA4E3inw\">bulma.io</a>#<a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a>via @<a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "dani 🌻", "username": "danidlcdev", "avatar": "https://pbs.twimg.com/profile_images/1471838786304843778/jOuezVL2_normal.jpg", "hearts": 8, "retweets": 0}, "1064592414315372544": {"id": "1064592414315372544", "date": "7:53 PM - 19 Nov 2018", "content": "Hey! I just gave my website a small redesign <a href=\"https://t.co/j70k3Ot0GG\">jahir.xyz</a><br>🤩 Moved from GitHub Pages to <a href=\"https://twitter.com/Netlify\">@Netlify</a> <br>😎 It's <a href=\"https://twitter.com/search?q=%23MadeWithBulma\">#Made<PERSON>ithBulma</a><br>Go check it out! 😀🙌🏼", "fullname": "<PERSON><PERSON><PERSON> 💎", "username": "jahirfiquitiva", "avatar": "https://pbs.twimg.com/profile_images/1519333214619328512/meyInjaZ_normal.jpg", "hearts": 7, "retweets": 1}, "1064595042172583938": {"id": "1064595042172583938", "date": "8:03 PM - 19 Nov 2018", "content": "cc <a href=\"https://twitter.com/jgthms\">@jgthms</a> ... Awesome work on Bulma! 🙌🏼", "fullname": "<PERSON><PERSON><PERSON> 💎", "username": "jahirfiquitiva", "avatar": "https://pbs.twimg.com/profile_images/1519333214619328512/meyInjaZ_normal.jpg", "hearts": 1, "retweets": 0}, "1065162109900738560": {"id": "1065162109900738560", "date": "9:36 AM - 21 Nov 2018", "content": "Fantastic tool, special documentatia <br><a href=\"https://t.co/34egXED8xw\">bulma.io</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON><PERSON><PERSON>", "username": "tolyk21", "avatar": "https://pbs.twimg.com/profile_images/378800000014594417/7e45faae5069cf2f7d640083af083c07_normal.jpeg", "hearts": 0, "retweets": 0}, "1067168440333606912": {"id": "1067168440333606912", "date": "10:29 PM - 26 Nov 2018", "content": "Just dropped for my next project PureCSS – which I love – to try <a href=\"https://twitter.com/search?q=%23Bulma\">#Bulma</a> by <a href=\"https://twitter.com/jgthms\">@jgthms</a> 🤝 I'm already 100% in 💚 with it.<br>So many classes. Super cool <a href=\"https://twitter.com/search?q=%23UI\">#UI</a> elements. A complete documentation. <a href=\"https://twitter.com/search?q=%23Opensource\">#Opensource</a><br>I mean, if you didn't try Bulma <a href=\"https://twitter.com/search?q=%23css\">#css</a> yet, check it out 👉 <a href=\"https://t.co/jt5nPFhQaU\">bulma.io</a>", "fullname": "<PERSON>", "username": "<PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1390383333507481604/u33UkrEW_normal.jpg", "hearts": 31, "retweets": 9}, "1079283026285064192": {"id": "1079283026285064192", "date": "7:48 AM - 30 Dec 2018", "content": "After trying a few different web UI frameworks, I'm going with <a href=\"https://twitter.com/jgthms\">@jgthms</a>'s <a href=\"https://t.co/znG9vQ4TeE\">bulma.io</a> for all new projects. I love that nearly everything is only a CSS class. <a href=\"https://t.co/Na5eGHQSpt\">buefy.github.io</a> uses Vue components to clean up a few rough edges. <a href=\"https://twitter.com/search?q=%23lovingWebDevAgain\">#lovingWebDevAgain</a>", "fullname": "<PERSON>", "username": "kc8apf", "avatar": "https://pbs.twimg.com/profile_images/723320394128576513/oRFBSQZY_normal.jpg", "hearts": 6, "retweets": 0}, "1084590757254696965": {"id": "1084590757254696965", "date": "12:19 AM - 14 Jan 2019", "content": "Using <a href=\"https://twitter.com/search?q=%23bulma\">#bulma</a> for my e-commerce website and I... I fell in ❤️! #<a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a>via @<a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON><PERSON><PERSON>", "username": "devlob", "avatar": "https://pbs.twimg.com/profile_images/995404431767326726/cgv4qFXT_normal.jpg", "hearts": 3, "retweets": 1}, "1084720361927729152": {"id": "1084720361927729152", "date": "8:54 AM - 14 Jan 2019", "content": "Wow, impressive CSS framework @@ Bulma: Free, open source,  <a href=\"https://t.co/huhotWa2AB\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "vBizChain", "avatar": "https://pbs.twimg.com/profile_images/732415851958149120/9WNENsPs_normal.jpg", "hearts": 1, "retweets": 0}, "1084915146793852928": {"id": "1084915146793852928", "date": "9:48 PM - 14 Jan 2019", "content": "For all our <a href=\"https://twitter.com/search?q=%23ux\">#ux</a> / <a href=\"https://twitter.com/search?q=%23ui\">#ui</a> needs, <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> delivers. <a href=\"https://twitter.com/search?q=%23css3\">#css3</a>. <a href=\"https://twitter.com/search?q=%23sass\">#sass</a>. <a href=\"https://twitter.com/search?q=%23flexbox\">#flexbox</a>. <a href=\"https://twitter.com/search?q=%23simplicity\">#simplicity</a>. Everytime. Thanks <a href=\"https://twitter.com/jgthms\">@jgthms</a> for providing us with this wonderful library. <br><br><a href=\"https://t.co/DtAiIo7PWZ\">bulma.io</a>", "fullname": "Code ADN", "username": "code_adn", "avatar": "https://pbs.twimg.com/profile_images/789106225820688384/64_iMMTv_normal.jpg", "hearts": 2, "retweets": 0}, "1088447825803800577": {"id": "1088447825803800577", "date": "3:45 PM - 24 Jan 2019", "content": "<a href=\"https://twitter.com/signalnerve\">@signalnerve</a> <a href=\"https://twitter.com/jgthms\">@jgthms</a> B<PERSON><PERSON> is the best and it makes sense, it's the best", "fullname": "<PERSON>", "username": "JREAMdev", "avatar": "https://pbs.twimg.com/profile_images/999797647987781633/pB9rXBS5_normal.jpg", "hearts": 2, "retweets": 0}, "1093467834209001472": {"id": "1093467834209001472", "date": "12:13 PM - 7 Feb 2019", "content": "I built <a href=\"https://t.co/653Sdrv82D\">livechatlabs.com</a> with <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> 🙏 to <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "username": "ahoylabsHQ", "avatar": "https://pbs.twimg.com/profile_images/992349681844924417/5TkZzuIw_normal.jpg", "hearts": 9, "retweets": 1}, "1097215157711843328": {"id": "1097215157711843328", "date": "8:24 PM - 17 Feb 2019", "content": "I've never been a big fan of CSS frameworks, but I'm super impressed/at home reading the concepts behind Bulma. You may be changing my mind <a href=\"https://twitter.com/jgthms\">@jgthms</a>! <a href=\"https://twitter.com/search?q=%23bulmacss\">#bulmacss</a>", "fullname": "<PERSON>", "username": "rob______gordon", "avatar": "https://pbs.twimg.com/profile_images/1487104204585979908/2y9pmOOl_normal.jpg", "hearts": 8, "retweets": 0}, "1103377098490724352": {"id": "1103377098490724352", "date": "8:29 PM - 6 Mar 2019", "content": "Started using an awesome UI framework called Bulma.<br><br>Thanks to <a href=\"https://twitter.com/jgthms\">@jgthms</a> for making this awesome framework!<br><br>It's very fast, uses one file only, very easy to use and totally free!<br><br><a href=\"https://twitter.com/search?q=%23webdev\">#webdev</a> <a href=\"https://twitter.com/search?q=%23Webdesign\">#Webdesign</a> <a href=\"https://twitter.com/search?q=%23indiedev\">#indiedev</a> <a href=\"https://twitter.com/search?q=%23css3\">#css3</a> <a href=\"https://twitter.com/search?q=%23html5\">#html5</a>", "fullname": "<PERSON><PERSON>", "username": "WinterboltGames", "avatar": "https://pbs.twimg.com/profile_images/1473290497397596173/IULwoRnO_normal.jpg", "hearts": 3, "retweets": 0}, "1104190974106918912": {"id": "1104190974106918912", "date": "2:23 AM - 9 Mar 2019", "content": "OMG Bulma 🤟🤟🤟<br>Bulma: Free, open source, &amp; modern CSS framework based on Flexbox <a href=\"https://t.co/hDB3Xjv4v2\">bulma.io</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON><PERSON>", "username": "caloneil", "avatar": "https://pbs.twimg.com/profile_images/1548152965/me_4_colour_normal.jpg", "hearts": 1, "retweets": 0}, "1106622513159856131": {"id": "1106622513159856131", "date": "7:25 PM - 15 Mar 2019", "content": "I've been working on some <a href=\"https://twitter.com/reactjs\">@reactjs</a> layouts components along with some <a href=\"https://twitter.com/search?q=%23CSS\">#CSS</a> extensions to <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> from <a href=\"https://twitter.com/jgthms\">@jgthms</a> - Featuring side panels, collapsible/flexible boxes and popout window that stay in sync through react portals! I can create desktop like web apps now 👍 <a href=\"https://t.co/G7OqZD7XSn\">pic.twitter.com/G7OqZD7XSn</a>", "fullname": "<PERSON>", "username": "Repraze", "avatar": "https://pbs.twimg.com/profile_images/1383830684318330890/dnmG91QO_normal.jpg", "hearts": 17, "retweets": 3}, "1106971813014315010": {"id": "1106971813014315010", "date": "6:33 PM - 16 Mar 2019", "content": "I'm starting to love <a href=\"https://twitter.com/search?q=%23CSS\">#CSS</a> framework Bluma.<br><br>Will be hard to go back to <a href=\"https://twitter.com/search?q=%23Bootstrap\">#Bootstrap</a> now.<br><br>By: <a href=\"https://twitter.com/jgthms\">@jgthms</a><br><br><a href=\"https://t.co/hrTDYY993D\">bulma.io</a><br><a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a>", "fullname": "<PERSON>", "username": "magnus<PERSON>a", "avatar": "https://pbs.twimg.com/profile_images/841996680484552705/5Q-RmnRO_normal.jpg", "hearts": 3, "retweets": 0}, "1108831735419154432": {"id": "1108831735419154432", "date": "9:44 PM - 21 Mar 2019", "content": "Wow - Bulma CSS framework is absolutely brilliant! I don't think I'll miss <PERSON><PERSON><PERSON><PERSON><PERSON> (see, already forgot the name). Thank you for making it <a href=\"https://twitter.com/jgthms\">@jgthms</a> - great work indeed. <a href=\"https://t.co/1UTZgsnKOu\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulma\">#bulma</a>", "fullname": "<PERSON>", "username": "coderparker", "avatar": "https://pbs.twimg.com/profile_images/1146252833899106305/vFt8hafC_normal.png", "hearts": 1, "retweets": 0}, "1109195636308692994": {"id": "1109195636308692994", "date": "9:50 PM - 22 Mar 2019", "content": "<a href=\"https://t.co/tgb96Fx3KG\">bulma.io</a> <a href=\"https://twitter.com/jgthms\">@jgthms</a> is so freaking awesome 🤘👊", "fullname": "<PERSON><PERSON> 🇺🇦 🇸🇰", "username": "dev_danco", "avatar": "https://pbs.twimg.com/profile_images/1422841311355412481/ydM7CWBP_normal.jpg", "hearts": 4, "retweets": 0}, "1110678480507883520": {"id": "1110678480507883520", "date": "11:02 PM - 26 Mar 2019", "content": "<a href=\"https://twitter.com/ruthmills\">@ruthmills</a> <a href=\"https://twitter.com/jessicaisace\">@jessicaisace</a> Bootstrap is so bloated now! I like <a href=\"https://twitter.com/search?q=%23bulma\">#bulma</a> <a href=\"https://t.co/UvQEqZXIEl\">bulma.io/alternative-to…</a> by <a href=\"https://twitter.com/jgthms\">@jgthms</a> It's becoming very popular. No JS too 👍Also worth having a saved bookmark for <a href=\"https://t.co/REU9u4j5E5\">cssreference.io</a> Too many devs think <a href=\"https://twitter.com/search?q=%23CSS\">#CSS</a> is the poor-mans-friend! More powerful than ever 🤓", "fullname": "💾", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1167096511123140608/tTjdFrbM_normal.jpg", "hearts": 3, "retweets": 1}, "1111371782051762187": {"id": "1111371782051762187", "date": "8:57 PM - 28 Mar 2019", "content": "Just found <PERSON><PERSON><PERSON>. Where have you been all my life?<br><br><a href=\"https://t.co/Panj1Xk9Tz\">buff.ly/2qjxkpL</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a> <a href=\"https://t.co/kdeoMSDEuZ\">pic.twitter.com/kdeoMSDEuZ</a>", "fullname": "jcooke.me", "username": "jcookemn", "avatar": "https://pbs.twimg.com/profile_images/1510817016839614465/CKRRkgz0_normal.jpg", "hearts": 6, "retweets": 0}, "1111768111273762816": {"id": "1111768111273762816", "date": "12:12 AM - 30 Mar 2019", "content": "<a href=\"https://twitter.com/avdi\">@avdi</a> I’m supporting <a href=\"https://twitter.com/jgthms\">@jgthms</a> because <PERSON><PERSON><PERSON> is way cool. (First CSS framework in ages to get me to step off of ZURB Foundation…)", "fullname": "<PERSON> supports the Open Web", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>e", "avatar": "https://pbs.twimg.com/profile_images/1431863008288931841/hsLQJBv4_normal.jpg", "hearts": 2, "retweets": 0}, "1113143442991751168": {"id": "1113143442991751168", "date": "8:17 PM - 2 Apr 2019", "content": "I'm going to be spending a lot of time working on front end projects soon (several big projects underway!), and it couldn't be a more fortunate time to run across <a href=\"https://t.co/mCzRzwWd6t\">bulma.io</a>. Amazing work, thanks for making CSS not such a nightmare <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "AlexWelcing", "avatar": "https://pbs.twimg.com/profile_images/1428049946939375621/lI48q5r1_normal.jpg", "hearts": 5, "retweets": 0}, "1070320154452656128": {"id": "1070320154452656128", "date": "3:13 PM - 5 Dec 2018", "content": "Wanted a dark mode and discovered that in my build steps for <a href=\"https://twitter.com/bulmaio\">@bulmaio</a> inverting the values for <a href=\"https://twitter.com/search?q=%23black\">$black</a>, <a href=\"https://twitter.com/search?q=%23white\">$white</a> and greyscales and setting a bg-color for the html element is 90% of the work done. Relly great work by <a href=\"https://twitter.com/jgthms\">@jgthms</a> to make stuff like this SO easy! <a href=\"https://twitter.com/search?q=%23coding\">#coding</a> <a href=\"https://twitter.com/search?q=%23webdev\">#webdev</a> <a href=\"https://t.co/nm84fBXcbU\">pic.twitter.com/nm84fBXcbU</a>", "fullname": "<PERSON>", "username": "Lars<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1073168355505385472/WJu6Gnh0_normal.jpg", "hearts": 29, "retweets": 1}, "1071108327554265088": {"id": "1071108327554265088", "date": "7:25 PM - 7 Dec 2018", "content": "I am loving it, so awesome!!!<br><a href=\"https://t.co/CJvKvzPR4L\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "saint<PERSON>i<PERSON>", "username": "mimremo", "avatar": "https://pbs.twimg.com/profile_images/1516208225984647175/hZc7a685_normal.jpg", "hearts": 2, "retweets": 0}, "1071179047244316677": {"id": "1071179047244316677", "date": "11:06 PM - 7 Dec 2018", "content": "Este framework es lo que necesitaba y no creo cambiarlo,  <a href=\"https://t.co/HmWAnzQ56q\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> vía <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "hackemate", "username": "hackemate_ninja", "avatar": "https://pbs.twimg.com/profile_images/1203204119739162624/PxXuw4mX_normal.jpg", "hearts": 0, "retweets": 0}, "1072757345959247873": {"id": "1072757345959247873", "date": "8:37 AM - 12 Dec 2018", "content": "Never worked with <PERSON><PERSON><PERSON> before. I'm liking it a lot! Thanks <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "TrvlMike", "avatar": "https://pbs.twimg.com/profile_images/615554825451798529/-uAVXJFL_normal.jpg", "hearts": 1, "retweets": 0}, "1073285247473721345": {"id": "1073285247473721345", "date": "6:35 PM - 13 Dec 2018", "content": "Clean, light and super cool css framework without any garbage, Fantastic Bulma &lt;3  <a href=\"https://t.co/E7n5UEG4QK\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/650582432136200192/1g5rXyWb_normal.jpg", "hearts": 0, "retweets": 0}, "1073405452166283264": {"id": "1073405452166283264", "date": "3:32 AM - 14 Dec 2018", "content": "I just discovered <a href=\"https://t.co/mo9FqQNWSX\">bulma.io</a> and its amazing!  <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> is a clean, simple and JS-free CSS framework, goodbye bootstrap! Good job <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "cominous", "avatar": "https://pbs.twimg.com/profile_images/1064329627458564096/IrgVQi6l_normal.jpg", "hearts": 2, "retweets": 0}, "1076031886332440576": {"id": "1076031886332440576", "date": "9:29 AM - 21 Dec 2018", "content": "Wow! really awesome..... <a href=\"https://t.co/IuACUfZ9NI\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON><PERSON><PERSON>", "username": "herevivekt", "avatar": "https://pbs.twimg.com/profile_images/1211931684653846530/O6snWsNC_normal.jpg", "hearts": 0, "retweets": 0}, "1076306162277072897": {"id": "1076306162277072897", "date": "3:39 AM - 22 Dec 2018", "content": "So today I came across <a href=\"https://t.co/7MJQ1NTjXl\">bulma.io</a> and it was like Love at first sight 😎 <a href=\"https://twitter.com/jgthms\">@jgthms</a> thanks 👍🏼. #<a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a>#<a href=\"https://twitter.com/search?q=%23bulma\">#bulma</a>#<a href=\"https://twitter.com/search?q=%23css\">#css</a>#<a href=\"https://twitter.com/search?q=%23webdev\">#webdev</a>#<a href=\"https://twitter.com/search?q=%23coding\">#coding</a>", "fullname": "<PERSON><PERSON>", "username": "CodesHalilo", "avatar": "https://pbs.twimg.com/profile_images/1265992063306338305/3nuF7f_7_normal.jpg", "hearts": 1, "retweets": 0}, "1076520024779751424": {"id": "1076520024779751424", "date": "4:49 PM - 22 Dec 2018", "content": "Today I just discovered Bluma 😍<br>Best CSS framework,<br>Flexbox Grid,<br>No JS,<br>Modular components,<br>Sass.<br><br>Awesome work <a href=\"https://twitter.com/jgthms\">@jgthms</a>.", "fullname": "SHAHROOZ", "username": "sha<PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1263770451580461056/wyPFM6fl_normal.jpg", "hearts": 7, "retweets": 1}, "1114064132364488705": {"id": "1114064132364488705", "date": "9:16 AM - 5 Apr 2019", "content": "Worked with <a href=\"https://twitter.com/search?q=%23bulma\">#bulma</a> <a href=\"https://twitter.com/search?q=%23css\">#css</a> <a href=\"https://twitter.com/search?q=%23framework\">#framework</a> from <a href=\"https://twitter.com/jgthms\">@jgthms</a>  lightweight, easy to use =&gt; great 🤗 check out <a href=\"https://t.co/bk7YlM2ZPW\">bulma.io</a>", "fullname": "<PERSON><PERSON><PERSON>", "username": "f5nole", "avatar": "https://pbs.twimg.com/profile_images/1040152228747780097/cFmMeLOX_normal.jpg", "hearts": 2, "retweets": 1}, "1114085081050755072": {"id": "1114085081050755072", "date": "10:39 AM - 5 Apr 2019", "content": "<a href=\"https://twitter.com/thorstenball\">@thorstenball</a> Not sure if minimal enough for you but I find <a href=\"https://t.co/yYPDwbo2cE\">bulma.io</a> by <a href=\"https://twitter.com/jgthms\">@jgthms</a> awesome!", "fullname": "<PERSON><PERSON>", "username": "_tomekw", "avatar": "https://pbs.twimg.com/profile_images/1259455345845510144/URs8rxR8_normal.jpg", "hearts": 1, "retweets": 0}, "1123933715556597761": {"id": "1123933715556597761", "date": "2:54 PM - 2 May 2019", "content": "I'll always praise a good documentation: <a href=\"https://twitter.com/jgthms\">@jgthms</a> kudos to you for the awesomeness of bulma! 🎉👏", "fullname": "<PERSON><PERSON><PERSON>", "username": "AlvaroFernandom", "avatar": "https://pbs.twimg.com/profile_images/1044605279466401792/ifSN6kLr_normal.jpg", "hearts": 1, "retweets": 0}, "1123938071957909508": {"id": "1123938071957909508", "date": "2:11 PM - 2 May 2019", "content": "Third iteration of my landing page, now (haha) hosted on <a href=\"https://twitter.com/zeithq\">@zeithq</a> Now, made using <a href=\"https://twitter.com/jgthms\">@jgthms</a>' <a href=\"https://twitter.com/search?q=%23bulma\">#bulma</a> and <a href=\"https://twitter.com/gatsbyjs\">@gatsbyjs</a>, and heavily based on <a href=\"https://twitter.com/fschultz_\">@fschultz_</a>'s gatsby-universal repo ✨<br><br><a href=\"https://t.co/DIURVKenKW\">griko.id</a>", "fullname": "<PERSON><PERSON><PERSON>", "username": "griko_nibras", "avatar": "https://pbs.twimg.com/profile_images/1166074077645131776/2xwMfUfk_normal.jpg", "hearts": 4, "retweets": 0}, "1126857886599122949": {"id": "1126857886599122949", "date": "3:33 PM - 10 May 2019", "content": "<PERSON><PERSON><PERSON> CSS by <a href=\"https://twitter.com/jgthms\">@jgthms</a> is just perfect. Simple, easily customizable and doesn't impose Javascript implementations.<br><br>After experimenting with <a href=\"https://twitter.com/tailwindcss\">@tailwindcss</a> and finding that utility first css is just not my thing, <PERSON><PERSON><PERSON> felt like a breath of fresh air.<br><br><a href=\"https://twitter.com/search?q=%23webdevelopment\">#webdevelopment</a> <a href=\"https://twitter.com/search?q=%23CSS\">#CSS</a>", "fullname": "<PERSON>.", "username": "ale_codes", "avatar": "/images/twitter/ale_codes.jpg", "hearts": 34, "retweets": 6}, "1127131435498762245": {"id": "1127131435498762245", "date": "10:40 AM - 11 May 2019", "content": "Thank you <a href=\"https://twitter.com/jgthms\">@jgthms</a> for Bulma CSS. It is a life saver.", "fullname": "<PERSON><PERSON><PERSON> 📈👨‍💻", "username": "a<PERSON>sh<PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1410407873801187331/QiajPDje_normal.jpg", "hearts": 2, "retweets": 0}, "1127151956940722177": {"id": "1127151956940722177", "date": "12:02 PM - 11 May 2019", "content": "@ujj<PERSON>_ch_king <a href=\"https://twitter.com/jgthms\">@jgthms</a> , everyone loves bulma...", "fullname": "<PERSON>", "username": "<PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1515047893618638860/Ko9kDIwN_normal.jpg", "hearts": 1, "retweets": 0}, "1127287630872424449": {"id": "1127287630872424449", "date": "9:01 PM - 11 May 2019", "content": "<a href=\"https://twitter.com/abarreraaponte\">@abarreraaponte</a> <a href=\"https://twitter.com/jgthms\">@jgthms</a> <a href=\"https://twitter.com/tailwindcss\">@tailwindcss</a> Bulma.... Super fast, super lite, super ...everything", "fullname": "Θεόδωρος", "username": "kouna_to", "avatar": "https://pbs.twimg.com/profile_images/974950257275621376/br7e5UFB_normal.jpg", "hearts": 2, "retweets": 0}, "1133491490263621638": {"id": "1133491490263621638", "date": "11:53 PM - 28 May 2019", "content": "Started a new side project with <a href=\"https://t.co/RIBTAjujQg\">bulma.io</a> Awesome devX, super flexible and great docs &amp; code org. Easily gets 🏆 as my favourite CSS framework for rapid prototyping. 💜 Thanks <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON><PERSON>", "username": "tair", "avatar": "https://pbs.twimg.com/profile_images/1502744438245842946/o4y6CrbI_normal.jpg", "hearts": 4, "retweets": 0}, "1136497784700514304": {"id": "1136497784700514304", "date": "6:59 AM - 6 Jun 2019", "content": "<PERSON><PERSON><PERSON> is now my go to framework. Great work! <a href=\"https://t.co/ecCosnQ4ub\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "WPCoderCA", "avatar": "https://pbs.twimg.com/profile_images/869701430210441217/Q3snzw2S_normal.jpg", "hearts": 0, "retweets": 0}, "1137040277804371970": {"id": "1137040277804371970", "date": "5:55 PM - 7 Jun 2019", "content": "To all <a href=\"https://twitter.com/search?q=%23FrontEnd\">#FrontEnd</a> <a href=\"https://twitter.com/search?q=%23dev\">#dev</a> out there! If you are looking for a very clean and simple to use CSS framework, I can highly recommend <a href=\"https://t.co/lsCJFOhk8P\">bulma.io</a> by <a href=\"https://twitter.com/jgthms\">@jgthms</a>!", "fullname": "<PERSON><PERSON><PERSON>", "username": "flomueller1981", "avatar": "https://pbs.twimg.com/profile_images/1110648123196616705/GlXqIniQ_normal.jpg", "hearts": 7, "retweets": 0}, "1137697312384475137": {"id": "1137697312384475137", "date": "2:25 PM - 9 Jun 2019", "content": "<a href=\"https://t.co/YFWCBa9s4x\">bulma.io</a> has got to be the best CSS framework out there. <a href=\"https://twitter.com/tailwindcss\">@tailwindcss</a> is great and all but <PERSON><PERSON>ma CSS really keeps it simple, no JS required. You don't need to be a Front-End wizard to figure it out. Great job <a href=\"https://twitter.com/jgthms\">@jgthms</a><br><br><a href=\"https://twitter.com/search?q=%23Bulma\">#Bulma</a> <a href=\"https://twitter.com/search?q=%23BulmaCSS\">#BulmaCSS</a> <a href=\"https://twitter.com/search?q=%23CSS\">#CSS</a> <a href=\"https://twitter.com/search?q=%23Bootstrap\">#Bootstrap</a> <a href=\"https://twitter.com/search?q=%23Tailwind\">#Tailwind</a>", "fullname": "<PERSON> 🇨🇲🇿🇦", "username": "pstevek", "avatar": "https://pbs.twimg.com/profile_images/1487870177060478976/E83MDC7j_normal.jpg", "hearts": 1, "retweets": 2}, "1140839283692982274": {"id": "1140839283692982274", "date": "6:30 AM - 18 Jun 2019", "content": "Okay so with <a href=\"https://twitter.com/jgthms\">@jgthms</a>’s <PERSON><PERSON><PERSON>, I was able to make each one of my pages on my website’s templates in one single day with no prior knowledge on how to use it, the docs are super easy to understand and it’s a great library. <a href=\"https://twitter.com/search?q=%23css\">#css</a>", "fullname": "Bruh", "username": "bruh<PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1132778579689762817/xyzQ4dAS_normal.png", "hearts": 10, "retweets": 4}, "1141831637497368576": {"id": "1141831637497368576", "date": "12:14 AM - 21 Jun 2019", "content": "Redesigned, re-introducing <a href=\"https://t.co/OuerxoJXKt\">bdov.dev</a> 2.0. Shout-out to <a href=\"https://twitter.com/jgthms\">@jgthms</a> for <PERSON><PERSON><PERSON>, my new favorite CSS framework, and <a href=\"https://twitter.com/zeithq\">@zeithq</a> for making deployment so, so simple. <a href=\"https://twitter.com/search?q=%23portfolio\">#portfolio</a>", "fullname": "<PERSON>", "username": "bdov_", "avatar": "https://pbs.twimg.com/profile_images/1025193187709661185/ujjZSYIT_normal.jpg", "hearts": 6, "retweets": 0}, "1144724239133609985": {"id": "1144724239133609985", "date": "11:48 PM - 28 Jun 2019", "content": "after one week on bulma. I'am in Love   <a href=\"https://t.co/J0RVt2kpAz\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "_<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1157782676205649922/Wa7hAg4A_normal.jpg", "hearts": 3, "retweets": 0}, "1148364620714958848": {"id": "1148364620714958848", "date": "12:53 AM - 9 Jul 2019", "content": "Building a new site right now that I can't wait to show off soon.  <a href=\"https://twitter.com/jgthms\">@jgthms</a> makes it easy thanks to <a href=\"https://t.co/QGgJgr7vaq\">bulma.io</a><br><br>Loving working with it on this project.", "fullname": "Prance (They/Them)🔞", "username": "pranceTheDeer", "avatar": "https://pbs.twimg.com/profile_images/1450864818530013191/yG8ctVrG_normal.jpg", "hearts": 11, "retweets": 3}, "1150109429238501376": {"id": "1150109429238501376", "date": "8:27 PM - 13 Jul 2019", "content": "Estoy haciendo un sitio web con bulma css, mientras mas lo conozco mas me encanta!, <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "mgznv1", "avatar": "https://pbs.twimg.com/profile_images/1146551208603111424/ChdXXZKE_normal.png", "hearts": 1, "retweets": 0}, "1150151867898503171": {"id": "1150151867898503171", "date": "11:15 PM - 13 Jul 2019", "content": "So very impressed with <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a>, a real joy to use! <a href=\"https://t.co/da5xmgCPj2\">bulma.io</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "jme_lord", "avatar": "https://pbs.twimg.com/profile_images/1144306203520057344/oSMBnMv0_normal.jpg", "hearts": 1, "retweets": 0}, "1154033397741699073": {"id": "1154033397741699073", "date": "4:19 PM - 24 Jul 2019", "content": "Just finished redesigning my website using <a href=\"https://twitter.com/jgthms\">@jgthms</a>'s <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> used any JS. jQuery has it's uses but I'm loving this CSS only framework. Thanks a lot <PERSON>!  <a href=\"https://t.co/aerZwbzcWh\">weboreviews.com</a> <a href=\"https://twitter.com/search?q=%23madewithbulma\">#madewithbulma</a>", "fullname": "akya", "username": "<PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1308991349367209984/flnL5UFC_normal.jpg", "hearts": 1, "retweets": 0}, "1156893616587059200": {"id": "1156893616587059200", "date": "1:45 PM - 1 Aug 2019", "content": "Our new website was made with <PERSON>ul<PERSON>! Thanks <a href=\"https://twitter.com/jgthms\">@jgthms</a> for this amazing framework!<br><a href=\"https://twitter.com/search?q=%23madewithbulma\">#madewithbulma</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a>.<br><br><a href=\"https://t.co/2TnY6R7228\">bulma.io</a>", "fullname": "centiv.io", "username": "usecentiv", "avatar": "https://pbs.twimg.com/profile_images/1218619541418233856/5NE0UHYh_normal.png", "hearts": 1, "retweets": 0}, "1161404522331344896": {"id": "1161404522331344896", "date": "12:29 AM - 14 Aug 2019", "content": "Bloody hell.... To think that I was using my own simple framework for all of the <a href=\"https://twitter.com/search?q=%23Frontend\">#Frontend</a> <a href=\"https://twitter.com/search?q=%23webdevelopment\">#webdevelopment</a>. Here is a my old login page vs <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> framework... Me so dumb. Why reinvent the wheel? Amazing job <a href=\"https://twitter.com/jgthms\">@jgthms</a> <a href=\"https://twitter.com/search?q=%23CSS\">#CSS</a> <a href=\"https://t.co/X5NnKZxyQq\">pic.twitter.com/X5NnKZxyQq</a>", "fullname": "db4you", "username": "db4you2", "avatar": "https://pbs.twimg.com/profile_images/1283183292683038727/wVhp9AQr_normal.jpg", "hearts": 22, "retweets": 4}, "1163489162630029312": {"id": "1163489162630029312", "date": "6:33 PM - 19 Aug 2019", "content": "Starting a new project today and I'm thinking of using <a href=\"https://twitter.com/search?q=%23Bulma\">#Bulma</a>.  Great framework for quick prototyping and gives me the freedom I need to style parts as I need it. Nice work <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "Worth<PERSON>_Stuff", "avatar": "https://pbs.twimg.com/profile_images/1509186677574492165/SYJ8qlWU_normal.jpg", "hearts": 1, "retweets": 0}, "1161234269257097221": {"id": "1161234269257097221", "date": "1:13 PM - 13 Aug 2019", "content": "Woo, finally redid my personal website. Bulma framework made it a blast! <a href=\"https://t.co/sAWQhqDnO4\">thomasulman.com</a> <a href=\"https://twitter.com/search?q=%23madewithbulma\">#madewithbulma</a>", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1274996018413617154/0U0yO5p0_normal.jpg", "hearts": 2, "retweets": 0}, "1168645153525026819": {"id": "1168645153525026819", "date": "12:01 AM - 3 Sep 2019", "content": "Thankfully I stumbled across <PERSON><PERSON><PERSON> (<a href=\"https://t.co/nvk3Qmlu4L\">bulma.io</a>).  It is incredibly easy to use because of its clean and concise syntax.  The documentation is phenomenal.  Beginners should definitely give it a try.  Great job <a href=\"https://twitter.com/jgthms\">@jgthms</a>!  <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> <a href=\"https://twitter.com/search?q=%23css\">#css</a>", "fullname": "<PERSON>", "username": "carlwgeorge", "avatar": "https://pbs.twimg.com/profile_images/1452692251486867462/rxbY5j8b_normal.jpg", "hearts": 3, "retweets": 0}, "1168739286419681281": {"id": "1168739286419681281", "date": "6:15 AM - 3 Sep 2019", "content": "<a href=\"https://twitter.com/carlwgeorge\">@carlwgeorge</a> <a href=\"https://twitter.com/jgthms\">@jgthms</a> Same here. Found <PERSON><PERSON><PERSON> just 3 days ago and it’s quite different. And beautiful by default", "fullname": "<PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1442842933821165570/p_qiGy4l_normal.jpg", "hearts": 2, "retweets": 0}, "1171067948183822337": {"id": "1171067948183822337", "date": "4:28 PM - 9 Sep 2019", "content": "Thanks for <PERSON><PERSON><PERSON> <a href=\"https://twitter.com/jgthms\">@jgthms</a>, love it! 🔥 Will be releasing more products using the framework. <a href=\"https://twitter.com/search?q=%23madewithbulma\">#madewithbulma</a>", "fullname": "T<PERSON><PERSON>z", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1496111034016407552/Keigb1-s_normal.jpg", "hearts": 1, "retweets": 0}, "1173823641026465793": {"id": "1173823641026465793", "date": "5:58 AM - 17 Sep 2019", "content": "I started using bulma on every project . Its simple and clean no imposed JavaScript implementations <a href=\"https://t.co/Uf5J3SPa4f\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON><PERSON>", "username": "elvin_ega", "avatar": "https://pbs.twimg.com/profile_images/960963101976915968/liy6MK2C_normal.jpg", "hearts": 0, "retweets": 0}, "1174376773775216640": {"id": "1174376773775216640", "date": "6:36 PM - 18 Sep 2019", "content": "This <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> that <a href=\"https://twitter.com/jgthms\">@jgthms</a> built is a pretty nifty thing! Very easy to refactor with and only bring what you need. Excited to see where it goes when 1.0 releases!", "fullname": "<PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1019264421212905474/P5Y77jek_normal.jpg", "hearts": 0, "retweets": 0}, "1176845849629995009": {"id": "1176845849629995009", "date": "3:08 PM - 25 Sep 2019", "content": "The Bulma (<a href=\"https://twitter.com/jgthms\">@jgthms</a>) and <a href=\"https://twitter.com/Ghost\">@Ghost</a> for building a blog is so remarkable. <PERSON><PERSON><PERSON> takes care of the design and Ghost of writing. And both can be/are minimalistic - exactly what you need for simple blogging without design restrictions. <a href=\"https://twitter.com/search?q=%23Bulma\">#Bulma</a> <a href=\"https://twitter.com/search?q=%23blog\">#blog</a> <br><br><a href=\"https://t.co/67dwH4KPfK\">martinverbic.com</a>", "fullname": "<PERSON>", "username": "martin_verbic", "avatar": "https://pbs.twimg.com/profile_images/1282616915073081348/blr9itmR_normal.jpg", "hearts": 12, "retweets": 3}, "1176845850909270016": {"id": "1176845850909270016", "date": "3:08 PM - 25 Sep 2019", "content": "Check out <PERSON><PERSON><PERSON> at <a href=\"https://t.co/iNBsGlZC1y\">bulma.io</a>....I was able to make a complete makeover of my blog in less than a day a few weeks ago.", "fullname": "<PERSON>", "username": "martin_verbic", "avatar": "https://pbs.twimg.com/profile_images/1282616915073081348/blr9itmR_normal.jpg", "hearts": 3, "retweets": 0}, "1176908216984395777": {"id": "1176908216984395777", "date": "6:15 PM - 25 Sep 2019", "content": "<a href=\"https://twitter.com/marteensart\">@marteensart</a> <a href=\"https://twitter.com/jgthms\">@jgthms</a> <a href=\"https://twitter.com/Ghost\">@Ghost</a> I love <PERSON><PERSON><PERSON>. I’m no designer but have enjoyed building my site with it. The syntax and everything is a pleasure to work with. I use Jekyll + Github + Netlify. Pushing my commits with the GH desktop app and letting Netlify do its magic.", "fullname": "the only dennis on the nice list", "username": "thismyrealone", "avatar": "https://pbs.twimg.com/profile_images/1332902822866612224/pS9ubclp_normal.jpg", "hearts": 1, "retweets": 0}, "1178406461795426304": {"id": "1178406461795426304", "date": "10:29 PM - 29 Sep 2019", "content": "<PERSON><PERSON><PERSON> felt like a breath of fresh air.<br><a href=\"https://twitter.com/jgthms\">@jgthms</a><br><br><a href=\"https://twitter.com/search?q=%23bulma\">#bulma</a> <br><a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a><br><a href=\"https://twitter.com/search?q=%23programmer\">#programmer</a>", "fullname": "Obka", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1490773046516490248/srz7KooV_normal.jpg", "hearts": 4, "retweets": 0}, "1182555739388178433": {"id": "1182555739388178433", "date": "8:17 AM - 11 Oct 2019", "content": "using what we need, it's neat and lightweight. <a href=\"https://t.co/vDb7BsNw3l\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "♉️", "username": "your<PERSON>ct<PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1411323570957930500/fgBSk7FI_normal.jpg", "hearts": 0, "retweets": 0}, "1184156232355078147": {"id": "1184156232355078147", "date": "7:17 PM - 15 Oct 2019", "content": "We've finished decision process on frontend and backend stacks of our new - Real Estate CRM. Backend: Directus Headless CMS (<a href=\"https://twitter.com/directus\">@directus</a>) + PHP + MySQL, Frontend: Bulma Css (<a href=\"https://twitter.com/jgthms\">@jgthms</a>) + Vue (<a href=\"https://twitter.com/vuejs\">@vuejs</a>) + Bulkit (<a href=\"https://twitter.com/cssninjaStudio\">@cssninjaStudio</a>). Big thanks to all mentioned for wonderful work you did!", "fullname": "Hyper38 Team", "username": "Hyper38T", "avatar": "https://pbs.twimg.com/profile_images/1088767993646104577/jYevEo0V_normal.jpg", "hearts": 3, "retweets": 0}, "1186740530446766080": {"id": "1186740530446766080", "date": "10:26 PM - 22 Oct 2019", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> <PERSON><PERSON><PERSON> has helped me write so much more code without worrying about the little gotchas of CSS. The core set of components are great! Thanks! <a href=\"https://twitter.com/search?q=%23madewithbulma\">#madewithbulma</a> <a href=\"https://t.co/FBiSggqznm\">ulluminate.com</a>", "fullname": "Ulluminate", "username": "ulluminate", "avatar": "https://pbs.twimg.com/profile_images/1153491976270073856/sFa0saXh_normal.png", "hearts": 0, "retweets": 0}, "1187213882604974081": {"id": "1187213882604974081", "date": "5:47 AM - 24 Oct 2019", "content": "<a href=\"https://twitter.com/reactjs\">@reactjs</a> <a href=\"https://twitter.com/olkesh\">@olkesh</a> I did originally try to hack it with my own CSS skills, but <PERSON><PERSON><PERSON> (<a href=\"https://twitter.com/jgthms\">@jgthms</a>) makes this look SO sick (I'm actually mid-refactor as I Tweet this 😅) <a href=\"https://t.co/mPmtBSMTQh\">pic.twitter.com/mPmtBSMTQh</a>", "fullname": "kayak with a \"k\"", "username": "__kayak__", "avatar": "https://pbs.twimg.com/profile_images/1279244926597750784/jPuQh69l_normal.jpg", "hearts": 1, "retweets": 0}, "1190345576979804160": {"id": "1190345576979804160", "date": "7:11 PM - 1 Nov 2019", "content": "I truly am a fan. Already built three websites running on Bulma (and <a href=\"https://twitter.com/search?q=%23buefy\">#buefy</a>) and they are live and kicking in production. Awesome. <a href=\"https://t.co/KqOV0e17WS\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON><PERSON>", "username": "axtg", "avatar": "https://pbs.twimg.com/profile_images/577897564718125057/rfcE4jqt_normal.jpeg", "hearts": 3, "retweets": 1}, "1191428460562046976": {"id": "1191428460562046976", "date": "7:54 PM - 4 Nov 2019", "content": "Together with <a href=\"https://twitter.com/BreznikMark\">@BreznikMark</a> I will host a three-part workshop on building blogs on <a href=\"https://twitter.com/Ghost\">@Ghost</a> using <PERSON><PERSON><PERSON> (<a href=\"https://twitter.com/jgthms\">@jgthms</a> ) - which I think is by far the best way to do so. It will be based on the fact that we learn best by making.<br><br>Starting Thursday.", "fullname": "<PERSON>", "username": "martin_verbic", "avatar": "https://pbs.twimg.com/profile_images/1282616915073081348/blr9itmR_normal.jpg", "hearts": 2, "retweets": 0}, "1192515272810348545": {"id": "1192515272810348545", "date": "7:52 PM - 7 Nov 2019", "content": "I've decided to use <a href=\"https://twitter.com/jgthms\">@jgthms</a>’s Bulma grid system for a new project, looks very promising so far<br><a href=\"https://twitter.com/search?q=%23CSS\">#CSS</a> <br><a href=\"https://t.co/riI2dnkIGw\">bulma.io/documentation/…</a>", "fullname": "<PERSON><PERSON><PERSON> (<PERSON>) <PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1138890151021686784/lNAQtjcF_normal.png", "hearts": 2, "retweets": 0}, "1193898621827108864": {"id": "1193898621827108864", "date": "3:29 PM - 11 Nov 2019", "content": "Site was built with my fave OTP, <a href=\"https://twitter.com/GatsbyJS\">@GatsbyJS</a> + <PERSON><PERSON><PERSON> (<a href=\"https://twitter.com/jgthms\">@jgthms</a>), plus easy deployment via <a href=\"https://twitter.com/Netlify\">@Netlify</a> 💜💚💙", "fullname": "<PERSON><PERSON><PERSON> 👩🏽‍💻", "username": "mc<PERSON><PERSON>o", "avatar": "https://pbs.twimg.com/profile_images/1446169268031135797/tPDAtpDi_normal.jpg", "hearts": 5, "retweets": 1}, "1198224190479970306": {"id": "1198224190479970306", "date": "12:58 PM - 23 Nov 2019", "content": "Along testing the new <a href=\"https://twitter.com/Ghost\">@Ghost</a> membership features and turning my blog into subscriber-exclusive one, I experimented with my homepage design. New post cards, new footer and a new header. I like where this is going. <a href=\"https://twitter.com/jgthms\">@jgthms</a> <br><br><a href=\"https://t.co/aipWQuXlCZ\">martinverbic.com</a> <a href=\"https://t.co/vWU5FRucXH\">pic.twitter.com/vWU5FRucXH</a>", "fullname": "<PERSON>", "username": "martin_verbic", "avatar": "https://pbs.twimg.com/profile_images/1282616915073081348/blr9itmR_normal.jpg", "hearts": 3, "retweets": 1}, "1198355347188125696": {"id": "1198355347188125696", "date": "9:39 PM - 23 Nov 2019", "content": "Excited about using B<PERSON>ma CSS in my project!<br><br>I've been reluctant to use CSS frameworks, basically because I want to be cool and just use Sass. But I need to be quick, and <PERSON><PERSON><PERSON> is just so nice <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "manuiswriting", "avatar": "https://pbs.twimg.com/profile_images/1152700255886069760/suwaR3zF_normal.jpg", "hearts": 1, "retweets": 0}, "1200344290683211777": {"id": "1200344290683211777", "date": "10:22 AM - 29 Nov 2019", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a>  Truly nice work with the <a href=\"https://twitter.com/search?q=%23bulma\">#bulma</a> CSS framework, it makes <a href=\"https://twitter.com/search?q=%23bootstrap\">#bootstrap</a> look like child's play. Impressive stuff !!!", "fullname": "Farai", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1392579646236119041/MsoZ4QqG_normal.jpg", "hearts": 1, "retweets": 0}, "1202835269856067584": {"id": "1202835269856067584", "date": "7:20 AM - 6 Dec 2019", "content": "Recently I started styling with Bulma CSS. Really It is cool and handy. They have maintaining a perfect documentation for all the features.<br><br>Bulma: Free, open source, and modern CSS framework based on Flexbox <a href=\"https://t.co/QnmwqOEa1a\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "D Software Machinist", "username": "softwrmachinist", "avatar": "https://pbs.twimg.com/profile_images/824142443583078400/arKMdhaH_normal.jpg", "hearts": 4, "retweets": 0}, "1206279201021558784": {"id": "1206279201021558784", "date": "7:25 PM - 15 Dec 2019", "content": "I've decided to use <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> on my new project and I am very satisfied, thank you <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "joao victor", "username": "geonizeli1", "avatar": "https://pbs.twimg.com/profile_images/1471647847766544388/XxSJTcn1_normal.jpg", "hearts": 1, "retweets": 0}, "1206630761371095040": {"id": "1206630761371095040", "date": "6:42 PM - 16 Dec 2019", "content": "<a href=\"https://twitter.com/search?q=%23Bulma\">#Bulma</a> made this possible and slowly I am following in 💓 with the framework <a href=\"https://twitter.com/search?q=%23css\">#css</a> <a href=\"https://twitter.com/jgthms\">@jgthms</a>. I should thank <a href=\"https://twitter.com/l2fprod\">@l2fprod</a> for introducing.", "fullname": "<PERSON><PERSON><PERSON><PERSON>", "username": "VidyasagarMSC", "avatar": "https://pbs.twimg.com/profile_images/1517509563947364352/xymQQQrH_normal.jpg", "hearts": 0, "retweets": 0}, "1208802132930883590": {"id": "1208802132930883590", "date": "5:31 PM - 22 Dec 2019", "content": "Built my first site from scratch on <a href=\"https://t.co/mJ8ZNY7cN9\">maskys.com</a> as I start job hunting. Thanks <a href=\"https://twitter.com/jgthms\">@jgthms</a> for creating <a href=\"https://twitter.com/bulmaio\">@bulmaio</a> and making the design-to-good-looking-site process a LOT less painful! 💜<br><br>Would love to hear feedback/bug reports/suggestions <a href=\"https://twitter.com/search?q=%23designtwitter\">#designtwitter</a> <a href=\"https://twitter.com/search?q=%23webdevelopment\">#webdevelopment</a>", "fullname": "<PERSON><PERSON><PERSON>", "username": "maskys_", "avatar": "https://pbs.twimg.com/profile_images/1345962322578182144/3XOQ-HLP_normal.jpg", "hearts": 7, "retweets": 1}, "1210753033404960770": {"id": "1210753033404960770", "date": "3:43 AM - 28 Dec 2019", "content": "Stuff done so far today on my registration system:<br><br>- Decided to use devise instead of myMLH because admin roles were gonna be questionable.<br>- Created gorgeous custom login and signup views with Bulma CSS by <a href=\"https://twitter.com/jgthms\">@jgthms</a> <br>- Learned Stimulus JS and used it for a navbar toggle <a href=\"https://t.co/Gxu0ZymfFH\">pic.twitter.com/Gxu0ZymfFH</a>", "fullname": "<PERSON>", "username": "sam<PERSON><PERSON>t", "avatar": "https://pbs.twimg.com/profile_images/1513700277173506051/FOFA24cc_normal.jpg", "hearts": 2, "retweets": 0}, "1211006304598319106": {"id": "1211006304598319106", "date": "8:29 PM - 28 Dec 2019", "content": "Easy to learn, easy to use. 💪<br>Build simple and lean UIs or more sophisticated ones. Bulma provides the basic building blocks for various use cases.<br><br>Give it a try! 😉<br><br><a href=\"https://t.co/9ewpjDuhrK\">bulma.io</a> - <a href=\"https://twitter.com/jgthms\">@jgthms</a> <br><br><a href=\"https://twitter.com/search?q=%23UX\">#UX</a> <a href=\"https://twitter.com/search?q=%23UI\">#UI</a> <a href=\"https://twitter.com/search?q=%23CSS\">#CSS</a>", "fullname": "Nightly Builders ✨", "username": "nightlybuiIders", "avatar": "https://pbs.twimg.com/profile_images/1038178244514729984/orQ9yicZ_normal.jpg", "hearts": 2, "retweets": 0}, "1220654109466005504": {"id": "1220654109466005504", "date": "10:26 AM - 24 Jan 2020", "content": "Bulma Css Framework is so easy, but so good. <a href=\"https://twitter.com/jgthms\">@jgthms</a> <a href=\"https://t.co/5nUHBNbfqj\">bulma.io</a>", "fullname": "<PERSON><PERSON>", "username": "A1S0N_", "avatar": "https://pbs.twimg.com/profile_images/1245232340512706567/HLssI-YP_normal.jpg", "hearts": 1, "retweets": 0}, "1230105281079570433": {"id": "1230105281079570433", "date": "1:22 PM - 19 Feb 2020", "content": "Easy to learn and awesome CSS framework <a href=\"https://t.co/BWzffdVRuN\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1428022980756475908/XCFI_vze_normal.jpg", "hearts": 1, "retweets": 0}, "1240347595106549762": {"id": "1240347595106549762", "date": "6:41 PM - 18 Mar 2020", "content": "We're very happy that <a href=\"https://twitter.com/jgthms\">@jgthms</a> created the Bulma CSS framework. It has saved us a ton of time and made our site responsive on all devices! <a href=\"https://t.co/lvEM7uhcLn\">pic.twitter.com/lvEM7uhcLn</a>", "fullname": "Infinity Search", "username": "InfinitySearch1", "avatar": "https://pbs.twimg.com/profile_images/1312949665818710017/aYXWMxwl_normal.jpg", "hearts": 10, "retweets": 1}, "1254050448711057410": {"id": "1254050448711057410", "date": "4:11 PM - 25 Apr 2020", "content": "One of the main contributors to our success at <a href=\"https://t.co/VjDhdf1Y6u\">grox.io</a>: the Bulma framework by <a href=\"https://twitter.com/jgthms\">@jgthms</a>. <br><br>His work is brilliant. It’s very low JavaScript, and it plays very well with <a href=\"https://twitter.com/elixirphoenix\">@elixirphoenix</a> <a href=\"https://twitter.com/search?q=%23liveview\">#liveview</a>. <br><br>In short, Bulma provides its value and gets out of the way.  Thanks!", "fullname": "<PERSON> (sending love to 🇺🇦)", "username": "redrapids", "avatar": "https://pbs.twimg.com/profile_images/1285717573364076545/PGxJY4Lc_normal.jpg", "hearts": 19, "retweets": 3}, "1254507503523713024": {"id": "1254507503523713024", "date": "9:27 PM - 26 Apr 2020", "content": "Loving <PERSON><PERSON><PERSON> by <a href=\"https://twitter.com/jgthms\">@jgthms</a> it's a great CSS framework", "fullname": "<PERSON> 🐢 #GNUTerryP", "username": "<PERSON><PERSON><PERSON><PERSON>_", "avatar": "https://pbs.twimg.com/profile_images/1446362581468332032/oms4oJcI_normal.jpg", "hearts": 2, "retweets": 0}, "1263809734618087426": {"id": "1263809734618087426", "date": "2:31 PM - 22 May 2020", "content": "Some SNEAK peaks of coming Update.. v3 of <a href=\"https://twitter.com/search?q=%23aapsspace\">#aapsspace</a> <br>Finally design feels fit in box.. thanks <a href=\"https://twitter.com/jgthms\">@jgthms</a> <br><a href=\"https://twitter.com/search?q=%23astrology\">#astrology</a> <a href=\"https://twitter.com/search?q=%23madewithbulma\">#madewithbulma</a> <a href=\"https://t.co/SL0ALNnheS\">pic.twitter.com/SL0ALNnheS</a>", "fullname": "<PERSON><PERSON><PERSON>", "username": "NkNachiket", "avatar": "https://pbs.twimg.com/profile_images/1019507053272891393/VoYZp2ds_normal.jpg", "hearts": 3, "retweets": 0}, "1265483236433694720": {"id": "1265483236433694720", "date": "5:21 AM - 27 May 2020", "content": "<a href=\"https://twitter.com/rawkode\">@rawkode</a> I found bulma from <a href=\"https://twitter.com/jgthms\">@jgthms</a> very useful", "fullname": "<PERSON><PERSON>", "username": "rajatjindal1983", "avatar": "https://pbs.twimg.com/profile_images/1254854393062551552/8ABH6N9i_normal.jpg", "hearts": 1, "retweets": 0}, "1268317768182575104": {"id": "1268317768182575104", "date": "12:04 AM - 4 Jun 2020", "content": "Fell in love with <PERSON><PERSON><PERSON> by <a href=\"https://twitter.com/jgthms\">@jgthms</a>. I love it! The love for it keeps growing! Easily customizable, neat and lightweight <a href=\"https://t.co/AlVhUhiUbE\">bulma.io</a>", "fullname": "{{<PERSON>}}", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1194143511853117440/XffFxRkT_normal.jpg", "hearts": 1, "retweets": 0}, "1270126166884696067": {"id": "1270126166884696067", "date": "12:50 AM - 9 Jun 2020", "content": "My CSS framework of choice. <a href=\"https://t.co/nAquV0Ffrn\">twitter.com/jgthms/status/…</a>", "fullname": "<PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1513426723907510284/y6n_hfGg_normal.jpg", "hearts": 2, "retweets": 0}, "1270342191198674946": {"id": "1270342191198674946", "date": "2:09 PM - 9 Jun 2020", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> I love <PERSON><PERSON><PERSON>. One of the support worthiest projects!", "fullname": "Subscribee | Monthly Revenue from Your Fans", "username": "Subscribee_net", "avatar": "https://pbs.twimg.com/profile_images/1301612381945110531/e850NGMK_normal.jpg", "hearts": 1, "retweets": 0}, "1272657987379830784": {"id": "1272657987379830784", "date": "11:31 PM - 15 Jun 2020", "content": "And <a href=\"https://t.co/KV1qhDacM2\">Bulma.io</a> by <a href=\"https://twitter.com/jgthms\">@jgthms</a> lets you build lovely layouts with simple HTML and CSS. <a href=\"https://t.co/fBefCwkE6m\">pic.twitter.com/fBefCwkE6m</a>", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1501461250215194624/p2FmJkzq_normal.jpg", "hearts": 1, "retweets": 0}, "1273003149956014080": {"id": "1273003149956014080", "date": "10:22 PM - 16 Jun 2020", "content": "Something else I've been cooking up for the Bridgetown v0.15 release…a new starter theme based on Bulma CSS by <a href=\"https://twitter.com/jgthms\">@jgthms</a>!<br><br>Once you install the <a href=\"https://twitter.com/bridgetownrb\">@bridgetownrb</a> CLI, with just one command you'll be able to <a href=\"https://twitter.com/search?q=%23SpinUpBridgetown\">#SpinUpBridgetown</a> + B<PERSON>ma in no time!<br><br>(And yes, live search too!!) <a href=\"https://t.co/YaBFlsJac5\">pic.twitter.com/YaBFlsJac5</a>", "fullname": "🇺🇦 <PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>e", "avatar": "https://pbs.twimg.com/profile_images/1431863008288931841/hsLQJBv4_normal.jpg", "hearts": 5, "retweets": 3}, "1273557899667738633": {"id": "1273557899667738633", "date": "12:07 PM - 18 Jun 2020", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> thanks <PERSON>, appreciate all the work on <PERSON><PERSON><PERSON>", "fullname": "<PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1513426723907510284/y6n_hfGg_normal.jpg", "hearts": 1, "retweets": 0}, "1275183515018158082": {"id": "1275183515018158082", "date": "10:46 PM - 22 Jun 2020", "content": "So... I am helping out a local <a href=\"https://twitter.com/search?q=%23Coventry\">#Coventry</a> <a href=\"https://twitter.com/search?q=%23socent\">#socent</a> with a tiny web system to aid their internal finance and employee time sheets generation. I am using Bulma and going with a minimalistic design. Thanks <a href=\"https://twitter.com/jgthms\">@jgthms</a>!<br><a href=\"https://twitter.com/search?q=%23ThisisCoventry\">#ThisisCoventry</a> <a href=\"https://twitter.com/search?q=%23smallbusiness\">#smallbusiness</a> <a href=\"https://twitter.com/search?q=%23CUSELAUNCH\">#CUSELAUNCH</a> <a href=\"https://twitter.com/search?q=%23warwickshire\">#warwickshire</a> <a href=\"https://twitter.com/search?q=%23codingisfun\">#codingisfun</a> <a href=\"https://t.co/cSED7nKsxp\">pic.twitter.com/cSED7nKsxp</a>", "fullname": "db4you", "username": "db4you2", "avatar": "https://pbs.twimg.com/profile_images/1283183292683038727/wVhp9AQr_normal.jpg", "hearts": 3, "retweets": 0}, "1275880289282203655": {"id": "1275880289282203655", "date": "8:55 PM - 24 Jun 2020", "content": "<a href=\"https://t.co/EqWyGDCXkb\">chandamama.page</a> uses bulma for styling because it is js-free and makes it easy to customize via sass 😍  Thank you <a href=\"https://twitter.com/jgthms\">@jgthms</a>! <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> <a href=\"https://twitter.com/search?q=%23madewithbulma\">#madewithbulma</a> <a href=\"https://twitter.com/search?q=%23slickui\">#slickui</a>", "fullname": "Chandamama Page", "username": "ChandamamaPage", "avatar": "https://pbs.twimg.com/profile_images/1275877695541456905/3bfCjynD_normal.jpg", "hearts": 2, "retweets": 0}, "1276522063184965632": {"id": "1276522063184965632", "date": "3:25 PM - 26 Jun 2020", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> thanks for your amazing CSS framework. <br><br>We have used it in our first project that we just launched on the ProductHunt - <a href=\"https://t.co/fprse3CtVG\">producthunt.com/posts/okrstudio</a>", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1235493398091571201/Ei6Q55l__normal.jpg", "hearts": 2, "retweets": 0}, "1276802746377662464": {"id": "1276802746377662464", "date": "11:01 AM - 27 Jun 2020", "content": "Another delightful find was the Bulma CSS framework, from <a href=\"https://twitter.com/jgthms\">@jgthms</a>. I can definitely recommend that as an alternative to Bootstrap.", "fullname": "<PERSON><PERSON> ✨", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1507445598538063878/8D7h-GN0_normal.jpg", "hearts": 4, "retweets": 0}, "1277584206084935685": {"id": "1277584206084935685", "date": "1:46 PM - 29 Jun 2020", "content": "<a href=\"https://twitter.com/search?q=%23right_to_left\">#right_to_left</a> RTL ... great job <a href=\"https://t.co/AafgVR7tsJ\">twitter.com/jgthms/status/…</a>", "fullname": "Ping0On", "username": "pingonnmod", "avatar": "https://pbs.twimg.com/profile_images/1141100502568116224/_sEOchaM_normal.jpg", "hearts": 3, "retweets": 0}, "1281269805312610304": {"id": "1281269805312610304", "date": "5:51 PM - 9 Jul 2020", "content": "I can't believe I am only just discovering Bulma CSS now. This is a great alternative to Bootstrap, I will be using Bulma a lot more going forward 😊 <a href=\"https://twitter.com/search?q=%23100DaysOfCode\">#100DaysOfCode</a><br><br>Bulma: Free, open source, and modern CSS framework based on Flexbox <a href=\"https://t.co/PdcThhkoZc\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "MikeOsa123", "avatar": "https://pbs.twimg.com/profile_images/1438482393120264200/7FEeomUk_normal.jpg", "hearts": 4, "retweets": 0}, "1284148855613927424": {"id": "1284148855613927424", "date": "4:32 PM - 17 Jul 2020", "content": "Built my first little project using <a href=\"https://twitter.com/jgthms\">@jgthms</a>'s <PERSON><PERSON><PERSON>. Love it makes things super simple I will using this for my projects going forward rather than bootstrap.", "fullname": "<PERSON>", "username": "j1mu5", "avatar": "https://pbs.twimg.com/profile_images/1356991465054961665/P-umIMSh_normal.jpg", "hearts": 1, "retweets": 0}, "1284485587735662592": {"id": "1284485587735662592", "date": "2:50 PM - 18 Jul 2020", "content": "Today I'm porting a little personal project to use <a href=\"https://t.co/rj4698T97q\">bulma.io</a> <a href=\"https://twitter.com/jgthms\">@jgthms</a> in an attempt to move away from bootstrap. So far so good - great docs! <a href=\"https://twitter.com/search?q=%23100DaysOfCode\">#100DaysOfCode</a>", "fullname": "<PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1274736024388845568/OyKJIYs6_normal.jpg", "hearts": 2, "retweets": 3}, "1284499278501732352": {"id": "1284499278501732352", "date": "3:44 PM - 18 Jul 2020", "content": "After using <PERSON><PERSON>ma for a few days, I think it is a framework I actually quite enjoy using. Still need some tugging with, but works mostly fine! <a href=\"https://t.co/8dx2IGBt6j\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "ThaKladd", "avatar": "https://pbs.twimg.com/profile_images/227032741/avatarakam_normal.jpg", "hearts": 1, "retweets": 0}, "1284567124967661568": {"id": "1284567124967661568", "date": "8:14 PM - 18 Jul 2020", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> I'd like to personally thank you for the frankly amazing work you have created at Bulma! It's saved me countless hours and is something I know I can rely on when it comes to cross-platform compatibility!", "fullname": "<PERSON>", "username": "thejackgledhill", "avatar": "https://pbs.twimg.com/profile_images/1347272944725225479/qzYvCKXC_normal.jpg", "hearts": 1, "retweets": 0}, "1285282802414637056": {"id": "1285282802414637056", "date": "8:37 PM - 20 Jul 2020", "content": "I love <a href=\"https://t.co/WZF5VDU9lf\">bulma.io</a>! <br>I am not a Web developer but is building a new website using gatsby and azure static Web. Struggling with responsiveness and layout. Until I found <a href=\"https://twitter.com/search?q=%23Bulma\">#Bulma</a>! <br><a href=\"https://twitter.com/jgthms\">@jgthms</a> you are awesome!", "fullname": "<PERSON>", "username": "sand<PERSON><PERSON>d", "avatar": "https://pbs.twimg.com/profile_images/888377452929708032/NA2xv0-e_normal.jpg", "hearts": 1, "retweets": 0}, "1285345859803033602": {"id": "1285345859803033602", "date": "11:48 PM - 20 Jul 2020", "content": "I made beautiful webpages without my own custom CSS!! What a wonderful! I just added some Bulma classes. Thank you Bul<PERSON>. It is the greatest CSS framework!<br><br>Bulma: Free, open source, and modern CSS framework based on Flexbox <a href=\"https://t.co/7VsZdiioRL\">bulma.io</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "あああ", "username": "mmmmmmrnmmm", "avatar": "https://pbs.twimg.com/profile_images/1368387827747065856/UnbI3DRD_normal.jpg", "hearts": 1, "retweets": 0}, "1285771458715586561": {"id": "1285771458715586561", "date": "3:59 AM - 22 Jul 2020", "content": "Everytime I use bulma I can’t help but feel an immense sense of gratitude to <a href=\"https://twitter.com/jgthms\">@jgthms</a> thank you 🙏🏽", "fullname": "<PERSON><PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON>we<PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1305252804194258945/3rBK4bx__normal.jpg", "hearts": 2, "retweets": 0}, "1285916056989560832": {"id": "1285916056989560832", "date": "1:34 PM - 22 Jul 2020", "content": "<a href=\"https://twitter.com/xteam\">@xteam</a> <PERSON><PERSON><PERSON> by <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON><PERSON><PERSON><PERSON>", "username": "VidyasagarMSC", "avatar": "https://pbs.twimg.com/profile_images/1479985054449291272/zg50lFjZ_normal.jpg", "hearts": 4, "retweets": 0}, "1285919746001711106": {"id": "1285919746001711106", "date": "1:48 PM - 22 Jul 2020", "content": "<a href=\"https://twitter.com/xteam\">@xteam</a> I 💚 Bulma by <a href=\"https://twitter.com/jgthms\">@jgthms</a> <br><br>You read it from an avocado 🥑", "fullname": "💻El Aguacate Programador🥑", "username": "ch1nux", "avatar": "https://pbs.twimg.com/profile_images/1491953029871984650/_AeOKeRK_normal.jpg", "hearts": 7, "retweets": 0}, "1295224277088649216": {"id": "1295224277088649216", "date": "6:01 AM - 17 Aug 2020", "content": "<a href=\"https://twitter.com/search?q=%23100DaysOfCode\">#100DaysOfCode</a> [ 7 ]<br>Thank you <a href=\"https://twitter.com/jgthms\">@jgthms</a> for CSS framework Bulma! It's straightforward. I can focus on priorities without getting bogged down with ux/ui. I was going to implement authentication in <a href=\"https://twitter.com/search?q=%23flask\">#flask</a> but I accidentally dozed off before finishing. =\\ So tire. SLEEP!!! <a href=\"https://twitter.com/search?q=%23python\">#python</a>", "fullname": "<PERSON>", "username": "andydangc", "avatar": "https://pbs.twimg.com/profile_images/1428166804208119817/bsXAcLSC_normal.jpg", "hearts": 3, "retweets": 8}, "1296058474216878080": {"id": "1296058474216878080", "date": "1:16 PM - 19 Aug 2020", "content": "<a href=\"https://twitter.com/search?q=%23madewithbulma\">#madewithbulma</a>. Bulma is just a perfect css framework. I used to bulma when I need simple and fast script load.<br><br>This site is version 3 since using bulma. Made with:<br>- <PERSON><PERSON><PERSON><br>- Buefy<br>- <PERSON><PERSON><PERSON> Js<br><br>Thanks <a href=\"https://twitter.com/jgthms\">@jgthms</a> <br><a href=\"https://t.co/mZldhMj9Sj\">imaka.or.id</a> <a href=\"https://t.co/9XNQJD4Lii\">pic.twitter.com/9XNQJD4Lii</a>", "fullname": "badut bercula satu", "username": "tehbot<PERSON>_sisri", "avatar": "https://pbs.twimg.com/profile_images/1448616235688599553/ToYWmz_g_normal.jpg", "hearts": 4, "retweets": 0}, "1304620948570734598": {"id": "1304620948570734598", "date": "4:20 AM - 12 Sep 2020", "content": "Tô precisando fazer um projetinho aqui e já tava desejando me matar por ter que usar bootstrap. Tava até considerando usar Vue mas é extremamente desnecessário pra algo que não tem tanto foco na interface. Aí eu conheci <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a>. Tks <a href=\"https://twitter.com/jgthms\">@jgthms</a> ❤️", "fullname": "appoks", "username": "appoks", "avatar": "https://pbs.twimg.com/profile_images/1266807852687032321/CkMHk1dl_normal.jpg", "hearts": 2, "retweets": 0}, "1306498446602379265": {"id": "1306498446602379265", "date": "8:41 AM - 17 Sep 2020", "content": "When web site comes with responsive, I would say <a href=\"https://twitter.com/search?q=%23bulma\">#bulma</a> thanks <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "redder", "username": "MANICKA70857671", "avatar": "https://pbs.twimg.com/profile_images/1289843965680746496/5CPkAoff_normal.jpg", "hearts": 3, "retweets": 0}, "1310481068886118400": {"id": "1310481068886118400", "date": "9:26 AM - 28 Sep 2020", "content": "thanks <PERSON><PERSON><PERSON> for saving my day.<br><a href=\"https://twitter.com/jgthms\">@jgthms</a> <br><br>when-a-developer-is-asked-to-design-a-website <a href=\"https://t.co/i44O1nDMNd\">pic.twitter.com/i44O1nDMNd</a>", "fullname": "ninan kara", "username": "<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/482392901314301952/3d6-xZT4_normal.png", "hearts": 19, "retweets": 2}, "1312956419117854720": {"id": "1312956419117854720", "date": "4:22 AM - 5 Oct 2020", "content": "<a href=\"https://twitter.com/ninankara\">@ninankara</a> <a href=\"https://twitter.com/jgthms\">@jgthms</a> This is me right now. <PERSON><PERSON><PERSON> is working well for me. So nice to have a design framework that isn't bloated with JS", "fullname": "Trev", "username": "realTrevDev", "avatar": "https://pbs.twimg.com/profile_images/1465103918690553858/rwBcOp3Q_normal.jpg", "hearts": 2, "retweets": 0}, "1313236270617972740": {"id": "1313236270617972740", "date": "10:55 PM - 5 Oct 2020", "content": "Rebuilt our entire frontend in Bulma! Loved every minute of it. Our dashboard is next 😁. <a href=\"https://twitter.com/jgthms\">@jgthms</a> <br><br><a href=\"https://twitter.com/search?q=%23javascript\">#javascript</a> <a href=\"https://twitter.com/search?q=%23CSS\">#CSS</a> <a href=\"https://twitter.com/search?q=%23Bulma\">#Bulma</a> <a href=\"https://twitter.com/search?q=%23100DaysOfCode\">#100DaysOfCode</a> <a href=\"https://twitter.com/search?q=%23DEVCommunity\">#DEVCommunity</a> <a href=\"https://twitter.com/search?q=%23html\">#html</a>", "fullname": "KwesForms", "username": "kwesforms", "avatar": "https://pbs.twimg.com/profile_images/1307919969066385408/EYnTRqs8_normal.jpg", "hearts": 7, "retweets": 8}, "1314920369514573824": {"id": "1314920369514573824", "date": "2:27 PM - 10 Oct 2020", "content": "ThanksTober <a href=\"https://twitter.com/jgthms\">@jgthms</a>  <PERSON><PERSON><PERSON> is amazing", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1348657778391461889/XaP6IbkY_normal.jpg", "hearts": 1, "retweets": 0}, "1315342004084244482": {"id": "1315342004084244482", "date": "6:22 PM - 11 Oct 2020", "content": "We created our (dark themed) website with this amazing <a href=\"https://twitter.com/search?q=%23bulma\">#bulma</a> framework by <a href=\"https://twitter.com/jgthms\">@jgthms</a>. We used <a href=\"https://twitter.com/search?q=%23VueJS\">#VueJS</a>  in combination with <a href=\"https://twitter.com/search?q=%23buefy\">#buefy</a> from <a href=\"https://twitter.com/walter_tommasi\">@walter_tommasi</a>, which is a match made in heaven! Super happy how all these tools work together and speed up development time a lot! Thanks a lot! <a href=\"https://t.co/2RoY70NmNt\">pic.twitter.com/2RoY70NmNt</a>", "fullname": "CryptoSocialRank", "username": "CryptoSocialRnk", "avatar": "https://pbs.twimg.com/profile_images/1315342846736633856/G_vfnICD_normal.jpg", "hearts": 1, "retweets": 0}, "1316421668483600385": {"id": "1316421668483600385", "date": "5:52 PM - 14 Oct 2020", "content": "Spent some time with <a href=\"https://twitter.com/search?q=%23Bulma\">#Bulma</a> this afternoon and I must say I kind of like their echo-system compared to major rivals. Thanks for the great lightwork <a href=\"https://twitter.com/search?q=%23CSS\">#CSS</a> framework <br><a href=\"https://twitter.com/search?q=%23CSS3\">#CSS3</a> <a href=\"https://twitter.com/search?q=%23SCSS\">#SCSS</a> <a href=\"https://twitter.com/search?q=%23framework\">#framework</a><br><a href=\"https://t.co/nai2TdhNAO\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON><PERSON> (AJ)", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1369340039826661383/gsJhUMVY_normal.jpg", "hearts": 4, "retweets": 1}, "1321261574330314754": {"id": "1321261574330314754", "date": "1:24 AM - 28 Oct 2020", "content": "I LOVE Bulma. Been using it to prototype our interfaces and it is simply awesome 😍<a href=\"https://t.co/xPmn5w0fqq\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "dotkoh5", "avatar": "https://pbs.twimg.com/profile_images/1435979427607764996/A2Z9Butw_normal.jpg", "hearts": 1, "retweets": 0}, "1322651648712347648": {"id": "1322651648712347648", "date": "9:28 PM - 31 Oct 2020", "content": "🤘Development tools today are bananas. <a href=\"https://twitter.com/meteorjs\">@meteorjs</a> +  <a href=\"https://twitter.com/sveltejs\">@sveltejs</a> + <a href=\"https://twitter.com/MongoDB\">@MongoDB</a>  + <a href=\"https://twitter.com/jgthms\">@jgthms</a>'s <PERSON><PERSON><PERSON> are crazy productive together.", "fullname": "<PERSON>", "username": "ppedrazzi", "avatar": "https://pbs.twimg.com/profile_images/1240030225791995905/uA2Zpre7_normal.png", "hearts": 2, "retweets": 0}, "1323215333197516808": {"id": "1323215333197516808", "date": "10:48 AM - 2 Nov 2020", "content": "For me, these are the following projects I use on a daily basis.<br><br>Distributions<br>- <a href=\"https://twitter.com/SolusProject\">@SolusProject</a><br>- <a href=\"https://twitter.com/ManjaroLinux\">@ManjaroLinux</a> <br>- <a href=\"https://twitter.com/search?q=%23ArchLinuxARM\">#ArchLinuxARM</a><br><br>Software<br>- vscode<br>- <a href=\"https://twitter.com/GIMP_Official\">@GIMP_Official</a> <br>- Git<br>- bulma (<a href=\"https://twitter.com/jgthms\">@jgthms</a>)<br>- <a href=\"https://twitter.com/firefox\">@firefox</a>", "fullname": "Free Open-Source Software Torrents", "username": "FossTorrents", "avatar": "https://pbs.twimg.com/profile_images/1350135576968781824/BhbSbLvi_normal.png", "hearts": 1, "retweets": 1}, "1324667303263424513": {"id": "1324667303263424513", "date": "10:57 AM - 6 Nov 2020", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> Oh no, forgot to mention <PERSON><PERSON><PERSON>! It's great, imo the semantics are really well though out: classnames are easy to remember and it really helps when you want to build something fast!", "fullname": "<PERSON><PERSON> セミ", "username": "theserey", "avatar": "https://pbs.twimg.com/profile_images/1325802067580116998/bUiJZsDL_normal.png", "hearts": 2, "retweets": 0}, "1325028695879376896": {"id": "1325028695879376896", "date": "10:53 AM - 7 Nov 2020", "content": "Yey ! I've moved <a href=\"https://t.co/B1iHp9uBX9\">musicall.com</a> from Bootstrap to Bulma ! <br>Still some work to do<br><br><a href=\"https://t.co/SzHU8BsM3b\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a>  <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON><PERSON><PERSON><PERSON>", "username": "Cryde_", "avatar": "https://pbs.twimg.com/profile_images/822428903285411840/QYnqT8UE_normal.jpg", "hearts": 3, "retweets": 0}, "1334994971426959360": {"id": "1334994971426959360", "date": "10:56 PM - 4 Dec 2020", "content": "<a href=\"https://twitter.com/codingwithdani\">@codingwithdani</a> <a href=\"https://twitter.com/jgthms\">@jgthms</a> <a href=\"https://twitter.com/tailwindcss\">@tailwindcss</a> <a href=\"https://twitter.com/getuikit\">@getuikit</a> Bulma es buenisimo, los otros no los he probado", "fullname": "El nombre no puede estar en blanco", "username": "true<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1460736938365837327/LBMXJ1vb_normal.jpg", "hearts": 2, "retweets": 0}, "1339242817919590404": {"id": "1339242817919590404", "date": "4:15 PM - 16 Dec 2020", "content": "Omg I've been fighting with bootstrap and foundation, trying to get them to work with my rails app, for days. After 5 minutes I have <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> working. It's a miracle!!!! <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON>", "username": "MyTopSecretName", "avatar": "https://pbs.twimg.com/profile_images/817832652938113024/COudsz8d_normal.jpg", "hearts": 22, "retweets": 1}, "1339962805307518976": {"id": "1339962805307518976", "date": "3:56 PM - 18 Dec 2020", "content": "<a href=\"https://twitter.com/MyTopSecretName\">@MyTopSecretName</a> <a href=\"https://twitter.com/jgthms\">@jgthms</a> Got to say, I was dubious about <PERSON><PERSON><PERSON> at first as it was totally out of my comfort zone, but it's fast becoming my framework of choice 😍", "fullname": "<PERSON><PERSON><PERSON>", "username": "ImKirstyMarks", "avatar": "https://pbs.twimg.com/profile_images/1270055747901153280/1nOGj9in_normal.jpg", "hearts": 2, "retweets": 0}, "1342294345404264449": {"id": "1342294345404264449", "date": "2:21 AM - 25 Dec 2020", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> Thanks for <a href=\"https://t.co/kdobwTJ7kQ\">bulma.io</a>.<br>You are a Hero!", "fullname": "SF", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1057803543049048064/Klk_Jw1s_normal.jpg", "hearts": 1, "retweets": 0}, "1342581917774962690": {"id": "1342581917774962690", "date": "9:24 PM - 25 Dec 2020", "content": "<a href=\"https://twitter.com/catalinmpit\">@catalinmpit</a> I love <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1357511880/163239_1717349087619_1055000918_1858588_1100995_n_normal.jpg", "hearts": 2, "retweets": 0}, "1344604032661409792": {"id": "1344604032661409792", "date": "11:19 AM - 31 Dec 2020", "content": "Almost forgot! For a CSS framework, I'm using <a href=\"https://twitter.com/jgthms\">@jgthms</a> <PERSON><PERSON><PERSON>. <br><br>I know Tailwind is very popular right now, but Bulma introduced utility classes for margins and paddings in version 0.7.1 -- Those were the utility classes that was looking for, so now all my CSS needs are covered 🙂", "fullname": "<PERSON><PERSON><PERSON>", "username": "tuo<PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1067348906890145792/esdf7ffA_normal.jpg", "hearts": 2, "retweets": 0}, "1345754590508027905": {"id": "1345754590508027905", "date": "3:31 PM - 3 Jan 2021", "content": "New portfolio: <a href=\"https://t.co/bpvzyhrIkp\">hyvdn.com</a><br>Thanks: <br>1. <a href=\"https://twitter.com/jgthms\">@jgthms</a> for the hassle free, beautiful <a href=\"https://t.co/va3k95LXgQ\">bulma.io</a> <a href=\"https://twitter.com/search?q=%23css\">#css</a> framework. <br>2. <a href=\"https://twitter.com/k00\">@k00</a> for inspiring me with his blogpost about minimum viable portfolio on <a href=\"https://twitter.com/intercom\">@intercom</a> blog.<br>Feedback/Review most welcome. <a href=\"https://twitter.com/tannerc\">@tannerc</a><br> <a href=\"https://twitter.com/hemeon\">@hemeon</a>", "fullname": "பரிமுகன்", "username": "<PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1488530253433966592/lDxLkb-S_normal.png", "hearts": 1, "retweets": 0}, "1347589796194037764": {"id": "1347589796194037764", "date": "5:03 PM - 8 Jan 2021", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> <a href=\"https://twitter.com/signalapp\">@signalapp</a> Love <a href=\"https://twitter.com/search?q=%23Bulma\">#Bulma</a>", "fullname": "Shuaib⚡", "username": "s<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1502424418458685440/NsTcX_Up_normal.png", "hearts": 1, "retweets": 0}, "1347593829692526592": {"id": "1347593829692526592", "date": "5:19 PM - 8 Jan 2021", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> <a href=\"https://twitter.com/signalapp\">@signalapp</a> <PERSON><PERSON><PERSON>’s my favorite css framework. Building out my portfolio with it right now 😁", "fullname": "<PERSON><PERSON>", "username": "j<PERSON><PERSON><PERSON>_", "avatar": "https://pbs.twimg.com/profile_images/1299770143921172480/nYpkyPUB_normal.jpg", "hearts": 1, "retweets": 0}, "1349398146154176520": {"id": "1349398146154176520", "date": "4:49 PM - 13 Jan 2021", "content": "I have lately discovered <PERSON><PERSON><PERSON> and I have fell in love in it!<br><br>Try it out! <a href=\"https://t.co/4mNHdBNhFs\">bulma.io</a><br><a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "Mir<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1445269447732772864/PMT326aH_normal.jpg", "hearts": 3, "retweets": 0}, "1350229753287081991": {"id": "1350229753287081991", "date": "11:53 PM - 15 Jan 2021", "content": "Hey this is pretty cool. <PERSON><PERSON><PERSON> looks awesome. <a href=\"https://t.co/5hel2mRozG\">twitter.com/jgthms/status/…</a>", "fullname": "drew♦️", "username": "Andevrs", "avatar": "https://pbs.twimg.com/profile_images/1441701087606546433/cFRAv7Op_normal.jpg", "hearts": 2, "retweets": 0}, "1350347522091479041": {"id": "1350347522091479041", "date": "7:41 AM - 16 Jan 2021", "content": "Day 68: Worked on <a href=\"https://twitter.com/sig_five\">@sig_five</a>. Added <a href=\"https://twitter.com/jgthms\">@jgthms</a>' B<PERSON>ma to the app. Works great, didn't have to fight with it to get it to work. Very easy to use. Proceeded to build out a navbar and front page. Going to use this on my personal site. <a href=\"https://twitter.com/search?q=%23100DaysOfCode\">#100DaysOfCode</a> <a href=\"https://twitter.com/search?q=%23rubyonrails\">#rubyonrails</a> <a href=\"https://twitter.com/search?q=%23buildinpublic\">#buildinpublic</a>", "fullname": "drew♦️", "username": "Andevrs", "avatar": "https://pbs.twimg.com/profile_images/1441701087606546433/cFRAv7Op_normal.jpg", "hearts": 6, "retweets": 5}, "1353035507253571584": {"id": "1353035507253571584", "date": "5:42 PM - 23 Jan 2021", "content": "Can we say goodbye to <a href=\"https://twitter.com/getbootstrap\">@getbootstrap</a>? I'm happy to use Bulma <a href=\"https://t.co/v76XbEHaPr\">bulma.io</a> <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "slo74design", "username": "slo74design", "avatar": "https://pbs.twimg.com/profile_images/1352637700159975424/xLDLIl62_normal.jpg", "hearts": 2, "retweets": 0}, "1353076927423123458": {"id": "1353076927423123458", "date": "8:27 PM - 23 Jan 2021", "content": "Built this using <a href=\"https://twitter.com/djangoproject\">@djangoproject</a> &amp; <a href=\"https://twitter.com/search?q=%23bulma\">#bulma</a> by <a href=\"https://twitter.com/jgthms\">@jgthms</a> pulling prices from ebay<br><br><a href=\"https://twitter.com/search?q=%23python\">#python</a> <a href=\"https://twitter.com/search?q=%23CSS\">#CSS</a>", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1510801423319117833/nt2AZkGj_normal.jpg", "hearts": 2, "retweets": 0}, "1353436029986992130": {"id": "1353436029986992130", "date": "8:14 PM - 24 Jan 2021", "content": "The new website is reactive: try it out on your mobile or resize your window.<br><br>Easier navigation with a local table of content (“on this page”) and toggle button for the main menu.<br><br>We used <a href=\"https://twitter.com/jgthms\">@jgthms</a>’s amazing Bulma framework with SASS, for a nicer, slicker look!", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/797225476624416772/Gw3PV1qc_normal.jpg", "hearts": 2, "retweets": 0}, "1353619424075370499": {"id": "1353619424075370499", "date": "8:23 AM - 25 Jan 2021", "content": "Made the best decision of moving from bootstrap to <a href=\"https://t.co/srO7y12mgV\">Bulma.io</a> and I'm loving it. <br> Thanks <a href=\"https://twitter.com/jgthms\">@jgthms</a> . Bul<PERSON> is the BEST!", "fullname": "<PERSON><PERSON>", "username": "hawk_ahsan", "avatar": "https://pbs.twimg.com/profile_images/1496724075682484226/In6-vTsC_normal.jpg", "hearts": 5, "retweets": 0}, "1355383014981840899": {"id": "1355383014981840899", "date": "5:11 AM - 30 Jan 2021", "content": "Back to Zeer0! Also, thank you <a href=\"https://twitter.com/walter_tommasi\">@walter_tommasi</a> for Buefy which combines <a href=\"https://twitter.com/jgthms\">@jgthms</a>'s <PERSON><PERSON><PERSON> and <a href=\"https://twitter.com/vuejs\">@vuejs</a>. You guys are making front end tech so easy that even I am starting to dream!", "fullname": "<PERSON>", "username": "zeer0hq", "avatar": "https://pbs.twimg.com/profile_images/1365460855979085826/H8l2w6lT_normal.jpg", "hearts": 3, "retweets": 0}, "1356178993045770241": {"id": "1356178993045770241", "date": "9:54 AM - 1 Feb 2021", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> Like the new website :)", "fullname": "<PERSON><PERSON>", "username": "Calumk", "avatar": "https://pbs.twimg.com/profile_images/1085267333671763968/UccYAzZJ_normal.jpg", "hearts": 2, "retweets": 0}, "1356889558265257985": {"id": "1356889558265257985", "date": "8:57 AM - 3 Feb 2021", "content": "Desde hace años he usado el framework <a href=\"https://twitter.com/search?q=%23Bootstrap\">#Bootstrap</a> para desarrollar proyectos simples por falta de tiempo. Y desde hace meses me animé a explorar otros frameworks más ligeros. Hoy empiezo a 'trastear' <a href=\"https://twitter.com/search?q=%23Bulma\">#Bulma</a> 👩‍💻<br><br>Bulma es Open Source, y está basado en Flexbox - @j<a href=\"https://twitter.com/jgthms\">@jgthms</a>#C<a href=\"https://twitter.com/search?q=%23CSS\">#CSS</a>h<a href=\"https://twitter.com/search?q=%23html5\">#html5</a>t<a href=\"https://t.co/1Da9Tu1GgV\">pic.twitter.com/1Da9Tu1GgV</a>", "fullname": "<PERSON><PERSON><PERSON>", "username": "RemediosFdez", "avatar": "https://pbs.twimg.com/profile_images/1432757214373109766/t3LMLu2M_normal.jpg", "hearts": 9, "retweets": 1}, "1364593055081226243": {"id": "1364593055081226243", "date": "4:08 PM - 24 Feb 2021", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> some our intranet pages use <PERSON><PERSON><PERSON>, cannot post a screenshot but I have to say the framework is amazing, definitely replaced/will replace bootstrap in some of our projects!", "fullname": "dvmNERV", "username": "_UN_NERV_", "avatar": "https://pbs.twimg.com/profile_images/1373509276031807497/o2vRSdaS_normal.jpg", "hearts": 1, "retweets": 0}, "1365014446997975042": {"id": "1365014446997975042", "date": "8:02 PM - 25 Feb 2021", "content": "C'est quand même plus beau 👌ça donne davantage envie de se connecter à <a href=\"https://t.co/1qq6mEXM2v\">lesnewsletters.com</a> 🥰 Merci <a href=\"https://twitter.com/jgthms\">@jgthms</a> pour le framework Bulma 🙏<br><br><a href=\"https://twitter.com/search?q=%23lesnewsletters\">#lesnewsletters</a> <a href=\"https://twitter.com/search?q=%23django\">#django</a> <a href=\"https://twitter.com/search?q=%23codeenpublic\">#codeenpublic</a> <a href=\"https://t.co/9eEDxsvYef\">pic.twitter.com/9eEDxsvYef</a>", "fullname": "<PERSON><PERSON><PERSON><PERSON>", "username": "carl_chenet", "avatar": "https://pbs.twimg.com/profile_images/1365073582473248770/_HPOZLD7_normal.jpg", "hearts": 3, "retweets": 0}, "1365045003354046469": {"id": "1365045003354046469", "date": "10:04 PM - 25 Feb 2021", "content": "Well, after some consideration I decided to \"ditch\" Tailwind and move forward with <a href=\"https://t.co/dLdnVqqxXm\">Bulma.io</a> from <a href=\"https://twitter.com/jgthms\">@jgthms</a>. It's closer to what I expect from a framework and the results speak for themselves <a href=\"https://twitter.com/search?q=%23100DaysOfCode\">#100DaysOfCode</a>  <a href=\"https://twitter.com/search?q=%23frontenddev\">#frontenddev</a> <a href=\"https://t.co/7BFi8VX5N1\">pic.twitter.com/7BFi8VX5N1</a>", "fullname": "<PERSON>", "username": "jorge_codes", "avatar": "https://pbs.twimg.com/profile_images/1392266410232004610/IKUQZzSQ_normal.jpg", "hearts": 12, "retweets": 10}, "1365312388099149830": {"id": "1365312388099149830", "date": "3:46 PM - 26 Feb 2021", "content": "<a href=\"https://twitter.com/jorge_codes\">@jorge_codes</a> <a href=\"https://twitter.com/jgthms\">@jgthms</a> <PERSON><PERSON><PERSON> is an excellent framework straight from the CSS Gods, good choice! I've had nothing but pleasure using it for the last year.", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1353660492938670082/QCtLZsq2_normal.jpg", "hearts": 3, "retweets": 1}, "1374216837127372801": {"id": "1374216837127372801", "date": "5:30 AM - 23 Mar 2021", "content": "<PERSON><PERSON><PERSON> by <a href=\"https://twitter.com/jgthms\">@jgthms</a> is a really underrated CSS framework for beginners. It is very easy to use and I really enjoy working with it.", "fullname": "<PERSON><PERSON><PERSON> 🥦", "username": "<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1272067807191031808/f_lJ-cSA_normal.jpg", "hearts": 2, "retweets": 0}, "1379802811991347203": {"id": "1379802811991347203", "date": "4:26 PM - 7 Apr 2021", "content": "<a href=\"https://twitter.com/search?q=%23Bulma\">#Bulma</a> is number #1 for me lol, new into CSS framework then found this Lightweight CSS from <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "MexL", "username": "<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1065855391064449024/uPOCbx84_normal.jpg", "hearts": 2, "retweets": 0}, "1380020310150410240": {"id": "1380020310150410240", "date": "6:50 AM - 8 Apr 2021", "content": "I wanted a light <a href=\"https://twitter.com/search?q=%23CSS\">#CSS</a> framework with sane defaults. Didn't want a bloated behemot like <PERSON><PERSON><PERSON>, <PERSON>t or Material. Didn't want the wacko class names of Tailwind.<br><br>Found a good one: <PERSON><PERSON><PERSON>.<br><br><a href=\"https://t.co/iepZL02rwn\">bulma.io</a> <br><br><a href=\"https://twitter.com/search?q=%23Bulmaio\">#Bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a> <br><a href=\"https://twitter.com/search?q=%23100DaysOfCode\">#100DaysOfCode</a> <a href=\"https://twitter.com/search?q=%23DEVCommunity\">#DEVCommunity</a>", "fullname": "<PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1143600497271672833/joL21EIB_normal.jpg", "hearts": 8, "retweets": 5}, "1380022718683357184": {"id": "1380022718683357184", "date": "7:00 AM - 8 Apr 2021", "content": "<a href=\"https://twitter.com/3LPU4S\">@3LPU4S</a> <a href=\"https://twitter.com/jgthms\">@jgthms</a> I'm using Bulma in NextJS and so far it's very fast. I'm controlling everything from Next.js' globals.scss only, like extend a few Bulma classes into a class and then use it.", "fullname": "<PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1143600497271672833/joL21EIB_normal.jpg", "hearts": 2, "retweets": 1}, "1386393448912023552": {"id": "1386393448912023552", "date": "8:55 PM - 25 Apr 2021", "content": "I was looking for software to make my book cheatsheets and checklist. In the end, it was <PERSON><PERSON><PERSON> (<a href=\"https://twitter.com/jgthms\">@jgthms</a>) that saved my ass. <a href=\"https://t.co/kj0r1J7YrU\">pic.twitter.com/kj0r1J7YrU</a>", "fullname": "<PERSON>", "username": "strzibnyj", "avatar": "https://pbs.twimg.com/profile_images/1272847795746152448/-gqd4Vl7_normal.jpg", "hearts": 4, "retweets": 0}, "1387025227880742915": {"id": "1387025227880742915", "date": "2:45 PM - 27 Apr 2021", "content": "I am using Bulma to produce an advanced Markup system for e-learning contents <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a>  <a href=\"https://twitter.com/jgthms\">@jgthms</a> <a href=\"https://t.co/o2KpmeLIYn\">youtube.com/watch?v=LAucHj…</a>", "fullname": "<PERSON>", "username": "netoriestra", "avatar": "https://pbs.twimg.com/profile_images/472750023386464256/WrR7NL0Y_normal.jpeg", "hearts": 2, "retweets": 0}, "1387445105527754754": {"id": "1387445105527754754", "date": "6:34 PM - 28 Apr 2021", "content": "<a href=\"https://twitter.com/NouTimbaler\">@NouTimbaler</a> <PERSON><PERSON><PERSON> (<a href=\"https://twitter.com/jgthms\">@jgthms</a>) &gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt; Bootstrap", "fullname": "Dasix", "username": "<PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1470880483764486149/MRgvZRAi_normal.jpg", "hearts": 2, "retweets": 0}, "1388186889195294724": {"id": "1388186889195294724", "date": "7:42 PM - 30 Apr 2021", "content": "Want to say thanks for the great CSS lib from <a href=\"https://twitter.com/jgthms\">@jgthms</a> <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> has served me well the past years and it's my go-to solution for the web 🎉👍", "fullname": "DevOwl", "username": "RealDevOwl", "avatar": "https://pbs.twimg.com/profile_images/1434434326637187072/HAvzTnPJ_normal.jpg", "hearts": 5, "retweets": 0}, "1390275618898591752": {"id": "1390275618898591752", "date": "2:01 PM - 6 May 2021", "content": "Just bought the official <a href=\"https://twitter.com/search?q=%23bulma\">#bulma</a> book as a way to learn from the creator and give back a little. Really love the framework ❤️ Can't remember the last time something worked so awesomely out of the box. Thanks @<a href=\"https://twitter.com/jgthms\">@jgthms</a>❤️<br><br>Love the name origin too 😏<br>#c<a href=\"https://twitter.com/search?q=%23css\">#css</a>w<a href=\"https://twitter.com/search?q=%23webdevelopment\">#webdevelopment</a>t<a href=\"https://t.co/B5ZCU3Y0JY\">pic.twitter.com/B5ZCU3Y0JY</a>", "fullname": "<PERSON><PERSON>", "username": "s_gbz", "avatar": "https://pbs.twimg.com/profile_images/1484456336918200321/RTKZS0B2_normal.jpg", "hearts": 2, "retweets": 0}, "1391122604208046080": {"id": "1391122604208046080", "date": "10:07 PM - 8 May 2021", "content": "shout out to <a href=\"https://twitter.com/jgthms\">@jgthms</a> and <a href=\"https://twitter.com/bulma\">@bulma</a> for making it look pretty 💅<br><br>and <a href=\"https://twitter.com/tannerlinsley\">@tannerlinsley</a> for the awesome react-table for making the table so easy<br><br>and of course <PERSON><PERSON><PERSON> for compiling the data in the first place 🚀<br><br><a href=\"https://t.co/aHtygHaui1\">gaelleconstantcomedian.com</a>", "fullname": "apuchitnis.eth", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1268484531495018496/O1IJSUjl_normal.jpg", "hearts": 2, "retweets": 0}, "1391535044049653763": {"id": "1391535044049653763", "date": "1:26 AM - 10 May 2021", "content": "2.<PERSON><PERSON><PERSON> CSS<br><a href=\"https://twitter.com/jgthms\">@jgthms</a> <br><br>Made by JGThms on <a href=\"https://twitter.com/jgthms\">@jgthms</a>  this is a flexbox-based framework you'd surely fall in love with the framework...", "fullname": "Zack🐍", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1510206158094716934/ScmRtXKv_normal.jpg", "hearts": 2, "retweets": 0}, "1391698488602382336": {"id": "1391698488602382336", "date": "12:15 PM - 10 May 2021", "content": "Just noticed that the wonderful <a href=\"https://twitter.com/radiohead\">@radiohead</a> Public Library's website is designed using <a href=\"https://twitter.com/search?q=%23Bulma\">#Bulma</a> CSS framework (see <a href=\"https://t.co/a9UB8ACQVs\">bulma.io</a>). <br>Did you know that <a href=\"https://twitter.com/jgthms\">@jgthms</a> ?<br><br><a href=\"https://t.co/YVj5At2lmi\">radiohead.com/library</a>", "fullname": "<PERSON>", "username": "tstassin", "avatar": "https://pbs.twimg.com/profile_images/556909465652314114/IQ14BUc4_normal.jpeg", "hearts": 1, "retweets": 0}, "1392723170679943168": {"id": "1392723170679943168", "date": "8:07 AM - 13 May 2021", "content": "Wow <a href=\"https://t.co/K2C1vBh4iw\">bulma.io</a> by <a href=\"https://twitter.com/jgthms\">@jgthms</a> looks like a really, really nice alternative to Bootstrap, and the markup is not terrifying like <PERSON><PERSON><PERSON>. I'll definitely be checking this out for future projects. 👍", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON>er", "avatar": "https://pbs.twimg.com/profile_images/754626289064042496/uDkwDUeZ_normal.jpg", "hearts": 2, "retweets": 1}, "1394824550219587585": {"id": "1394824550219587585", "date": "3:17 AM - 19 May 2021", "content": "Have made a website mockup within a few days using the excellent <a href=\"https://t.co/K2C1vBh4iw\">bulma.io</a> and some simple scripting. Most of it could probably be used in production. Really, really nice work <a href=\"https://twitter.com/jgthms\">@jgthms</a>. 👍", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON>er", "avatar": "https://pbs.twimg.com/profile_images/754626289064042496/uDkwDUeZ_normal.jpg", "hearts": 2, "retweets": 0}, "1396070337117265923": {"id": "1396070337117265923", "date": "1:48 PM - 22 May 2021", "content": "😍Aesthetic customization in <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a>.<br>🍻 <a href=\"https://twitter.com/jgthms\">@jgthms</a> <a href=\"https://twitter.com/search?q=%23Bulma\">#Bulma</a> <a href=\"https://t.co/k4My5L3YwR\">pic.twitter.com/k4My5L3YwR</a>", "fullname": "<PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1500123538753486850/-0xeJWHW_normal.jpg", "hearts": 2, "retweets": 0}, "1396989847546564609": {"id": "1396989847546564609", "date": "2:41 AM - 25 May 2021", "content": "added a close button to the edit field.  next up, adding a checkbox for todo completed.  also  I am giving <a href=\"https://twitter.com/jgthms\">@jgthms</a>, bulma a try.  I like it so far.  the descriptors of each class are named well.  I like that.  <a href=\"https://twitter.com/search?q=%23100DaysOfCode\">#100DaysOfCode</a>", "fullname": "<PERSON> Cates🇺🇸", "username": "_MarkCates", "avatar": "https://pbs.twimg.com/profile_images/1167893208518389760/UqTxYyuk_normal.jpg", "hearts": 3, "retweets": 1}, "1397619766915997716": {"id": "1397619766915997716", "date": "8:24 PM - 26 May 2021", "content": "<a href=\"https://twitter.com/StackOverflow\">@StackOverflow</a> .<a href=\"https://twitter.com/jgthms\">@jgthms</a>’s <a href=\"https://t.co/XPiP8Jv83a\">Bulma.io</a> has been a personal favorite of mine for a few years now.", "fullname": "<PERSON> ➡️ Seeking Developer Work", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1336825734359359488/8JPIpfVq_normal.jpg", "hearts": 1, "retweets": 0}, "1402291415200276483": {"id": "1402291415200276483", "date": "5:48 PM - 8 Jun 2021", "content": "Shoutout to my <a href=\"https://twitter.com/search?q=%23100DaysOfCode\">#100DaysOfCode</a> fellows, <a href=\"https://t.co/VnKoHkO4FM\">bulma.io</a>  by <a href=\"https://twitter.com/jgthms\">@jgthms</a> deserves some more love. <br><br>I went back to it because build steps during development for a certain utility first frameworks are almost unbearable unless you have a super machine.<br><br>10/10 would do it again.", "fullname": "<PERSON>", "username": "codingsafari", "avatar": "https://pbs.twimg.com/profile_images/1096845156177846273/2zO578ou_normal.jpg", "hearts": 1, "retweets": 2}, "1402375562828652544": {"id": "1402375562828652544", "date": "11:22 PM - 8 Jun 2021", "content": "Developing websites with <a href=\"https://twitter.com/jgthms\">@jgthms</a> excellent Bulma is just a pleasure!<br><br><a href=\"https://t.co/9u52b6t8dm\">kopanko.com</a>", "fullname": "<PERSON><PERSON><PERSON>", "username": "pcktm_", "avatar": "https://pbs.twimg.com/profile_images/1229884522289942529/M8WVwztz_normal.jpg", "hearts": 2, "retweets": 0}, "1402618985661698048": {"id": "1402618985661698048", "date": "3:30 PM - 9 Jun 2021", "content": "<a href=\"https://twitter.com/search?q=%23ShoutoutWednesdays\">#ShoutoutWednesdays</a> to show exciting products. This week: <br><br>∙ <a href=\"https://t.co/IwNhg8pR8b\">lofi.cafe</a> by <a href=\"https://twitter.com/linuz90\">@linuz90</a> (live lofi stations) <br><br>∙ <a href=\"https://t.co/1tfczCE5yZ\">bulma.io</a> by <a href=\"https://twitter.com/jgthms\">@jgthms</a> (my fav CSS framework) <br><br>∙ <a href=\"https://t.co/v3YMOuM0cE\">potion.so</a> by <a href=\"https://twitter.com/noahwbragg\">@noahwbragg</a> (make sites with notion)", "fullname": "<PERSON><PERSON><PERSON> 🥦", "username": "<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1272067807191031808/f_lJ-cSA_normal.jpg", "hearts": 5, "retweets": 0}, "1404866267740258306": {"id": "1404866267740258306", "date": "8:19 PM - 15 Jun 2021", "content": "6/ Don't reinvent the wheel!<br><br>PipeSocial is made with: <br>- a framework that let you build super fast <a href=\"https://twitter.com/rails\">@rails</a><br>- a template and a CSS framework (Bulma by <a href=\"https://twitter.com/jgthms\">@jgthms</a>)<br>- libraries to parse Twitter API<br><br>Don't start from scratch, don't care about code best practice.<br><br>BE FAST🚀🚀🚀", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1329753184320053252/vOn-iEc7_normal.jpg", "hearts": 14, "retweets": 0}, "1406666124905762818": {"id": "1406666124905762818", "date": "7:31 PM - 20 Jun 2021", "content": "Bulma makes building websites more efficient and so beautiful. Redesigned <a href=\"https://t.co/gn0ZTPV72S\">heksagon.net</a> with <PERSON>ulma. Thanks <br><a href=\"https://twitter.com/jgthms\">@jgthms</a><br> !<br><br>Bulma: Free, open source, and modern CSS framework based on Flexbox <a href=\"https://t.co/phOZApR2SF\">bulma.io</a>  <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <br><a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON><PERSON><PERSON>", "username": "Heksagonnet", "avatar": "https://pbs.twimg.com/profile_images/1469847302822981634/fdbbOtst_normal.jpg", "hearts": 4, "retweets": 0}, "1406972663034228741": {"id": "1406972663034228741", "date": "3:49 PM - 21 Jun 2021", "content": "Redesigned the website for my browser extension, <a href=\"https://twitter.com/search?q=%23RenewedTab\">#RenewedTab</a>. It's my first time customising a CSS framework (<a href=\"https://twitter.com/search?q=%23bulmacss\">#bulmacss</a> by <a href=\"https://twitter.com/jgthms\">@jgthms</a>), was less painful than I thought it would be<br><br><a href=\"https://t.co/aoeQ2RIj27\">renewedtab.com</a> <a href=\"https://t.co/E6986SyQqK\">pic.twitter.com/E6986SyQqK</a>", "fullname": "<PERSON><PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1268291650591379458/J6m3aWZ7_normal.jpg", "hearts": 9, "retweets": 1}, "1408337294017191936": {"id": "1408337294017191936", "date": "10:12 AM - 25 Jun 2021", "content": "Our new website is built with Bulma. Frankly, we cannot think of building <a href=\"https://twitter.com/mailboxfiler\">@mailboxfiler</a> without <PERSON><PERSON><PERSON>. It's just awesome. Thank you <a href=\"https://twitter.com/jgthms\">@jgthms</a> <br><br>PS. Don't try spacing helpers; it's addictive 😉<br><br> <a href=\"https://twitter.com/search?q=%23madewithbulma\">#madewithbulma</a> <a href=\"https://twitter.com/search?q=%23Bulma\">#Bulma</a>", "fullname": "Mailboxfiler", "username": "mailboxfiler", "avatar": "https://pbs.twimg.com/profile_images/1389352145472786433/nFc16aIm_normal.jpg", "hearts": 1, "retweets": 0}, "1413115316528324621": {"id": "1413115316528324621", "date": "2:38 PM - 8 Jul 2021", "content": "I just deployed a beautiful website with the dazzling Bulma <a href=\"https://twitter.com/search?q=%23CSS\">#CSS</a> framework by <a href=\"https://twitter.com/jgthms\">@jgthms</a>. UI design has never been more fun without this fantastic framework! Here is the website:<br><br><a href=\"https://t.co/XeTjAec1Ch\">divakar.fitness</a><br><br><a href=\"https://twitter.com/search?q=%23webdesign\">#webdesign</a> rocks with <a href=\"https://twitter.com/search?q=%23bulma\">#bulma</a>! <a href=\"https://t.co/K62MR1hb4z\">pic.twitter.com/K62MR1hb4z</a>", "fullname": "<PERSON><PERSON><PERSON><PERSON>", "username": "askjag07", "avatar": "https://pbs.twimg.com/profile_images/1452001768896872456/1BG2SfIS_normal.jpg", "hearts": 3, "retweets": 1}, "1413229777167818756": {"id": "1413229777167818756", "date": "10:13 PM - 8 Jul 2021", "content": "I developed a small utility web app for iOS. <br><br>Standing on the shoulder of giants; <PERSON><PERSON><PERSON> (<a href=\"https://twitter.com/jgthms\">@jgthms</a>), <PERSON><PERSON><PERSON> (<a href=\"https://twitter.com/itsjloh\">@itsjloh</a>), Ionicons (<a href=\"https://twitter.com/ionicons\">@ionicons</a>), PurifyCSS Online (@buggy1985), <a href=\"https://t.co/O7mtcjU17A\">web.dev/web-share</a> (<a href=\"https://twitter.com/medleyjp\">@medleyjp</a>), disclaimer (<a href=\"https://twitter.com/Termly_io\">@Termly_io</a>). Thanks!<br><br><a href=\"https://t.co/FSpnpULJjX\">heicdrop.kasperkamperman.com</a>", "fullname": "<PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/851722151879168000/-MVteS0T_normal.jpg", "hearts": 6, "retweets": 1}, "1413247782308229125": {"id": "1413247782308229125", "date": "11:25 PM - 8 Jul 2021", "content": "We've been using bulma a lot at <a href=\"https://twitter.com/radikalagency\">@radikalagency</a> !<br>Here's <a href=\"https://t.co/EckmgVBdju\">naviconus.com</a>, a site i'm really proud of. <br>Thanks, guys! And thanks, <PERSON>. <a href=\"https://twitter.com/jgthms\">@jgthms</a> <br><a href=\"https://twitter.com/search?q=%23madewithbulma\">#madewithbulma</a>", "fullname": "{adrian:salvat<PERSON>}", "username": "salvatori_dev", "avatar": "https://pbs.twimg.com/profile_images/1437582009480712197/hv5VXyVf_normal.jpg", "hearts": 3, "retweets": 0}, "1417301250887102466": {"id": "1417301250887102466", "date": "3:52 AM - 20 Jul 2021", "content": "(2/3) talented people, from all around the world! We're all really lucky to live in this time, a quick scan of the last ten centuries will tell you that - and it speaks volumes. I've been spending a lot of time on the <a href=\"https://twitter.com/search?q=%23bulmacss\">#bulmacss</a> (<a href=\"https://twitter.com/jgthms\">@jgthms</a>) and <a href=\"https://twitter.com/fontawesome\">@fontawesome</a> docs 💯", "fullname": "<PERSON> 🇺🇦", "username": "david<PERSON>7", "avatar": "https://pbs.twimg.com/profile_images/1519830070172471296/vVXBn85Q_normal.jpg", "hearts": 1, "retweets": 0}, "1417500321753010180": {"id": "1417500321753010180", "date": "5:03 PM - 20 Jul 2021", "content": "Currently Working on <a href=\"https://twitter.com/Subscribee_net\">@Subscribee_net</a>'s DM functionalities - <a href=\"https://twitter.com/jgthms\">@jgthms</a>' <a href=\"https://t.co/JLJWBozi6U\">bulma.io</a> is a hyper-accelerator when building the UI. <br><br>I want to modify it to look more like Twitters' DM UI but I'll definitely take the boilerplate that the panel-component provides! <a href=\"https://t.co/rgyFMAE04b\">pic.twitter.com/rgyFMAE04b</a>", "fullname": "Bengin *the webapp guy* <PERSON><PERSON><PERSON><PERSON>", "username": "ben<PERSON><PERSON>t", "avatar": "https://pbs.twimg.com/profile_images/1443099795586011136/gNBIkEu7_normal.jpg", "hearts": 4, "retweets": 2}, "1418358227499028482": {"id": "1418358227499028482", "date": "1:52 AM - 23 Jul 2021", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> Your question on the <a href=\"https://twitter.com/search?q=%23Bulma\">#Bulma</a> website about which version of sass do we use. You forgot to add, none.<br><br>LOVE <a href=\"https://twitter.com/search?q=%23Bulma\">#Bulma</a>. thank you. so easy to understand the classes.<br><br>I was tweaking other <a href=\"https://twitter.com/search?q=%23CSS\">#CSS</a> packages and naming classes close to <a href=\"https://twitter.com/search?q=%23Bulma\">#Bulma</a>, but i never finished and <a href=\"https://twitter.com/search?q=%23Bulma\">#Bulma</a> is magic", "fullname": "Aria Pictures", "username": "ariapictures", "avatar": "https://pbs.twimg.com/profile_images/462347201847386112/WvZTsyT5_normal.png", "hearts": 1, "retweets": 1}, "1422394818320166915": {"id": "1422394818320166915", "date": "5:12 AM - 3 Aug 2021", "content": "Here is my lovely website! <a href=\"https://twitter.com/search?q=%23madewithbulma\">#madewithbulma</a> <br><br><a href=\"https://t.co/9f7iojpHOA\">mailboxfiler.com</a><br><br>Thank you <a href=\"https://twitter.com/jgthms\">@jgthms</a> <a href=\"https://twitter.com/search?q=%23bulma\">#bulma</a>", "fullname": "Mailboxfiler", "username": "mailboxfiler", "avatar": "https://pbs.twimg.com/profile_images/1389352145472786433/nFc16aIm_normal.jpg", "hearts": 1, "retweets": 0}, "1425074438995791874": {"id": "1425074438995791874", "date": "2:39 PM - 10 Aug 2021", "content": "Great CSS framework by <a href=\"https://twitter.com/jgthms\">@jgthms</a> which soon will replace the majority of CSS frameworks, I believe.<br><a href=\"https://twitter.com/search?q=%23Kudos\">#Kudos</a> to team <a href=\"https://twitter.com/search?q=%23bulma\">#bulma</a><br><br><a href=\"https://t.co/Z0dSuqs3Vn\">bulma.io</a>", "fullname": "Code Topology", "username": "CodeTopology", "avatar": "https://pbs.twimg.com/profile_images/1424277783434252291/w4675yAm_normal.png", "hearts": 2, "retweets": 0}, "1431066442464645122": {"id": "1431066442464645122", "date": "3:30 AM - 27 Aug 2021", "content": "I can now say that <a href=\"https://twitter.com/jgthms\">@jgthms</a> bulma is a really good CSS framework. I find it to be less hassle than Tailwind and have just the right amount of utility classes to get stuff done. I still like Tailwind but <PERSON><PERSON><PERSON> is definitely an option for me now. <a href=\"https://t.co/txME3FRgcG\">bulma.io</a>", "fullname": "<PERSON>", "username": "silentworks", "avatar": "https://pbs.twimg.com/profile_images/1499802694420213760/Xk8HCfrN_normal.png", "hearts": 4, "retweets": 0}, "1433801542818168834": {"id": "1433801542818168834", "date": "4:38 PM - 3 Sep 2021", "content": "On various developer forums, NodeJS devs suggested BulmaCSS. <br><br>It has excellent documentation too... <a href=\"https://twitter.com/jgthms\">@jgthms</a> <br><br><a href=\"https://t.co/5NNuVK6wfd\">github.com/jgthms/bulma</a>", "fullname": "<PERSON><PERSON><PERSON> | అరవింద్ | अरविंद", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/881476892309585920/i9GC4hb5_normal.jpg", "hearts": 2, "retweets": 0}, "1446384291781488649": {"id": "1446384291781488649", "date": "9:57 AM - 8 Oct 2021", "content": "<a href=\"https://twitter.com/damengchen\">@damengchen</a> <a href=\"https://twitter.com/tailwindui\">@tailwindui</a> Similar story here 😀My skills lie primarily with backend development. But I moved from <a href=\"https://twitter.com/getbootstrap\">@getbootstrap</a> in the past to using <a href=\"https://t.co/jBdwOFL0fI\">bulma.io</a> from <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON><PERSON>", "username": "daangemist", "avatar": "https://pbs.twimg.com/profile_images/1294550100665147392/P4EvxdSu_normal.png", "hearts": 2, "retweets": 0}, "1074716163777679360": {"id": "1074716163777679360", "date": "5:21 PM - 17 Dec 2018", "content": "Just rebuilt my longest-running website using Bulma by <a href=\"https://twitter.com/jgthms\">@jgthms</a>, <PERSON><PERSON> and <PERSON>ueJS <a href=\"https://t.co/ZFdytkSJwP\">receptionhalls.com</a> <a href=\"https://twitter.com/search?q=%23madewithbulma\">#madewithbulma</a>.", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1020747664034430976/JPVqzg4W_normal.jpg", "hearts": 14, "retweets": 2}, "1455646795158204423": {"id": "1455646795158204423", "date": "10:23 PM - 2 Nov 2021", "content": "Did I forget to mention that <a href=\"https://t.co/iXrf5kWYfT\">shademotion.net</a> is made with <a href=\"https://twitter.com/vuejs\">@vuejs</a> and <PERSON><PERSON><PERSON> (by <a href=\"https://twitter.com/jgthms\">@jgthms</a>)?<br><br>What wonderful and powerful tools. I loved them and I continue using them.", "fullname": "<PERSON>", "username": "RandallZamoraCR", "avatar": "https://pbs.twimg.com/profile_images/874112716021022724/CcZhQekM_normal.jpg", "hearts": 1, "retweets": 0}, "1460086640303648768": {"id": "1460086640303648768", "date": "4:25 AM - 15 Nov 2021", "content": "Finished up my professional website made with <a href=\"https://twitter.com/search?q=%23bulma\">#bulma</a> css. Thanks <a href=\"https://twitter.com/jgthms\">@jgthms</a>! Check it out here: <a href=\"https://t.co/2HS0PXvJvv\">lucasbecker.dev</a>", "fullname": "<PERSON>", "username": "luca<PERSON><PERSON><PERSON>_dev", "avatar": "https://pbs.twimg.com/profile_images/1501303777885708288/C9sUQg9W_normal.jpg", "hearts": 1, "retweets": 0}, "1461104526816129024": {"id": "1461104526816129024", "date": "11:50 PM - 17 Nov 2021", "content": "Been playing around with <PERSON><PERSON><PERSON> by <a href=\"https://twitter.com/jgthms\">@jgthms</a> for the last few days, and it's exactly how every CSS framework should be: <br><br>0% Javascript!<br><br>Definitely replacing bootstrap as my go-to.<br><br><a href=\"https://t.co/Osutl9o3BQ\">bulma.io</a>", "fullname": "<PERSON> 🍊", "username": "RealOrangeOne", "avatar": "https://pbs.twimg.com/profile_images/600770928021221378/Ff5U8Zah_normal.png", "hearts": 1, "retweets": 0}, "1462024034972020742": {"id": "1462024034972020742", "date": "12:44 PM - 20 Nov 2021", "content": "Redesigning &amp; remaking our website using <a href=\"https://t.co/t0H7q4mcFG\">bulma.io</a> from <a href=\"https://twitter.com/jgthms\">@jgthms</a><br>! <a href=\"https://t.co/QaXhJWjm24\">pic.twitter.com/QaXhJWjm24</a>", "fullname": "<PERSON><PERSON><PERSON><PERSON>", "username": "nosesisaid", "avatar": "https://pbs.twimg.com/profile_images/1441498150984982528/GWzRORER_normal.jpg", "hearts": 3, "retweets": 1}, "1471178177569443840": {"id": "1471178177569443840", "date": "6:59 PM - 15 Dec 2021", "content": "How To Create A Resume Website Using Bulma CSS <a href=\"https://t.co/0W5Hlxn3Da\">heksagon.net/web-design/how…</a> <a href=\"https://twitter.com/search?q=%23bulma\">#bulma</a> <a href=\"https://twitter.com/search?q=%23scss\">#scss</a> <a href=\"https://twitter.com/search?q=%23iconify\">#iconify</a> <a href=\"https://twitter.com/search?q=%23jgthms\">#jgthms</a> <a href=\"https://twitter.com/search?q=%23animateonscroll\">#animateonscroll</a> <br><br><a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "<PERSON><PERSON><PERSON>", "username": "Heksagonnet", "avatar": "https://pbs.twimg.com/profile_images/1469847302822981634/fdbbOtst_normal.jpg", "hearts": 16, "retweets": 4}, "1471456919445377026": {"id": "1471456919445377026", "date": "1:27 PM - 16 Dec 2021", "content": "<a href=\"https://twitter.com/Heksagonnet\">@Heksagonnet</a> <a href=\"https://twitter.com/jgthms\">@jgthms</a> <PERSON><PERSON><PERSON> is 🔥, i love bulma, Still dont know why <a href=\"https://twitter.com/nuxt_js\">@nuxt_js</a> stop supporting <PERSON><PERSON><PERSON>", "fullname": "<PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1302267009816604674/8tggDGxJ_normal.jpg", "hearts": 1, "retweets": 0}, "1472281183551639563": {"id": "1472281183551639563", "date": "8:02 PM - 18 Dec 2021", "content": "As a long Rails fanatic, I hope <a href=\"https://twitter.com/bulmaio\">@bulmaio</a> is next 😍  by <a href=\"https://twitter.com/jgthms\">@jgthms</a> a semantic friendly css framework with zero Javascript. It fits perfectly with hotwire's philosophy <a href=\"https://twitter.com/dhh\">@dhh</a> My faith in frontend is restored! <a href=\"https://t.co/Vjqzjzn12X\">twitter.com/dhh/status/147…</a>", "fullname": "Azzen Abidi", "username": "funkypan<PERSON>ath", "avatar": "https://pbs.twimg.com/profile_images/1400963207401979905/AHaRHQF0_normal.jpg", "hearts": 3, "retweets": 0}, "1472509235405725699": {"id": "1472509235405725699", "date": "11:08 AM - 19 Dec 2021", "content": "<a href=\"https://twitter.com/palashv2\">@palashv2</a> 🔹 <PERSON><PERSON><PERSON> <a href=\"https://twitter.com/jgthms\">@jgthms</a> ⭐", "fullname": "Pr. <PERSON><PERSON><PERSON> ✪", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1508213506151174146/mqlVsrw__normal.jpg", "hearts": 1, "retweets": 0}, "1472906659001475072": {"id": "1472906659001475072", "date": "1:28 PM - 20 Dec 2021", "content": "I don drop <PERSON><PERSON><PERSON> and tailwind for una ooo<br><br><PERSON><PERSON><PERSON> all the way. <a href=\"https://t.co/8d0MUpJZbx\">twitter.com/AKirtesh/statu…</a>", "fullname": "<PERSON><PERSON><PERSON>", "username": "akiode_timothy", "avatar": "https://pbs.twimg.com/profile_images/1438088324879732739/LHz7wNKU_normal.jpg", "hearts": 2, "retweets": 0}, "1473128114331045893": {"id": "1473128114331045893", "date": "4:08 AM - 21 Dec 2021", "content": "Been heads down cranking on the book, \"Learning Blazor\". Humbling experience, and I'm immensely grateful for this opportunity! Worked my ass off to get here, and I've made so many good friends along the way. Thank you <a href=\"https://twitter.com/Ben<PERSON><PERSON>a\">@<PERSON><PERSON><PERSON><PERSON></a> for personalized help, and <a href=\"https://twitter.com/jgthms\">@jgthms</a> for <a href=\"https://twitter.com/search?q=%23Bulma\">#Bulma</a> 🤓 <a href=\"https://t.co/x7PHjcS2r3\">pic.twitter.com/x7PHjcS2r3</a>", "fullname": "<PERSON> 🇺🇦", "username": "david<PERSON>7", "avatar": "https://pbs.twimg.com/profile_images/1519830070172471296/vVXBn85Q_normal.jpg", "hearts": 10, "retweets": 1}, "1473228760140951559": {"id": "1473228760140951559", "date": "10:48 AM - 21 Dec 2021", "content": "<a href=\"https://twitter.com/TheAnkurTyagi\">@TheAnkurTyagi</a> B<PERSON><PERSON> by <a href=\"https://twitter.com/jgthms\">@jgthms</a> comes to mind, lots of examples included. <a href=\"https://t.co/igUpq8Si9K\">bulma.io/documentation/</a>", "fullname": "💻Gerard Klijs🦀", "username": "GKlijs", "avatar": "https://pbs.twimg.com/profile_images/1392047192395980801/cY3xKZRq_normal.jpg", "hearts": 4, "retweets": 0}, "1474197146224234502": {"id": "1474197146224234502", "date": "2:56 AM - 24 Dec 2021", "content": "Amazing CSS Framework ever. Did we mention that we are building our future <a href=\"https://t.co/1MGV5KplDf\">bestforandroid.com</a> project on <a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "BestForAndroid", "username": "BestForAndroid_", "avatar": "https://pbs.twimg.com/profile_images/1498757510085455881/V0mzyX2G_normal.jpg", "hearts": 1, "retweets": 0}, "1483496364638146560": {"id": "1483496364638146560", "date": "6:47 PM - 18 Jan 2022", "content": "<a href=\"https://twitter.com/jgthms\">@jgthms</a> - Bulma<br><br>Bulma is a modern CSS framework based on flexbox. It provides responsive design and mobile-first UI components and has the modular structure for you to import only the kinds of stuff that you want to include in your web design.", "fullname": "<PERSON><PERSON>", "username": "sidi_jedd<PERSON>_dev", "avatar": "https://pbs.twimg.com/profile_images/1481040000254885905/ucZCXMcY_normal.jpg", "hearts": 2, "retweets": 0}, "1485712571080585222": {"id": "1485712571080585222", "date": "9:34 PM - 24 Jan 2022", "content": "Using React + <a href=\"https://t.co/hSF1HI8RzL\">Bluma.io</a> (<a href=\"https://twitter.com/jgthms\">@jgthms</a>) + <a href=\"https://twitter.com/apexcharts\">@apexcharts</a> <br>Now on to creating the schema and integrating supabase. <a href=\"https://t.co/FMf7oqgvh8\">pic.twitter.com/FMf7oqgvh8</a>", "fullname": "<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1485633593825275910/c4s_pnGG_normal.jpg", "hearts": 2, "retweets": 1}, "1486757889863651328": {"id": "1486757889863651328", "date": "6:47 PM - 27 Jan 2022", "content": "<a href=\"https://twitter.com/RobStuttaford\">@RobStuttaford</a> <a href=\"https://twitter.com/jgthms\">@jgthms</a> We use Bulma and Garden with front-end injected styles so we get full language features. Of course, we are building pretty small apps with modest CSS needs, but <PERSON><PERSON><PERSON> has never called to me. Before Bulma we used Bootstrap, but purity.", "fullname": "(webdev \"Tory\")", "username": "Endless_WebDev", "avatar": "https://pbs.twimg.com/profile_images/1394308910660550656/Lt4nghdQ_normal.jpg", "hearts": 2, "retweets": 0}, "1490752836019310594": {"id": "1490752836019310594", "date": "7:22 PM - 7 Feb 2022", "content": "Shoutout to <a href=\"https://t.co/zX3DImd8px\">bulma.io</a> and <a href=\"https://twitter.com/jgthms\">@jgthms</a> because I use <PERSON><PERSON><PERSON> way too much for my own good.", "fullname": "Sideliner", "username": "<PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1269657048846827520/PCcY5icg_normal.jpg", "hearts": 2, "retweets": 0}, "1494550520794652673": {"id": "1494550520794652673", "date": "6:53 AM - 18 Feb 2022", "content": "Another one. Built with <a href=\"https://twitter.com/search?q=%23bulma\">#bulma</a> <a href=\"https://twitter.com/jgthms\">@jgthms</a>  .<br>I am begining to love <PERSON><PERSON><PERSON>.<br><a href=\"https://twitter.com/search?q=%23100Devs\">#100Devs</a> <a href=\"https://twitter.com/search?q=%23100DaysOfCode\">#100DaysOfCode</a> <a href=\"https://twitter.com/search?q=%23HTML\">#HTML</a> <a href=\"https://twitter.com/search?q=%23HTML5\">#HTML5</a>  <a href=\"https://twitter.com/search?q=%23design\">#design</a> <a href=\"https://twitter.com/search?q=%23code\">#code</a> <br><br>P.S: source code is available 👇👇 <a href=\"https://t.co/HlEHtNZZTc\">pic.twitter.com/HlEHtNZZTc</a>", "fullname": "<PERSON> (何利敏敏敏)", "username": "Budex1", "avatar": "https://pbs.twimg.com/profile_images/1283190919508959234/KCwnJxNe_normal.jpg", "hearts": 1, "retweets": 2}, "1494749634006331392": {"id": "1494749634006331392", "date": "8:04 PM - 18 Feb 2022", "content": "<a href=\"https://twitter.com/TreciaKS\">@TreciaKS</a> Bulma CSS by <a href=\"https://twitter.com/jgthms\">@jgthms</a> is great too.", "fullname": "@LordSamDev", "username": "<PERSON><PERSON><PERSON><PERSON>", "avatar": "https://pbs.twimg.com/profile_images/1449448633980571650/1Ol2QLvL_normal.jpg", "hearts": 2, "retweets": 0}, "1496753247767482369": {"id": "1496753247767482369", "date": "8:45 AM - 24 Feb 2022", "content": "Bulma again !!! <a href=\"https://twitter.com/jgthms\">@jgthms</a><br><a href=\"https://twitter.com/search?q=%23code\">#code</a> <a href=\"https://twitter.com/search?q=%23Codex\">#Codex</a> <a href=\"https://twitter.com/search?q=%23100DaysOfCode\">#100DaysOfCode</a> <a href=\"https://t.co/nOi2g8J4JE\">pic.twitter.com/nOi2g8J4JE</a>", "fullname": "<PERSON> (何利敏敏敏)", "username": "Budex1", "avatar": "https://pbs.twimg.com/profile_images/1283190919508959234/KCwnJxNe_normal.jpg", "hearts": 3, "retweets": 4}, "1498088826761199616": {"id": "1498088826761199616", "date": "1:13 AM - 28 Feb 2022", "content": "Newest <a href=\"https://twitter.com/search?q=%23webdev\">#webdev</a> project: <br><a href=\"https://twitter.com/search?q=%23Sqwordle\">#Sqwordle</a> - <a href=\"https://twitter.com/search?q=%23Wordle\">#Wordle</a> for <a href=\"https://twitter.com/search?q=%23Pokemon\">#Pokemon</a>.<br>- <a href=\"https://twitter.com/search?q=%23ReactJS\">#ReactJS</a> JavaScript framework<br>- <a href=\"https://t.co/FwgD6tMZS7\">bulma.io</a> CSS framework by <a href=\"https://twitter.com/jgthms\">@jgthms</a> <br>- <a href=\"https://twitter.com/PokeAPI\">@PokeAPI</a> for Pokémon data<br><br>Hope you enjoy!<br><br><a href=\"https://t.co/65I7VWkXKy\">sqwordle.io</a>", "fullname": "<PERSON>", "username": "nmfretz", "avatar": "https://pbs.twimg.com/profile_images/1475328014795771905/x0y9_zpy_normal.jpg", "hearts": 1, "retweets": 0}, "1501192708131598337": {"id": "1501192708131598337", "date": "2:46 PM - 8 Mar 2022", "content": "I made a google clone yesterday with <a href=\"https://twitter.com/search?q=%23Bulma\">#Bulma</a> in 2 hours. What other rapid prototyping tools should I learn? <a href=\"https://twitter.com/figma\">@figma</a> <a href=\"https://twitter.com/uxpin\">@uxpin</a> <a href=\"https://twitter.com/jgthms\">@jgthms</a> <a href=\"https://t.co/vMXKBUP7G3\">pic.twitter.com/vMXKBUP7G3</a>", "fullname": "<PERSON><PERSON>", "username": "geren8te", "avatar": "https://pbs.twimg.com/profile_images/1186822199308673024/3IWqWK7F_normal.jpg", "hearts": 2, "retweets": 0}, "1501482882434539521": {"id": "1501482882434539521", "date": "8:59 AM - 9 Mar 2022", "content": "My private <a href=\"https://twitter.com/search?q=%23freifunk\">#freifunk</a> webpage use the bulma framwork &lt;3<br><a href=\"https://t.co/2wNKG9BDa8\">leeraner-freifunk.de</a><br><a href=\"https://twitter.com/search?q=%23bulmaio\">#bulmaio</a> via <a href=\"https://twitter.com/jgthms\">@jgthms</a>", "fullname": "Nico", "username": "nok3s", "avatar": "https://pbs.twimg.com/profile_images/560006099261489152/mnpVMJm4_normal.jpeg", "hearts": 1, "retweets": 0}}, "tweets": ["1012427478915256320", "1012491101264064512", "1012491101264064512", "1012491101264064512", "1012491101264064512", "1012491101264064512", "1012491101264064512", "1012491101264064512", "1012491101264064512", "1012491101264064512", "1012491101264064512", "1012491101264064512", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012670247323697155", "1012674477962944513", "1012674477962944513", "1012674477962944513", "1012674477962944513", "1012674477962944513", "1012674477962944513", "1012674477962944513", "1012674477962944513", "1012674477962944513", "1012674477962944513", "1012674477962944513", "1012674477962944513", "1012674477962944513", "1012674477962944513", "1012674477962944513", "1012674477962944513", "1012674477962944513", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013079944178458624", "1013507295739707393", "1013507295739707393", "1013507295739707393", "1013507295739707393", "1013507295739707393", "1013507295739707393", "1013507295739707393", "1013507295739707393", "1013507295739707393", "1013507295739707393", "1013507295739707393", "1013507295739707393", "1013507295739707393", "1013507295739707393", "1013507295739707393", "1013507295739707393", "1013507295739707393", "1013507295739707393", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013700257920634880", "1013933065096159233", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016062253349601280", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016398509493374976", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1016508485738131458", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1020920571167531008", "1024739774534303744", "1024739774534303744", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1025375916367794176", "1032476756308119554", "1032476756308119554", "1032476756308119554", "1032476756308119554", "1032476756308119554", "1032476756308119554", "1032476756308119554", "1032476756308119554", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1033211102245646337", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1037655983663861760", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1038461036037324802", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039577420053921792", "1039625285640118272", "1039625285640118272", "1039625285640118272", "1039625285640118272", "1039625285640118272", "1039625285640118272", "1039625285640118272", "1039625285640118272", "1039625285640118272", "1039625285640118272", "1039625285640118272", "1039625285640118272", "1039625285640118272", "1039625285640118272", "1039625285640118272", "1039625285640118272", "1039625285640118272", "1039625285640118272", "1039625285640118272", "1039625285640118272", "1039625285640118272", "1039625285640118272", "1039625285640118272", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1042348474002550784", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1044117881582059520", "1047846962412900352", "1047846962412900352", "1047846962412900352", "1047846962412900352", "1047846962412900352", "1047846962412900352", "1047846962412900352", "1047846962412900352", "1047846962412900352", "1047846962412900352", "1047846962412900352", "1047846962412900352", "1047846962412900352", "1047846962412900352", "1047846962412900352", "1047846962412900352", "1047846962412900352", "1047846962412900352", "1047846962412900352", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051534293665808390", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1051978773174734848", "1062394741092950018", "1062394741092950018", "1062394741092950018", "1062394741092950018", "1062394741092950018", "1062394741092950018", "1062394741092950018", "1062394741092950018", "1062394741092950018", "1062394741092950018", "1062394741092950018", "1062394741092950018", "1062394741092950018", "1062394741092950018", "1062394741092950018", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064150088480710657", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064592414315372544", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1064595042172583938", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1065162109900738560", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1067168440333606912", "1070320154452656128", "1070320154452656128", "1070320154452656128", "1070320154452656128", "1070320154452656128", "1070320154452656128", "1070320154452656128", "1070320154452656128", "1070320154452656128", "1070320154452656128", "1070320154452656128", "1070320154452656128", "1070320154452656128", "1070320154452656128", "1070320154452656128", "1070320154452656128", "1070320154452656128", "1070320154452656128", "1070320154452656128", "1070320154452656128", "1070320154452656128", "1070320154452656128", "1070320154452656128", "1070320154452656128", "1070320154452656128", "1070320154452656128", "1070320154452656128", "1070320154452656128", "1070320154452656128", "1070320154452656128", "1071108327554265088", "1071108327554265088", "1071108327554265088", "1071108327554265088", "1071108327554265088", "1071108327554265088", "1071108327554265088", "1071108327554265088", "1071108327554265088", "1071108327554265088", "1071108327554265088", "1071108327554265088", "1071108327554265088", "1071108327554265088", "1071108327554265088", "1071108327554265088", "1071108327554265088", "1071108327554265088", "1071108327554265088", "1071108327554265088", "1071108327554265088", "1071108327554265088", "1071108327554265088", "1071108327554265088", "1071108327554265088", "1071108327554265088", "1071108327554265088", "1071108327554265088", "1071108327554265088", "1071108327554265088", "1071179047244316677", "1071179047244316677", "1071179047244316677", "1071179047244316677", "1071179047244316677", "1071179047244316677", "1071179047244316677", "1071179047244316677", "1071179047244316677", "1071179047244316677", "1072757345959247873", "1072757345959247873", "1072757345959247873", "1072757345959247873", "1072757345959247873", "1072757345959247873", "1072757345959247873", "1072757345959247873", "1072757345959247873", "1072757345959247873", "1072757345959247873", "1072757345959247873", "1072757345959247873", "1072757345959247873", "1072757345959247873", "1072757345959247873", "1072757345959247873", "1072757345959247873", "1072757345959247873", "1072757345959247873", "1072757345959247873", "1072757345959247873", "1072757345959247873", "1072757345959247873", "1072757345959247873", "1072757345959247873", "1072757345959247873", "1072757345959247873", "1072757345959247873", "1072757345959247873", "1073285247473721345", "1073285247473721345", "1073285247473721345", "1073285247473721345", "1073285247473721345", "1073285247473721345", "1073285247473721345", "1073285247473721345", "1073285247473721345", "1073285247473721345", "1073285247473721345", "1073285247473721345", "1073285247473721345", "1073285247473721345", "1073285247473721345", "1073405452166283264", "1073405452166283264", "1073405452166283264", "1073405452166283264", "1073405452166283264", "1073405452166283264", "1073405452166283264", "1073405452166283264", "1073405452166283264", "1073405452166283264", "1073405452166283264", "1073405452166283264", "1073405452166283264", "1073405452166283264", "1073405452166283264", "1073405452166283264", "1073405452166283264", "1073405452166283264", "1073405452166283264", "1073405452166283264", "1073405452166283264", "1073405452166283264", "1073405452166283264", "1073405452166283264", "1073405452166283264", "1073405452166283264", "1073405452166283264", "1073405452166283264", "1073405452166283264", "1073405452166283264", "1074716163777679360", "1074716163777679360", "1074716163777679360", "1074716163777679360", "1076031886332440576", "1076031886332440576", "1076031886332440576", "1076031886332440576", "1076031886332440576", "1076031886332440576", "1076031886332440576", "1076031886332440576", "1076031886332440576", "1076031886332440576", "1076031886332440576", "1076031886332440576", "1076031886332440576", "1076031886332440576", "1076031886332440576", "1076031886332440576", "1076031886332440576", "1076031886332440576", "1076031886332440576", "1076031886332440576", "1076031886332440576", "1076031886332440576", "1076031886332440576", "1076031886332440576", "1076031886332440576", "1076031886332440576", "1076031886332440576", "1076031886332440576", "1076031886332440576", "1076031886332440576", "1076306162277072897", "1076306162277072897", "1076306162277072897", "1076306162277072897", "1076306162277072897", "1076306162277072897", "1076306162277072897", "1076306162277072897", "1076306162277072897", "1076306162277072897", "1076306162277072897", "1076306162277072897", "1076306162277072897", "1076306162277072897", "1076306162277072897", "1076306162277072897", "1076306162277072897", "1076306162277072897", "1076306162277072897", "1076306162277072897", "1076306162277072897", "1076306162277072897", "1076306162277072897", "1076306162277072897", "1076306162277072897", "1076306162277072897", "1076306162277072897", "1076306162277072897", "1076306162277072897", "1076306162277072897", "1076520024779751424", "1076520024779751424", "1076520024779751424", "1076520024779751424", "1076520024779751424", "1076520024779751424", "1076520024779751424", "1076520024779751424", "1076520024779751424", "1076520024779751424", "1076520024779751424", "1076520024779751424", "1076520024779751424", "1076520024779751424", "1076520024779751424", "1076520024779751424", "1076520024779751424", "1076520024779751424", "1079283026285064192", "1079283026285064192", "1079283026285064192", "1079283026285064192", "1079283026285064192", "1079283026285064192", "1079283026285064192", "1079283026285064192", "1079283026285064192", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084590757254696965", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084720361927729152", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1084915146793852928", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1088447825803800577", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1093467834209001472", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1097215157711843328", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1103377098490724352", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1104190974106918912", "1106622513159856131", "1106622513159856131", "1106622513159856131", "1106622513159856131", "1106622513159856131", "1106622513159856131", "1106622513159856131", "1106622513159856131", "1106622513159856131", "1106622513159856131", "1106622513159856131", "1106622513159856131", "1106622513159856131", "1106622513159856131", "1106622513159856131", "1106622513159856131", "1106622513159856131", "1106622513159856131", "1106622513159856131", "1106622513159856131", "1106622513159856131", "1106622513159856131", "1106622513159856131", "1106622513159856131", "1106622513159856131", "1106622513159856131", "1106622513159856131", "1106622513159856131", "1106622513159856131", "1106622513159856131", "1106622513159856131", "1106971813014315010", "1106971813014315010", "1106971813014315010", "1106971813014315010", "1106971813014315010", "1106971813014315010", "1106971813014315010", "1106971813014315010", "1106971813014315010", "1106971813014315010", "1106971813014315010", "1106971813014315010", "1106971813014315010", "1106971813014315010", "1106971813014315010", "1106971813014315010", "1106971813014315010", "1106971813014315010", "1106971813014315010", "1106971813014315010", "1106971813014315010", "1106971813014315010", "1106971813014315010", "1106971813014315010", "1106971813014315010", "1106971813014315010", "1106971813014315010", "1106971813014315010", "1106971813014315010", "1106971813014315010", "1106971813014315010", "1108831735419154432", "1108831735419154432", "1108831735419154432", "1108831735419154432", "1108831735419154432", "1108831735419154432", "1108831735419154432", "1108831735419154432", "1108831735419154432", "1108831735419154432", "1108831735419154432", "1108831735419154432", "1108831735419154432", "1108831735419154432", "1108831735419154432", "1108831735419154432", "1108831735419154432", "1108831735419154432", "1108831735419154432", "1108831735419154432", "1108831735419154432", "1108831735419154432", "1108831735419154432", "1108831735419154432", "1108831735419154432", "1108831735419154432", "1108831735419154432", "1108831735419154432", "1108831735419154432", "1108831735419154432", "1108831735419154432", "1109195636308692994", "1109195636308692994", "1109195636308692994", "1109195636308692994", "1109195636308692994", "1109195636308692994", "1109195636308692994", "1109195636308692994", "1109195636308692994", "1109195636308692994", "1109195636308692994", "1109195636308692994", "1109195636308692994", "1109195636308692994", "1109195636308692994", "1109195636308692994", "1109195636308692994", "1109195636308692994", "1109195636308692994", "1109195636308692994", "1109195636308692994", "1109195636308692994", "1109195636308692994", "1109195636308692994", "1109195636308692994", "1109195636308692994", "1109195636308692994", "1109195636308692994", "1109195636308692994", "1109195636308692994", "1109195636308692994", "1110678480507883520", "1110678480507883520", "1110678480507883520", "1110678480507883520", "1110678480507883520", "1110678480507883520", "1110678480507883520", "1111371782051762187", "1111371782051762187", "1111371782051762187", "1111371782051762187", "1111371782051762187", "1111371782051762187", "1111371782051762187", "1111371782051762187", "1111371782051762187", "1111371782051762187", "1111371782051762187", "1111371782051762187", "1111371782051762187", "1111371782051762187", "1111371782051762187", "1111371782051762187", "1111371782051762187", "1111371782051762187", "1111371782051762187", "1111371782051762187", "1111371782051762187", "1111371782051762187", "1111371782051762187", "1111371782051762187", "1111371782051762187", "1111371782051762187", "1111371782051762187", "1111371782051762187", "1111371782051762187", "1111371782051762187", "1111768111273762816", "1111768111273762816", "1111768111273762816", "1111768111273762816", "1111768111273762816", "1111768111273762816", "1111768111273762816", "1111768111273762816", "1111768111273762816", "1111768111273762816", "1111768111273762816", "1111768111273762816", "1111768111273762816", "1111768111273762816", "1111768111273762816", "1111768111273762816", "1111768111273762816", "1111768111273762816", "1111768111273762816", "1111768111273762816", "1111768111273762816", "1111768111273762816", "1111768111273762816", "1111768111273762816", "1111768111273762816", "1111768111273762816", "1111768111273762816", "1111768111273762816", "1111768111273762816", "1111768111273762816", "1111768111273762816", "1113143442991751168", "1113143442991751168", "1113143442991751168", "1113143442991751168", "1113143442991751168", "1113143442991751168", "1113143442991751168", "1113143442991751168", "1113143442991751168", "1113143442991751168", "1113143442991751168", "1113143442991751168", "1113143442991751168", "1113143442991751168", "1113143442991751168", "1113143442991751168", "1113143442991751168", "1113143442991751168", "1113143442991751168", "1113143442991751168", "1113143442991751168", "1113143442991751168", "1113143442991751168", "1113143442991751168", "1113143442991751168", "1113143442991751168", "1113143442991751168", "1113143442991751168", "1113143442991751168", "1114064132364488705", "1114064132364488705", "1114064132364488705", "1114064132364488705", "1114064132364488705", "1114064132364488705", "1114064132364488705", "1114064132364488705", "1114064132364488705", "1114064132364488705", "1114064132364488705", "1114064132364488705", "1114064132364488705", "1114064132364488705", "1114064132364488705", "1114064132364488705", "1114064132364488705", "1114064132364488705", "1114064132364488705", "1114064132364488705", "1114064132364488705", "1114064132364488705", "1114064132364488705", "1114064132364488705", "1114064132364488705", "1114064132364488705", "1114064132364488705", "1114064132364488705", "1114064132364488705", "1114085081050755072", "1114085081050755072", "1114085081050755072", "1114085081050755072", "1114085081050755072", "1114085081050755072", "1114085081050755072", "1114085081050755072", "1114085081050755072", "1114085081050755072", "1114085081050755072", "1114085081050755072", "1114085081050755072", "1114085081050755072", "1114085081050755072", "1114085081050755072", "1114085081050755072", "1114085081050755072", "1114085081050755072", "1114085081050755072", "1114085081050755072", "1114085081050755072", "1114085081050755072", "1114085081050755072", "1114085081050755072", "1114085081050755072", "1114085081050755072", "1114085081050755072", "1114085081050755072", "1123933715556597761", "1123933715556597761", "1123933715556597761", "1123933715556597761", "1123933715556597761", "1123933715556597761", "1123933715556597761", "1123933715556597761", "1123933715556597761", "1123933715556597761", "1123933715556597761", "1123933715556597761", "1123933715556597761", "1123933715556597761", "1123933715556597761", "1123933715556597761", "1123933715556597761", "1123933715556597761", "1123933715556597761", "1123933715556597761", "1123933715556597761", "1123933715556597761", "1123933715556597761", "1123933715556597761", "1123933715556597761", "1123933715556597761", "1123933715556597761", "1123933715556597761", "1123933715556597761", "1123938071957909508", "1123938071957909508", "1123938071957909508", "1126857886599122949", "1126857886599122949", "1126857886599122949", "1126857886599122949", "1126857886599122949", "1126857886599122949", "1126857886599122949", "1126857886599122949", "1126857886599122949", "1126857886599122949", "1126857886599122949", "1126857886599122949", "1126857886599122949", "1126857886599122949", "1126857886599122949", "1126857886599122949", "1126857886599122949", "1126857886599122949", "1126857886599122949", "1126857886599122949", "1126857886599122949", "1126857886599122949", "1126857886599122949", "1126857886599122949", "1126857886599122949", "1127131435498762245", "1127131435498762245", "1127131435498762245", "1127131435498762245", "1127131435498762245", "1127131435498762245", "1127131435498762245", "1127131435498762245", "1127131435498762245", "1127131435498762245", "1127131435498762245", "1127131435498762245", "1127131435498762245", "1127131435498762245", "1127131435498762245", "1127131435498762245", "1127131435498762245", "1127131435498762245", "1127131435498762245", "1127131435498762245", "1127131435498762245", "1127131435498762245", "1127131435498762245", "1127131435498762245", "1127131435498762245", "1127131435498762245", "1127131435498762245", "1127131435498762245", "1127131435498762245", "1127151956940722177", "1127151956940722177", "1127151956940722177", "1127151956940722177", "1127151956940722177", "1127151956940722177", "1127151956940722177", "1127151956940722177", "1127151956940722177", "1127151956940722177", "1127151956940722177", "1127151956940722177", "1127151956940722177", "1127151956940722177", "1127151956940722177", "1127151956940722177", "1127151956940722177", "1127151956940722177", "1127151956940722177", "1127151956940722177", "1127151956940722177", "1127151956940722177", "1127151956940722177", "1127151956940722177", "1127151956940722177", "1127151956940722177", "1127151956940722177", "1127151956940722177", "1127151956940722177", "1127287630872424449", "1127287630872424449", "1127287630872424449", "1127287630872424449", "1127287630872424449", "1127287630872424449", "1127287630872424449", "1127287630872424449", "1127287630872424449", "1127287630872424449", "1127287630872424449", "1127287630872424449", "1127287630872424449", "1127287630872424449", "1127287630872424449", "1127287630872424449", "1127287630872424449", "1127287630872424449", "1127287630872424449", "1127287630872424449", "1127287630872424449", "1127287630872424449", "1127287630872424449", "1127287630872424449", "1127287630872424449", "1127287630872424449", "1127287630872424449", "1127287630872424449", "1127287630872424449", "1133491490263621638", "1133491490263621638", "1133491490263621638", "1133491490263621638", "1133491490263621638", "1133491490263621638", "1133491490263621638", "1133491490263621638", "1133491490263621638", "1133491490263621638", "1133491490263621638", "1133491490263621638", "1133491490263621638", "1133491490263621638", "1133491490263621638", "1133491490263621638", "1133491490263621638", "1133491490263621638", "1133491490263621638", "1133491490263621638", "1133491490263621638", "1133491490263621638", "1133491490263621638", "1133491490263621638", "1133491490263621638", "1133491490263621638", "1133491490263621638", "1133491490263621638", "1136497784700514304", "1136497784700514304", "1136497784700514304", "1136497784700514304", "1136497784700514304", "1136497784700514304", "1136497784700514304", "1136497784700514304", "1136497784700514304", "1136497784700514304", "1136497784700514304", "1136497784700514304", "1136497784700514304", "1136497784700514304", "1136497784700514304", "1136497784700514304", "1136497784700514304", "1136497784700514304", "1136497784700514304", "1136497784700514304", "1136497784700514304", "1136497784700514304", "1136497784700514304", "1136497784700514304", "1136497784700514304", "1136497784700514304", "1136497784700514304", "1136497784700514304", "1137040277804371970", "1137040277804371970", "1137040277804371970", "1137697312384475137", "1137697312384475137", "1137697312384475137", "1137697312384475137", "1137697312384475137", "1137697312384475137", "1137697312384475137", "1137697312384475137", "1137697312384475137", "1137697312384475137", "1137697312384475137", "1137697312384475137", "1137697312384475137", "1137697312384475137", "1137697312384475137", "1137697312384475137", "1137697312384475137", "1137697312384475137", "1137697312384475137", "1137697312384475137", "1137697312384475137", "1137697312384475137", "1137697312384475137", "1137697312384475137", "1137697312384475137", "1137697312384475137", "1137697312384475137", "1137697312384475137", "1140839283692982274", "1140839283692982274", "1140839283692982274", "1140839283692982274", "1140839283692982274", "1140839283692982274", "1140839283692982274", "1140839283692982274", "1140839283692982274", "1140839283692982274", "1140839283692982274", "1140839283692982274", "1140839283692982274", "1140839283692982274", "1140839283692982274", "1140839283692982274", "1140839283692982274", "1140839283692982274", "1140839283692982274", "1140839283692982274", "1140839283692982274", "1140839283692982274", "1140839283692982274", "1140839283692982274", "1140839283692982274", "1140839283692982274", "1140839283692982274", "1140839283692982274", "1141831637497368576", "1141831637497368576", "1141831637497368576", "1141831637497368576", "1141831637497368576", "1141831637497368576", "1141831637497368576", "1141831637497368576", "1141831637497368576", "1141831637497368576", "1141831637497368576", "1141831637497368576", "1141831637497368576", "1141831637497368576", "1141831637497368576", "1141831637497368576", "1141831637497368576", "1141831637497368576", "1141831637497368576", "1141831637497368576", "1141831637497368576", "1141831637497368576", "1141831637497368576", "1141831637497368576", "1141831637497368576", "1141831637497368576", "1141831637497368576", "1141831637497368576", "1144724239133609985", "1144724239133609985", "1144724239133609985", "1144724239133609985", "1144724239133609985", "1144724239133609985", "1144724239133609985", "1144724239133609985", "1144724239133609985", "1144724239133609985", "1144724239133609985", "1144724239133609985", "1144724239133609985", "1144724239133609985", "1144724239133609985", "1144724239133609985", "1144724239133609985", "1144724239133609985", "1144724239133609985", "1144724239133609985", "1144724239133609985", "1144724239133609985", "1144724239133609985", "1144724239133609985", "1144724239133609985", "1144724239133609985", "1144724239133609985", "1144724239133609985", "1148364620714958848", "1148364620714958848", "1148364620714958848", "1148364620714958848", "1148364620714958848", "1148364620714958848", "1148364620714958848", "1148364620714958848", "1148364620714958848", "1148364620714958848", "1148364620714958848", "1148364620714958848", "1148364620714958848", "1148364620714958848", "1148364620714958848", "1148364620714958848", "1148364620714958848", "1148364620714958848", "1148364620714958848", "1148364620714958848", "1148364620714958848", "1148364620714958848", "1150109429238501376", "1150109429238501376", "1150109429238501376", "1150109429238501376", "1150109429238501376", "1150109429238501376", "1150109429238501376", "1150109429238501376", "1150109429238501376", "1150109429238501376", "1150109429238501376", "1150109429238501376", "1150109429238501376", "1150109429238501376", "1150109429238501376", "1150109429238501376", "1150109429238501376", "1150109429238501376", "1150109429238501376", "1150109429238501376", "1150109429238501376", "1150109429238501376", "1150109429238501376", "1150109429238501376", "1150109429238501376", "1150109429238501376", "1150109429238501376", "1150151867898503171", "1150151867898503171", "1150151867898503171", "1150151867898503171", "1150151867898503171", "1150151867898503171", "1150151867898503171", "1150151867898503171", "1150151867898503171", "1150151867898503171", "1150151867898503171", "1150151867898503171", "1150151867898503171", "1150151867898503171", "1150151867898503171", "1150151867898503171", "1150151867898503171", "1150151867898503171", "1150151867898503171", "1150151867898503171", "1150151867898503171", "1150151867898503171", "1150151867898503171", "1150151867898503171", "1150151867898503171", "1150151867898503171", "1150151867898503171", "1154033397741699073", "1154033397741699073", "1154033397741699073", "1154033397741699073", "1154033397741699073", "1154033397741699073", "1154033397741699073", "1154033397741699073", "1154033397741699073", "1154033397741699073", "1154033397741699073", "1154033397741699073", "1154033397741699073", "1154033397741699073", "1154033397741699073", "1154033397741699073", "1154033397741699073", "1154033397741699073", "1154033397741699073", "1154033397741699073", "1154033397741699073", "1154033397741699073", "1154033397741699073", "1154033397741699073", "1154033397741699073", "1154033397741699073", "1154033397741699073", "1156893616587059200", "1156893616587059200", "1156893616587059200", "1156893616587059200", "1156893616587059200", "1156893616587059200", "1156893616587059200", "1156893616587059200", "1156893616587059200", "1156893616587059200", "1156893616587059200", "1156893616587059200", "1156893616587059200", "1156893616587059200", "1156893616587059200", "1156893616587059200", "1156893616587059200", "1156893616587059200", "1156893616587059200", "1156893616587059200", "1156893616587059200", "1156893616587059200", "1156893616587059200", "1156893616587059200", "1156893616587059200", "1156893616587059200", "1156893616587059200", "1161234269257097221", "1161234269257097221", "1161234269257097221", "1161234269257097221", "1161234269257097221", "1161234269257097221", "1161234269257097221", "1161234269257097221", "1161234269257097221", "1161234269257097221", "1161234269257097221", "1161234269257097221", "1161234269257097221", "1161234269257097221", "1161234269257097221", "1161234269257097221", "1161234269257097221", "1161234269257097221", "1161234269257097221", "1161234269257097221", "1161234269257097221", "1161234269257097221", "1161234269257097221", "1161234269257097221", "1161234269257097221", "1161234269257097221", "1161404522331344896", "1161404522331344896", "1161404522331344896", "1161404522331344896", "1161404522331344896", "1161404522331344896", "1161404522331344896", "1161404522331344896", "1161404522331344896", "1161404522331344896", "1161404522331344896", "1161404522331344896", "1161404522331344896", "1161404522331344896", "1161404522331344896", "1161404522331344896", "1161404522331344896", "1161404522331344896", "1161404522331344896", "1161404522331344896", "1161404522331344896", "1161404522331344896", "1161404522331344896", "1161404522331344896", "1161404522331344896", "1161404522331344896", "1161404522331344896", "1163489162630029312", "1163489162630029312", "1163489162630029312", "1163489162630029312", "1163489162630029312", "1163489162630029312", "1163489162630029312", "1163489162630029312", "1163489162630029312", "1163489162630029312", "1163489162630029312", "1163489162630029312", "1163489162630029312", "1163489162630029312", "1163489162630029312", "1163489162630029312", "1163489162630029312", "1163489162630029312", "1163489162630029312", "1163489162630029312", "1163489162630029312", "1163489162630029312", "1163489162630029312", "1163489162630029312", "1163489162630029312", "1163489162630029312", "1163489162630029312", "1168645153525026819", "1168645153525026819", "1168645153525026819", "1168645153525026819", "1168645153525026819", "1168645153525026819", "1168645153525026819", "1168645153525026819", "1168645153525026819", "1168645153525026819", "1168645153525026819", "1168645153525026819", "1168645153525026819", "1168645153525026819", "1168645153525026819", "1168645153525026819", "1168645153525026819", "1168645153525026819", "1168645153525026819", "1168645153525026819", "1168645153525026819", "1168645153525026819", "1168645153525026819", "1168645153525026819", "1168645153525026819", "1168645153525026819", "1168739286419681281", "1168739286419681281", "1168739286419681281", "1168739286419681281", "1168739286419681281", "1168739286419681281", "1168739286419681281", "1168739286419681281", "1168739286419681281", "1168739286419681281", "1168739286419681281", "1168739286419681281", "1168739286419681281", "1168739286419681281", "1168739286419681281", "1168739286419681281", "1168739286419681281", "1168739286419681281", "1168739286419681281", "1168739286419681281", "1168739286419681281", "1168739286419681281", "1168739286419681281", "1168739286419681281", "1168739286419681281", "1168739286419681281", "1171067948183822337", "1171067948183822337", "1171067948183822337", "1171067948183822337", "1171067948183822337", "1171067948183822337", "1171067948183822337", "1171067948183822337", "1171067948183822337", "1171067948183822337", "1171067948183822337", "1171067948183822337", "1173823641026465793", "1173823641026465793", "1173823641026465793", "1173823641026465793", "1173823641026465793", "1173823641026465793", "1173823641026465793", "1173823641026465793", "1173823641026465793", "1173823641026465793", "1173823641026465793", "1173823641026465793", "1173823641026465793", "1173823641026465793", "1173823641026465793", "1173823641026465793", "1173823641026465793", "1174376773775216640", "1174376773775216640", "1174376773775216640", "1174376773775216640", "1174376773775216640", "1174376773775216640", "1174376773775216640", "1174376773775216640", "1174376773775216640", "1174376773775216640", "1174376773775216640", "1174376773775216640", "1174376773775216640", "1174376773775216640", "1174376773775216640", "1174376773775216640", "1176845849629995009", "1176845849629995009", "1176845849629995009", "1176845849629995009", "1176845849629995009", "1176845849629995009", "1176845849629995009", "1176845849629995009", "1176845849629995009", "1176845849629995009", "1176845849629995009", "1176845849629995009", "1176845849629995009", "1176845849629995009", "1176845849629995009", "1176845849629995009", "1176845849629995009", "1176845849629995009", "1176845849629995009", "1176845849629995009", "1176845849629995009", "1176845849629995009", "1176845849629995009", "1176845849629995009", "1176845849629995009", "1176845849629995009", "1176845850909270016", "1176845850909270016", "1176845850909270016", "1176845850909270016", "1176845850909270016", "1176845850909270016", "1176845850909270016", "1176845850909270016", "1176845850909270016", "1176845850909270016", "1176845850909270016", "1176845850909270016", "1176845850909270016", "1176845850909270016", "1176845850909270016", "1176845850909270016", "1176845850909270016", "1176845850909270016", "1176845850909270016", "1176845850909270016", "1176845850909270016", "1176845850909270016", "1176845850909270016", "1176845850909270016", "1176845850909270016", "1176845850909270016", "1176908216984395777", "1176908216984395777", "1176908216984395777", "1176908216984395777", "1176908216984395777", "1176908216984395777", "1176908216984395777", "1176908216984395777", "1176908216984395777", "1176908216984395777", "1176908216984395777", "1176908216984395777", "1178406461795426304", "1178406461795426304", "1178406461795426304", "1178406461795426304", "1178406461795426304", "1178406461795426304", "1178406461795426304", "1178406461795426304", "1178406461795426304", "1178406461795426304", "1178406461795426304", "1178406461795426304", "1178406461795426304", "1178406461795426304", "1178406461795426304", "1178406461795426304", "1178406461795426304", "1178406461795426304", "1178406461795426304", "1178406461795426304", "1178406461795426304", "1178406461795426304", "1178406461795426304", "1178406461795426304", "1178406461795426304", "1178406461795426304", "1182555739388178433", "1182555739388178433", "1182555739388178433", "1182555739388178433", "1182555739388178433", "1182555739388178433", "1182555739388178433", "1182555739388178433", "1182555739388178433", "1182555739388178433", "1182555739388178433", "1182555739388178433", "1182555739388178433", "1182555739388178433", "1184156232355078147", "1184156232355078147", "1184156232355078147", "1184156232355078147", "1184156232355078147", "1184156232355078147", "1184156232355078147", "1184156232355078147", "1184156232355078147", "1184156232355078147", "1184156232355078147", "1184156232355078147", "1184156232355078147", "1184156232355078147", "1184156232355078147", "1184156232355078147", "1184156232355078147", "1184156232355078147", "1184156232355078147", "1184156232355078147", "1184156232355078147", "1184156232355078147", "1184156232355078147", "1184156232355078147", "1184156232355078147", "1186740530446766080", "1186740530446766080", "1186740530446766080", "1186740530446766080", "1186740530446766080", "1186740530446766080", "1186740530446766080", "1186740530446766080", "1186740530446766080", "1186740530446766080", "1186740530446766080", "1186740530446766080", "1186740530446766080", "1186740530446766080", "1186740530446766080", "1186740530446766080", "1186740530446766080", "1186740530446766080", "1186740530446766080", "1186740530446766080", "1186740530446766080", "1186740530446766080", "1186740530446766080", "1186740530446766080", "1186740530446766080", "1187213882604974081", "1187213882604974081", "1187213882604974081", "1187213882604974081", "1187213882604974081", "1187213882604974081", "1187213882604974081", "1190345576979804160", "1190345576979804160", "1190345576979804160", "1190345576979804160", "1190345576979804160", "1190345576979804160", "1190345576979804160", "1190345576979804160", "1190345576979804160", "1190345576979804160", "1190345576979804160", "1190345576979804160", "1190345576979804160", "1190345576979804160", "1190345576979804160", "1190345576979804160", "1190345576979804160", "1190345576979804160", "1190345576979804160", "1190345576979804160", "1190345576979804160", "1190345576979804160", "1190345576979804160", "1190345576979804160", "1191428460562046976", "1191428460562046976", "1191428460562046976", "1191428460562046976", "1191428460562046976", "1191428460562046976", "1191428460562046976", "1191428460562046976", "1191428460562046976", "1191428460562046976", "1191428460562046976", "1191428460562046976", "1191428460562046976", "1191428460562046976", "1191428460562046976", "1191428460562046976", "1191428460562046976", "1191428460562046976", "1191428460562046976", "1191428460562046976", "1191428460562046976", "1191428460562046976", "1191428460562046976", "1191428460562046976", "1191428460562046976", "1192515272810348545", "1192515272810348545", "1192515272810348545", "1192515272810348545", "1192515272810348545", "1192515272810348545", "1192515272810348545", "1192515272810348545", "1192515272810348545", "1192515272810348545", "1192515272810348545", "1192515272810348545", "1192515272810348545", "1192515272810348545", "1192515272810348545", "1192515272810348545", "1192515272810348545", "1192515272810348545", "1192515272810348545", "1192515272810348545", "1192515272810348545", "1192515272810348545", "1192515272810348545", "1192515272810348545", "1192515272810348545", "1193898621827108864", "1193898621827108864", "1193898621827108864", "1193898621827108864", "1193898621827108864", "1193898621827108864", "1193898621827108864", "1193898621827108864", "1193898621827108864", "1193898621827108864", "1193898621827108864", "1193898621827108864", "1193898621827108864", "1193898621827108864", "1193898621827108864", "1193898621827108864", "1193898621827108864", "1193898621827108864", "1193898621827108864", "1193898621827108864", "1193898621827108864", "1193898621827108864", "1193898621827108864", "1193898621827108864", "1193898621827108864", "1198224190479970306", "1198224190479970306", "1198224190479970306", "1198224190479970306", "1198224190479970306", "1198224190479970306", "1198224190479970306", "1198224190479970306", "1198224190479970306", "1198224190479970306", "1198224190479970306", "1198224190479970306", "1198224190479970306", "1198224190479970306", "1198224190479970306", "1198224190479970306", "1198224190479970306", "1198224190479970306", "1198224190479970306", "1198224190479970306", "1198224190479970306", "1198224190479970306", "1198224190479970306", "1198355347188125696", "1198355347188125696", "1198355347188125696", "1198355347188125696", "1198355347188125696", "1198355347188125696", "1198355347188125696", "1198355347188125696", "1198355347188125696", "1198355347188125696", "1200344290683211777", "1200344290683211777", "1200344290683211777", "1200344290683211777", "1200344290683211777", "1200344290683211777", "1200344290683211777", "1200344290683211777", "1200344290683211777", "1200344290683211777", "1200344290683211777", "1200344290683211777", "1200344290683211777", "1200344290683211777", "1200344290683211777", "1200344290683211777", "1200344290683211777", "1200344290683211777", "1200344290683211777", "1200344290683211777", "1200344290683211777", "1200344290683211777", "1200344290683211777", "1200344290683211777", "1202835269856067584", "1202835269856067584", "1202835269856067584", "1202835269856067584", "1202835269856067584", "1202835269856067584", "1202835269856067584", "1202835269856067584", "1202835269856067584", "1202835269856067584", "1202835269856067584", "1202835269856067584", "1202835269856067584", "1202835269856067584", "1202835269856067584", "1202835269856067584", "1202835269856067584", "1202835269856067584", "1202835269856067584", "1202835269856067584", "1202835269856067584", "1202835269856067584", "1202835269856067584", "1202835269856067584", "1206279201021558784", "1206279201021558784", "1206279201021558784", "1206279201021558784", "1206279201021558784", "1206279201021558784", "1206279201021558784", "1206279201021558784", "1206279201021558784", "1206279201021558784", "1206279201021558784", "1206279201021558784", "1206279201021558784", "1206279201021558784", "1206279201021558784", "1206279201021558784", "1206279201021558784", "1206279201021558784", "1206279201021558784", "1206279201021558784", "1206279201021558784", "1206279201021558784", "1206279201021558784", "1206279201021558784", "1206630761371095040", "1206630761371095040", "1206630761371095040", "1206630761371095040", "1206630761371095040", "1206630761371095040", "1206630761371095040", "1206630761371095040", "1206630761371095040", "1206630761371095040", "1206630761371095040", "1206630761371095040", "1206630761371095040", "1206630761371095040", "1206630761371095040", "1206630761371095040", "1206630761371095040", "1206630761371095040", "1206630761371095040", "1206630761371095040", "1206630761371095040", "1206630761371095040", "1206630761371095040", "1206630761371095040", "1208802132930883590", "1208802132930883590", "1208802132930883590", "1208802132930883590", "1208802132930883590", "1208802132930883590", "1208802132930883590", "1208802132930883590", "1208802132930883590", "1208802132930883590", "1208802132930883590", "1208802132930883590", "1208802132930883590", "1208802132930883590", "1208802132930883590", "1208802132930883590", "1208802132930883590", "1208802132930883590", "1208802132930883590", "1208802132930883590", "1208802132930883590", "1208802132930883590", "1208802132930883590", "1210753033404960770", "1210753033404960770", "1210753033404960770", "1210753033404960770", "1210753033404960770", "1210753033404960770", "1210753033404960770", "1210753033404960770", "1210753033404960770", "1210753033404960770", "1210753033404960770", "1210753033404960770", "1210753033404960770", "1210753033404960770", "1210753033404960770", "1210753033404960770", "1210753033404960770", "1210753033404960770", "1210753033404960770", "1210753033404960770", "1210753033404960770", "1210753033404960770", "1210753033404960770", "1210753033404960770", "1211006304598319106", "1211006304598319106", "1211006304598319106", "1211006304598319106", "1211006304598319106", "1211006304598319106", "1211006304598319106", "1211006304598319106", "1211006304598319106", "1211006304598319106", "1211006304598319106", "1211006304598319106", "1211006304598319106", "1211006304598319106", "1211006304598319106", "1211006304598319106", "1211006304598319106", "1211006304598319106", "1211006304598319106", "1211006304598319106", "1211006304598319106", "1211006304598319106", "1211006304598319106", "1211006304598319106", "1220654109466005504", "1220654109466005504", "1220654109466005504", "1230105281079570433", "1230105281079570433", "1230105281079570433", "1230105281079570433", "1230105281079570433", "1230105281079570433", "1230105281079570433", "1230105281079570433", "1230105281079570433", "1230105281079570433", "1230105281079570433", "1230105281079570433", "1230105281079570433", "1230105281079570433", "1230105281079570433", "1230105281079570433", "1230105281079570433", "1230105281079570433", "1230105281079570433", "1230105281079570433", "1230105281079570433", "1230105281079570433", "1230105281079570433", "1240347595106549762", "1240347595106549762", "1240347595106549762", "1240347595106549762", "1240347595106549762", "1240347595106549762", "1240347595106549762", "1240347595106549762", "1240347595106549762", "1254050448711057410", "1254050448711057410", "1254050448711057410", "1254050448711057410", "1254050448711057410", "1254050448711057410", "1254050448711057410", "1254050448711057410", "1254050448711057410", "1254050448711057410", "1254050448711057410", "1254050448711057410", "1254050448711057410", "1254050448711057410", "1254050448711057410", "1254050448711057410", "1254050448711057410", "1254050448711057410", "1254050448711057410", "1254050448711057410", "1254050448711057410", "1254050448711057410", "1254507503523713024", "1254507503523713024", "1254507503523713024", "1254507503523713024", "1254507503523713024", "1254507503523713024", "1254507503523713024", "1254507503523713024", "1254507503523713024", "1254507503523713024", "1254507503523713024", "1254507503523713024", "1254507503523713024", "1254507503523713024", "1254507503523713024", "1254507503523713024", "1254507503523713024", "1263809734618087426", "1263809734618087426", "1263809734618087426", "1263809734618087426", "1263809734618087426", "1263809734618087426", "1263809734618087426", "1263809734618087426", "1263809734618087426", "1263809734618087426", "1263809734618087426", "1263809734618087426", "1263809734618087426", "1263809734618087426", "1263809734618087426", "1263809734618087426", "1263809734618087426", "1263809734618087426", "1263809734618087426", "1263809734618087426", "1265483236433694720", "1265483236433694720", "1265483236433694720", "1265483236433694720", "1265483236433694720", "1265483236433694720", "1265483236433694720", "1265483236433694720", "1265483236433694720", "1265483236433694720", "1265483236433694720", "1265483236433694720", "1265483236433694720", "1265483236433694720", "1265483236433694720", "1265483236433694720", "1265483236433694720", "1265483236433694720", "1265483236433694720", "1265483236433694720", "1268317768182575104", "1270126166884696067", "1270126166884696067", "1270126166884696067", "1270126166884696067", "1270126166884696067", "1270126166884696067", "1270126166884696067", "1270126166884696067", "1270126166884696067", "1270126166884696067", "1270126166884696067", "1270126166884696067", "1270126166884696067", "1270126166884696067", "1270126166884696067", "1270126166884696067", "1270126166884696067", "1270126166884696067", "1270126166884696067", "1270126166884696067", "1270342191198674946", "1270342191198674946", "1270342191198674946", "1270342191198674946", "1270342191198674946", "1270342191198674946", "1270342191198674946", "1270342191198674946", "1270342191198674946", "1270342191198674946", "1270342191198674946", "1270342191198674946", "1270342191198674946", "1270342191198674946", "1270342191198674946", "1270342191198674946", "1270342191198674946", "1270342191198674946", "1270342191198674946", "1272657987379830784", "1272657987379830784", "1272657987379830784", "1272657987379830784", "1272657987379830784", "1272657987379830784", "1272657987379830784", "1272657987379830784", "1272657987379830784", "1272657987379830784", "1272657987379830784", "1272657987379830784", "1272657987379830784", "1272657987379830784", "1272657987379830784", "1272657987379830784", "1272657987379830784", "1272657987379830784", "1272657987379830784", "1273003149956014080", "1273003149956014080", "1273003149956014080", "1273003149956014080", "1273003149956014080", "1273003149956014080", "1273003149956014080", "1273003149956014080", "1273003149956014080", "1273003149956014080", "1273003149956014080", "1273003149956014080", "1273003149956014080", "1273003149956014080", "1273003149956014080", "1273003149956014080", "1273003149956014080", "1273003149956014080", "1273003149956014080", "1273557899667738633", "1273557899667738633", "1273557899667738633", "1273557899667738633", "1273557899667738633", "1273557899667738633", "1273557899667738633", "1273557899667738633", "1273557899667738633", "1273557899667738633", "1273557899667738633", "1273557899667738633", "1273557899667738633", "1273557899667738633", "1273557899667738633", "1273557899667738633", "1273557899667738633", "1273557899667738633", "1273557899667738633", "1273557899667738633", "1275183515018158082", "1275183515018158082", "1275183515018158082", "1275183515018158082", "1275183515018158082", "1275183515018158082", "1275183515018158082", "1275183515018158082", "1275183515018158082", "1275183515018158082", "1275183515018158082", "1275183515018158082", "1275183515018158082", "1275183515018158082", "1275183515018158082", "1275183515018158082", "1275183515018158082", "1275183515018158082", "1275183515018158082", "1275880289282203655", "1275880289282203655", "1275880289282203655", "1275880289282203655", "1275880289282203655", "1275880289282203655", "1275880289282203655", "1275880289282203655", "1275880289282203655", "1275880289282203655", "1275880289282203655", "1275880289282203655", "1275880289282203655", "1275880289282203655", "1275880289282203655", "1275880289282203655", "1275880289282203655", "1275880289282203655", "1275880289282203655", "1276522063184965632", "1276522063184965632", "1276522063184965632", "1276522063184965632", "1276522063184965632", "1276522063184965632", "1276522063184965632", "1276522063184965632", "1276522063184965632", "1276522063184965632", "1276522063184965632", "1276522063184965632", "1276522063184965632", "1276522063184965632", "1276522063184965632", "1276522063184965632", "1276522063184965632", "1276522063184965632", "1276522063184965632", "1276802746377662464", "1276802746377662464", "1276802746377662464", "1276802746377662464", "1276802746377662464", "1276802746377662464", "1276802746377662464", "1276802746377662464", "1276802746377662464", "1276802746377662464", "1276802746377662464", "1276802746377662464", "1276802746377662464", "1276802746377662464", "1276802746377662464", "1276802746377662464", "1276802746377662464", "1276802746377662464", "1276802746377662464", "1276802746377662464", "1277584206084935685", "1277584206084935685", "1277584206084935685", "1277584206084935685", "1277584206084935685", "1277584206084935685", "1277584206084935685", "1277584206084935685", "1277584206084935685", "1277584206084935685", "1277584206084935685", "1277584206084935685", "1277584206084935685", "1277584206084935685", "1277584206084935685", "1277584206084935685", "1277584206084935685", "1277584206084935685", "1277584206084935685", "1281269805312610304", "1281269805312610304", "1281269805312610304", "1281269805312610304", "1281269805312610304", "1281269805312610304", "1281269805312610304", "1281269805312610304", "1281269805312610304", "1281269805312610304", "1281269805312610304", "1281269805312610304", "1281269805312610304", "1281269805312610304", "1281269805312610304", "1281269805312610304", "1281269805312610304", "1281269805312610304", "1284148855613927424", "1284148855613927424", "1284148855613927424", "1284148855613927424", "1284148855613927424", "1284148855613927424", "1284148855613927424", "1284485587735662592", "1284485587735662592", "1284485587735662592", "1284485587735662592", "1284485587735662592", "1284485587735662592", "1284485587735662592", "1284485587735662592", "1284485587735662592", "1284485587735662592", "1284485587735662592", "1284485587735662592", "1284485587735662592", "1284485587735662592", "1284485587735662592", "1284485587735662592", "1284485587735662592", "1284485587735662592", "1284499278501732352", "1284499278501732352", "1284499278501732352", "1284499278501732352", "1284499278501732352", "1284499278501732352", "1284499278501732352", "1284499278501732352", "1284499278501732352", "1284499278501732352", "1284499278501732352", "1284499278501732352", "1284499278501732352", "1284499278501732352", "1284499278501732352", "1284499278501732352", "1284499278501732352", "1284499278501732352", "1284567124967661568", "1284567124967661568", "1284567124967661568", "1284567124967661568", "1284567124967661568", "1284567124967661568", "1284567124967661568", "1284567124967661568", "1284567124967661568", "1284567124967661568", "1284567124967661568", "1284567124967661568", "1284567124967661568", "1284567124967661568", "1284567124967661568", "1284567124967661568", "1284567124967661568", "1284567124967661568", "1285282802414637056", "1285282802414637056", "1285282802414637056", "1285282802414637056", "1285282802414637056", "1285282802414637056", "1285282802414637056", "1285282802414637056", "1285282802414637056", "1285282802414637056", "1285282802414637056", "1285282802414637056", "1285282802414637056", "1285282802414637056", "1285282802414637056", "1285282802414637056", "1285282802414637056", "1285282802414637056", "1285282802414637056", "1285345859803033602", "1285345859803033602", "1285345859803033602", "1285345859803033602", "1285345859803033602", "1285345859803033602", "1285345859803033602", "1285345859803033602", "1285345859803033602", "1285345859803033602", "1285345859803033602", "1285345859803033602", "1285345859803033602", "1285345859803033602", "1285771458715586561", "1285771458715586561", "1285771458715586561", "1285771458715586561", "1285771458715586561", "1285771458715586561", "1285771458715586561", "1285771458715586561", "1285771458715586561", "1285771458715586561", "1285771458715586561", "1285771458715586561", "1285771458715586561", "1285771458715586561", "1285771458715586561", "1285771458715586561", "1285771458715586561", "1285771458715586561", "1285916056989560832", "1285916056989560832", "1285916056989560832", "1285916056989560832", "1285916056989560832", "1285916056989560832", "1285916056989560832", "1285916056989560832", "1285916056989560832", "1285916056989560832", "1285916056989560832", "1285916056989560832", "1285916056989560832", "1285916056989560832", "1285916056989560832", "1285916056989560832", "1285916056989560832", "1285916056989560832", "1285919746001711106", "1285919746001711106", "1285919746001711106", "1285919746001711106", "1285919746001711106", "1285919746001711106", "1285919746001711106", "1285919746001711106", "1285919746001711106", "1285919746001711106", "1285919746001711106", "1285919746001711106", "1285919746001711106", "1285919746001711106", "1285919746001711106", "1285919746001711106", "1285919746001711106", "1285919746001711106", "1295224277088649216", "1295224277088649216", "1295224277088649216", "1295224277088649216", "1295224277088649216", "1295224277088649216", "1295224277088649216", "1295224277088649216", "1295224277088649216", "1295224277088649216", "1295224277088649216", "1295224277088649216", "1295224277088649216", "1295224277088649216", "1295224277088649216", "1295224277088649216", "1295224277088649216", "1295224277088649216", "1296058474216878080", "1296058474216878080", "1296058474216878080", "1296058474216878080", "1296058474216878080", "1296058474216878080", "1296058474216878080", "1296058474216878080", "1296058474216878080", "1296058474216878080", "1296058474216878080", "1296058474216878080", "1296058474216878080", "1296058474216878080", "1296058474216878080", "1296058474216878080", "1296058474216878080", "1304620948570734598", "1304620948570734598", "1304620948570734598", "1304620948570734598", "1304620948570734598", "1304620948570734598", "1304620948570734598", "1304620948570734598", "1304620948570734598", "1304620948570734598", "1304620948570734598", "1304620948570734598", "1304620948570734598", "1304620948570734598", "1304620948570734598", "1304620948570734598", "1304620948570734598", "1306498446602379265", "1306498446602379265", "1306498446602379265", "1306498446602379265", "1306498446602379265", "1306498446602379265", "1306498446602379265", "1306498446602379265", "1306498446602379265", "1306498446602379265", "1306498446602379265", "1306498446602379265", "1306498446602379265", "1306498446602379265", "1306498446602379265", "1306498446602379265", "1306498446602379265", "1310481068886118400", "1310481068886118400", "1310481068886118400", "1310481068886118400", "1310481068886118400", "1310481068886118400", "1310481068886118400", "1310481068886118400", "1310481068886118400", "1310481068886118400", "1310481068886118400", "1310481068886118400", "1310481068886118400", "1310481068886118400", "1310481068886118400", "1310481068886118400", "1310481068886118400", "1310481068886118400", "1312956419117854720", "1312956419117854720", "1312956419117854720", "1312956419117854720", "1312956419117854720", "1312956419117854720", "1312956419117854720", "1312956419117854720", "1312956419117854720", "1312956419117854720", "1312956419117854720", "1312956419117854720", "1312956419117854720", "1312956419117854720", "1312956419117854720", "1312956419117854720", "1313236270617972740", "1313236270617972740", "1313236270617972740", "1313236270617972740", "1313236270617972740", "1313236270617972740", "1313236270617972740", "1313236270617972740", "1313236270617972740", "1313236270617972740", "1313236270617972740", "1313236270617972740", "1313236270617972740", "1313236270617972740", "1313236270617972740", "1313236270617972740", "1313236270617972740", "1314920369514573824", "1314920369514573824", "1314920369514573824", "1314920369514573824", "1315342004084244482", "1315342004084244482", "1315342004084244482", "1315342004084244482", "1315342004084244482", "1315342004084244482", "1315342004084244482", "1315342004084244482", "1315342004084244482", "1315342004084244482", "1315342004084244482", "1315342004084244482", "1315342004084244482", "1315342004084244482", "1315342004084244482", "1316421668483600385", "1316421668483600385", "1316421668483600385", "1316421668483600385", "1316421668483600385", "1316421668483600385", "1316421668483600385", "1316421668483600385", "1316421668483600385", "1316421668483600385", "1316421668483600385", "1316421668483600385", "1316421668483600385", "1316421668483600385", "1316421668483600385", "1321261574330314754", "1321261574330314754", "1321261574330314754", "1321261574330314754", "1321261574330314754", "1321261574330314754", "1321261574330314754", "1321261574330314754", "1321261574330314754", "1321261574330314754", "1321261574330314754", "1321261574330314754", "1321261574330314754", "1321261574330314754", "1321261574330314754", "1322651648712347648", "1322651648712347648", "1322651648712347648", "1322651648712347648", "1322651648712347648", "1322651648712347648", "1322651648712347648", "1322651648712347648", "1322651648712347648", "1322651648712347648", "1322651648712347648", "1322651648712347648", "1322651648712347648", "1322651648712347648", "1322651648712347648", "1323215333197516808", "1323215333197516808", "1323215333197516808", "1323215333197516808", "1323215333197516808", "1323215333197516808", "1323215333197516808", "1323215333197516808", "1323215333197516808", "1323215333197516808", "1323215333197516808", "1323215333197516808", "1323215333197516808", "1323215333197516808", "1323215333197516808", "1324667303263424513", "1324667303263424513", "1324667303263424513", "1324667303263424513", "1324667303263424513", "1324667303263424513", "1324667303263424513", "1324667303263424513", "1324667303263424513", "1324667303263424513", "1324667303263424513", "1324667303263424513", "1324667303263424513", "1324667303263424513", "1325028695879376896", "1325028695879376896", "1325028695879376896", "1325028695879376896", "1325028695879376896", "1325028695879376896", "1325028695879376896", "1325028695879376896", "1325028695879376896", "1325028695879376896", "1325028695879376896", "1325028695879376896", "1325028695879376896", "1325028695879376896", "1334994971426959360", "1334994971426959360", "1334994971426959360", "1334994971426959360", "1334994971426959360", "1334994971426959360", "1334994971426959360", "1334994971426959360", "1334994971426959360", "1334994971426959360", "1334994971426959360", "1334994971426959360", "1334994971426959360", "1339242817919590404", "1339242817919590404", "1339242817919590404", "1339242817919590404", "1339242817919590404", "1339242817919590404", "1339242817919590404", "1339242817919590404", "1339242817919590404", "1339242817919590404", "1339242817919590404", "1339242817919590404", "1339242817919590404", "1339962805307518976", "1339962805307518976", "1339962805307518976", "1339962805307518976", "1339962805307518976", "1339962805307518976", "1339962805307518976", "1339962805307518976", "1339962805307518976", "1339962805307518976", "1339962805307518976", "1339962805307518976", "1339962805307518976", "1342294345404264449", "1342294345404264449", "1342581917774962690", "1342581917774962690", "1342581917774962690", "1342581917774962690", "1342581917774962690", "1342581917774962690", "1342581917774962690", "1342581917774962690", "1342581917774962690", "1342581917774962690", "1342581917774962690", "1342581917774962690", "1342581917774962690", "1344604032661409792", "1344604032661409792", "1344604032661409792", "1344604032661409792", "1344604032661409792", "1344604032661409792", "1344604032661409792", "1344604032661409792", "1344604032661409792", "1344604032661409792", "1344604032661409792", "1344604032661409792", "1344604032661409792", "1345754590508027905", "1345754590508027905", "1345754590508027905", "1345754590508027905", "1345754590508027905", "1345754590508027905", "1345754590508027905", "1345754590508027905", "1345754590508027905", "1345754590508027905", "1345754590508027905", "1345754590508027905", "1345754590508027905", "1347589796194037764", "1347589796194037764", "1347589796194037764", "1347589796194037764", "1347589796194037764", "1347589796194037764", "1347589796194037764", "1347589796194037764", "1347589796194037764", "1347589796194037764", "1347589796194037764", "1347589796194037764", "1347589796194037764", "1347593829692526592", "1347593829692526592", "1347593829692526592", "1347593829692526592", "1347593829692526592", "1347593829692526592", "1347593829692526592", "1347593829692526592", "1347593829692526592", "1347593829692526592", "1347593829692526592", "1347593829692526592", "1347593829692526592", "1349398146154176520", "1349398146154176520", "1349398146154176520", "1349398146154176520", "1349398146154176520", "1349398146154176520", "1349398146154176520", "1349398146154176520", "1349398146154176520", "1349398146154176520", "1349398146154176520", "1349398146154176520", "1350229753287081991", "1350229753287081991", "1350229753287081991", "1350229753287081991", "1350229753287081991", "1350229753287081991", "1350229753287081991", "1350229753287081991", "1350229753287081991", "1350229753287081991", "1350229753287081991", "1350229753287081991", "1350229753287081991", "1350347522091479041", "1350347522091479041", "1350347522091479041", "1350347522091479041", "1350347522091479041", "1350347522091479041", "1350347522091479041", "1350347522091479041", "1350347522091479041", "1350347522091479041", "1350347522091479041", "1350347522091479041", "1350347522091479041", "1353035507253571584", "1353035507253571584", "1353035507253571584", "1353035507253571584", "1353035507253571584", "1353035507253571584", "1353035507253571584", "1353035507253571584", "1353035507253571584", "1353035507253571584", "1353076927423123458", "1353076927423123458", "1353076927423123458", "1353076927423123458", "1353076927423123458", "1353076927423123458", "1353076927423123458", "1353076927423123458", "1353076927423123458", "1353076927423123458", "1353076927423123458", "1353076927423123458", "1353436029986992130", "1353436029986992130", "1353436029986992130", "1353436029986992130", "1353436029986992130", "1353436029986992130", "1353436029986992130", "1353436029986992130", "1353436029986992130", "1353436029986992130", "1353436029986992130", "1353436029986992130", "1353619424075370499", "1353619424075370499", "1353619424075370499", "1353619424075370499", "1353619424075370499", "1353619424075370499", "1353619424075370499", "1353619424075370499", "1353619424075370499", "1353619424075370499", "1353619424075370499", "1353619424075370499", "1355383014981840899", "1355383014981840899", "1355383014981840899", "1355383014981840899", "1355383014981840899", "1355383014981840899", "1355383014981840899", "1355383014981840899", "1355383014981840899", "1355383014981840899", "1356178993045770241", "1356178993045770241", "1356178993045770241", "1356178993045770241", "1356178993045770241", "1356178993045770241", "1356178993045770241", "1356178993045770241", "1356178993045770241", "1356178993045770241", "1356178993045770241", "1356178993045770241", "1356889558265257985", "1356889558265257985", "1356889558265257985", "1356889558265257985", "1356889558265257985", "1356889558265257985", "1356889558265257985", "1356889558265257985", "1356889558265257985", "1356889558265257985", "1356889558265257985", "1356889558265257985", "1364593055081226243", "1364593055081226243", "1364593055081226243", "1364593055081226243", "1364593055081226243", "1364593055081226243", "1364593055081226243", "1364593055081226243", "1364593055081226243", "1364593055081226243", "1364593055081226243", "1364593055081226243", "1365014446997975042", "1365014446997975042", "1365014446997975042", "1365014446997975042", "1365014446997975042", "1365014446997975042", "1365014446997975042", "1365014446997975042", "1365014446997975042", "1365014446997975042", "1365014446997975042", "1365014446997975042", "1365045003354046469", "1365045003354046469", "1365045003354046469", "1365045003354046469", "1365045003354046469", "1365045003354046469", "1365045003354046469", "1365045003354046469", "1365045003354046469", "1365045003354046469", "1365045003354046469", "1365045003354046469", "1365312388099149830", "1365312388099149830", "1365312388099149830", "1365312388099149830", "1365312388099149830", "1365312388099149830", "1365312388099149830", "1365312388099149830", "1365312388099149830", "1365312388099149830", "1365312388099149830", "1365312388099149830", "1374216837127372801", "1374216837127372801", "1374216837127372801", "1374216837127372801", "1374216837127372801", "1374216837127372801", "1374216837127372801", "1374216837127372801", "1374216837127372801", "1374216837127372801", "1374216837127372801", "1374216837127372801", "1379802811991347203", "1379802811991347203", "1379802811991347203", "1379802811991347203", "1379802811991347203", "1379802811991347203", "1379802811991347203", "1379802811991347203", "1379802811991347203", "1379802811991347203", "1379802811991347203", "1379802811991347203", "1380020310150410240", "1380020310150410240", "1380020310150410240", "1380020310150410240", "1380020310150410240", "1380020310150410240", "1380020310150410240", "1380020310150410240", "1380020310150410240", "1380020310150410240", "1380020310150410240", "1380020310150410240", "1380022718683357184", "1380022718683357184", "1380022718683357184", "1380022718683357184", "1380022718683357184", "1380022718683357184", "1380022718683357184", "1380022718683357184", "1380022718683357184", "1380022718683357184", "1380022718683357184", "1380022718683357184", "1386393448912023552", "1386393448912023552", "1386393448912023552", "1386393448912023552", "1386393448912023552", "1386393448912023552", "1386393448912023552", "1386393448912023552", "1386393448912023552", "1386393448912023552", "1386393448912023552", "1386393448912023552", "1387025227880742915", "1387025227880742915", "1387025227880742915", "1387025227880742915", "1387025227880742915", "1387025227880742915", "1387025227880742915", "1387025227880742915", "1387025227880742915", "1387025227880742915", "1387025227880742915", "1387025227880742915", "1387445105527754754", "1387445105527754754", "1387445105527754754", "1387445105527754754", "1387445105527754754", "1387445105527754754", "1387445105527754754", "1387445105527754754", "1387445105527754754", "1387445105527754754", "1387445105527754754", "1387445105527754754", "1388186889195294724", "1388186889195294724", "1388186889195294724", "1388186889195294724", "1388186889195294724", "1388186889195294724", "1388186889195294724", "1388186889195294724", "1388186889195294724", "1388186889195294724", "1388186889195294724", "1388186889195294724", "1390275618898591752", "1390275618898591752", "1390275618898591752", "1390275618898591752", "1390275618898591752", "1390275618898591752", "1390275618898591752", "1390275618898591752", "1390275618898591752", "1390275618898591752", "1391122604208046080", "1391122604208046080", "1391122604208046080", "1391122604208046080", "1391122604208046080", "1391122604208046080", "1391122604208046080", "1391122604208046080", "1391122604208046080", "1391122604208046080", "1391535044049653763", "1391535044049653763", "1391535044049653763", "1391535044049653763", "1391535044049653763", "1391535044049653763", "1391535044049653763", "1391535044049653763", "1391535044049653763", "1391535044049653763", "1391698488602382336", "1391698488602382336", "1391698488602382336", "1391698488602382336", "1391698488602382336", "1391698488602382336", "1391698488602382336", "1391698488602382336", "1391698488602382336", "1391698488602382336", "1392723170679943168", "1392723170679943168", "1392723170679943168", "1392723170679943168", "1392723170679943168", "1392723170679943168", "1392723170679943168", "1392723170679943168", "1392723170679943168", "1392723170679943168", "1394824550219587585", "1394824550219587585", "1394824550219587585", "1394824550219587585", "1394824550219587585", "1394824550219587585", "1394824550219587585", "1394824550219587585", "1394824550219587585", "1394824550219587585", "1396070337117265923", "1396070337117265923", "1396070337117265923", "1396070337117265923", "1396070337117265923", "1396070337117265923", "1396070337117265923", "1396070337117265923", "1396070337117265923", "1396070337117265923", "1396989847546564609", "1396989847546564609", "1396989847546564609", "1396989847546564609", "1396989847546564609", "1396989847546564609", "1396989847546564609", "1396989847546564609", "1396989847546564609", "1396989847546564609", "1397619766915997716", "1397619766915997716", "1397619766915997716", "1397619766915997716", "1397619766915997716", "1397619766915997716", "1397619766915997716", "1397619766915997716", "1397619766915997716", "1397619766915997716", "1402291415200276483", "1402291415200276483", "1402291415200276483", "1402291415200276483", "1402291415200276483", "1402291415200276483", "1402291415200276483", "1402291415200276483", "1402291415200276483", "1402375562828652544", "1402375562828652544", "1402375562828652544", "1402375562828652544", "1402375562828652544", "1402375562828652544", "1402375562828652544", "1402375562828652544", "1402375562828652544", "1402618985661698048", "1402618985661698048", "1402618985661698048", "1402618985661698048", "1402618985661698048", "1402618985661698048", "1402618985661698048", "1402618985661698048", "1402618985661698048", "1404866267740258306", "1404866267740258306", "1404866267740258306", "1404866267740258306", "1404866267740258306", "1404866267740258306", "1404866267740258306", "1404866267740258306", "1404866267740258306", "1406666124905762818", "1406666124905762818", "1406666124905762818", "1406666124905762818", "1406666124905762818", "1406666124905762818", "1406666124905762818", "1406666124905762818", "1406666124905762818", "1406972663034228741", "1406972663034228741", "1406972663034228741", "1406972663034228741", "1406972663034228741", "1406972663034228741", "1406972663034228741", "1406972663034228741", "1406972663034228741", "1408337294017191936", "1408337294017191936", "1408337294017191936", "1408337294017191936", "1408337294017191936", "1408337294017191936", "1408337294017191936", "1408337294017191936", "1408337294017191936", "1413115316528324621", "1413115316528324621", "1413115316528324621", "1413115316528324621", "1413115316528324621", "1413115316528324621", "1413115316528324621", "1413115316528324621", "1413229777167818756", "1413229777167818756", "1413229777167818756", "1413229777167818756", "1413229777167818756", "1413229777167818756", "1413229777167818756", "1413229777167818756", "1413247782308229125", "1413247782308229125", "1413247782308229125", "1413247782308229125", "1413247782308229125", "1413247782308229125", "1413247782308229125", "1413247782308229125", "1417301250887102466", "1417301250887102466", "1417301250887102466", "1417301250887102466", "1417301250887102466", "1417301250887102466", "1417301250887102466", "1417301250887102466", "1417500321753010180", "1417500321753010180", "1417500321753010180", "1417500321753010180", "1417500321753010180", "1417500321753010180", "1417500321753010180", "1418358227499028482", "1418358227499028482", "1418358227499028482", "1418358227499028482", "1418358227499028482", "1418358227499028482", "1418358227499028482", "1422394818320166915", "1422394818320166915", "1422394818320166915", "1422394818320166915", "1422394818320166915", "1422394818320166915", "1425074438995791874", "1425074438995791874", "1425074438995791874", "1425074438995791874", "1425074438995791874", "1425074438995791874", "1431066442464645122", "1431066442464645122", "1431066442464645122", "1431066442464645122", "1431066442464645122", "1431066442464645122", "1433801542818168834", "1433801542818168834", "1433801542818168834", "1433801542818168834", "1433801542818168834", "1433801542818168834", "1446384291781488649", "1446384291781488649", "1446384291781488649", "1446384291781488649", "1446384291781488649", "1446384291781488649", "1455646795158204423", "1455646795158204423", "1455646795158204423", "1455646795158204423", "1455646795158204423", "1460086640303648768", "1460086640303648768", "1460086640303648768", "1460086640303648768", "1461104526816129024", "1461104526816129024", "1461104526816129024", "1461104526816129024", "1462024034972020742", "1462024034972020742", "1462024034972020742", "1462024034972020742", "1471178177569443840", "1471178177569443840", "1471178177569443840", "1471456919445377026", "1471456919445377026", "1471456919445377026", "1472281183551639563", "1472281183551639563", "1472281183551639563", "1472509235405725699", "1472509235405725699", "1472509235405725699", "1472906659001475072", "1472906659001475072", "1472906659001475072", "1473128114331045893", "1473128114331045893", "1473128114331045893", "1473228760140951559", "1473228760140951559", "1473228760140951559", "1474197146224234502", "1474197146224234502", "1474197146224234502", "1483496364638146560", "1483496364638146560", "1483496364638146560", "1485712571080585222", "1485712571080585222", "1485712571080585222", "1486757889863651328", "1486757889863651328", "1486757889863651328", "1490752836019310594", "1490752836019310594", "1494550520794652673", "1494550520794652673", "1494749634006331392", "1494749634006331392", "1496753247767482369", "1496753247767482369", "1498088826761199616", "1498088826761199616", "1501192708131598337", "1501192708131598337", "1501482882434539521", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "779966186121560064", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "783630950718504960", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "808825432233558016", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "819710615337857024", "824053457527209984", "824053457527209984", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "831866770755579904", "834030605847326726", "834030605847326726", "834030605847326726", "834030605847326726", "834030605847326726", "834030605847326726", "834030605847326726", "834030605847326726", "834030605847326726", "834030605847326726", "834030605847326726", "834030605847326726", "834030605847326726", "834030605847326726", "834030605847326726", "834030605847326726", "834030605847326726", "834030605847326726", "834030605847326726", "834030605847326726", "834030605847326726", "834030605847326726", "834140257054502913", "834140257054502913", "834140257054502913", "834140257054502913", "834140257054502913", "834140257054502913", "834140257054502913", "834140257054502913", "834140257054502913", "834140257054502913", "834140257054502913", "834140257054502913", "834140257054502913", "834140257054502913", "834140257054502913", "834140257054502913", "834140257054502913", "834140257054502913", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "835834634655174658", "839423865205977088", "839423865205977088", "839423865205977088", "839423865205977088", "839423865205977088", "839423865205977088", "839423865205977088", "839423865205977088", "839423865205977088", "839423865205977088", "839423865205977088", "839423865205977088", "839423865205977088", "839423865205977088", "839423865205977088", "839423865205977088", "839423865205977088", "839423865205977088", "839423865205977088", "839423865205977088", "839423865205977088", "839423865205977088", "839423865205977088", "839423865205977088", "839423865205977088", "839423865205977088", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "857590406724243456", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "860885116909998080", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "862586112770023425", "868730725926711296", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "868829487072464897", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "869284735440363520", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871163877622460417", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "871410394622865408", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874200312378269696", "874925154475929602", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "875511410008219649", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877010184463294465", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877691760226562048", "877764422466322432", "877764422466322432", "877764422466322432", "877764422466322432", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "878220062007504897", "879294487301718016", "879294487301718016", "879294487301718016", "879294487301718016", "879294487301718016", "879294487301718016", "879294487301718016", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "879725003306147840", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "880554577187266560", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884443272948658176", "884449239291580416", "884449239291580416", "884449239291580416", "884449239291580416", "884449239291580416", "884449239291580416", "884449239291580416", "884449239291580416", "884449239291580416", "884449239291580416", "884449239291580416", "884449239291580416", "884449239291580416", "884449239291580416", "884449239291580416", "884449239291580416", "884449239291580416", "884449239291580416", "884449239291580416", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "888602569148211203", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "889499262136045569", "892184654501076994", "892184654501076994", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892273691563896832", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892362154610917378", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892588073950773248", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892691375715745792", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "892798375753592836", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893112719519481856", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893176202210553857", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893193878685220864", "893410677196378112", "893410677196378112", "893410677196378112", "893410677196378112", "893410677196378112", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "893820545141280769", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894338592796966912", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "894941805208158213", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896030502544707584", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "896363657029980160", "897496105562320896", "897496105562320896", "897496105562320896", "897496105562320896", "897496105562320896", "897496105562320896", "897496105562320896", "897496105562320896", "897496105562320896", "897496105562320896", "897496105562320896", "897496105562320896", "897496105562320896", "897496105562320896", "897496105562320896", "898167777718087684", "898167777718087684", "898167777718087684", "898167777718087684", "898167777718087684", "898167777718087684", "898167777718087684", "898167777718087684", "898167777718087684", "898167777718087684", "898167777718087684", "898167777718087684", "898167777718087684", "898167777718087684", "898167777718087684", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "899981663358373888", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "900088665292115972", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "903629781744439297", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904544458431188992", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "904875370842689536", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "905126100250103813", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907312535396929537", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "907551723459416071", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908286434607542274", "908734745994969089", "908734745994969089", "908734745994969089", "908734745994969089", "908734745994969089", "908734745994969089", "908734745994969089", "908734745994969089", "908734745994969089", "908734745994969089", "908734745994969089", "909242618969804800", "909242618969804800", "909242618969804800", "909242618969804800", "909242618969804800", "909242618969804800", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "909653512010833920", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910956939886043136", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "910989105172852737", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "911062230711640064", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912690697416753152", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "912727491177013248", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915316133904908293", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915580081938018304", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "915607553693888513", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "916613798261293056", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918246391440228353", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "918832236249657344", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "919669366781706240", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920269157647527938", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920572900440199168", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "920660006831382528", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "922849122008354817", "923223339388297216", "923223339388297216", "923223339388297216", "923223339388297216", "923223339388297216", "923223339388297216", "923223339388297216", "923223339388297216", "923223339388297216", "923223339388297216", "923223339388297216", "923223339388297216", "923223339388297216", "923223339388297216", "923223339388297216", "923223339388297216", "923223339388297216", "923223339388297216", "923223339388297216", "923223339388297216", "923223339388297216", "923223339388297216", "923223339388297216", "923223339388297216", "923223339388297216", "923223339388297216", "923223339388297216", "923223339388297216", "923223339388297216", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923230419746803713", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923248261519040514", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923266288830636044", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "923439846089113600", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "924319630398775303", "927566369616515072", "927566369616515072", "927566369616515072", "927566369616515072", "927566369616515072", "927566369616515072", "927566369616515072", "927566369616515072", "927566369616515072", "927566369616515072", "927566369616515072", "927566369616515072", "927566369616515072", "927566369616515072", "927566369616515072", "927566369616515072", "927566369616515072", "927566369616515072", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "930438052610265088", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "932859950103236609", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "938770201344200704", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "940986690969194497", "941217999981301762", "941217999981301762", "941217999981301762", "941217999981301762", "941217999981301762", "941217999981301762", "941217999981301762", "941217999981301762", "941217999981301762", "941217999981301762", "941217999981301762", "941217999981301762", "941217999981301762", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941263756402798592", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "941417798999379969", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943018433427443713", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "943109273776807938", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "944323985805922305", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "946165040243249152", "948498673247358977", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949253263987216384", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949293612990386177", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "949986519250624513", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951098342158807041", "951132634083545088", "959005382273355776", "959005382273355776", "959005382273355776", "959005382273355776", "959005382273355776", "959005382273355776", "959005382273355776", "959005382273355776", "959005382273355776", "959005382273355776", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "962366768260972544", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "963476902324391937", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "966731525709619200", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "975991513489567744", "977974559671504896", "977974559671504896", "977974559671504896", "977974559671504896", "977974559671504896", "977974559671504896", "977974559671504896", "977974559671504896", "977974559671504896", "977974559671504896", "977974559671504896", "977974559671504896", "977974559671504896", "977974559671504896", "977974559671504896", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978285770992685056", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "978391597799886848", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979267491590483971", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979328804953849857", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "979645983637364738", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "982215750432604160", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "989986709801664518", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "993132960718012417", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "996263346189094916", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997455495156895744", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997518915268939776", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "997733530103824384", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840", "998600110144675840"]}