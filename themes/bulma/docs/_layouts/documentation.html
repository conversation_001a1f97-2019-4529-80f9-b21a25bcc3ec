---
layout: default
route: documentation
---

{% include global/navbar.html id="Documentation" %}

{% assign current_category = page.doc-tab %}
{% assign current_link_id = page.breadcrumb | last %}
{% assign current_link = site.data.links.by_id[current_link_id] %}

{% assign subtitle = current_link.subtitle %}

{% if page.subtitle %}
  {% assign subtitle = page.subtitle %}
{% endif %}

<header id="docsToggles" class="bd-docs-toggles">
  <button class="button is-primary is-light bd-fat-button is-small" id="docsNavButton">Show menu</button>
  <button class="button is-primary is-light bd-fat-button is-small" id="docsSideButton">Show sidebar</button>
</header>

<div
  id="docs"
  class="
  bd-docs
    {% if page.fullwidth %}
      bd-is-fullwidth
    {% else %}
      bd-is-contained
    {% endif %}
    {% if page.fullmain %}bd-is-full-main{% endif %}
  ">
  <div id="docsNavOverlay" class="bd-docs-overlay"></div>

  <nav id="docsNav" class="bd-docs-nav {% if page.fullwidth %}bd-stickied{% endif %}">
    {% include components/categories.html %}
  </nav>

  <main class="bd-docs-main">
    {% if page.fullwidth %}
      {% include components/docs-body.html %}
    {% else %}
      <div class="container">
        {% include components/docs-body.html %}
      </div>
    {% endif %}
  </main>

  <aside id="docsSide" class="bd-docs-side {% if page.fullwidth %}bd-stickied{% endif %}">
    {% include global/fortyfour.html %}

    {% if page.meta %}
      {%
        include elements/meta.html
        colors=page.meta.colors
        sizes=page.meta.sizes
        variables=page.meta.variables
        experimental=page.meta.experimental
      %}
    {% endif %}

    {% include components/anchors.html %}
  </aside>
</div>
