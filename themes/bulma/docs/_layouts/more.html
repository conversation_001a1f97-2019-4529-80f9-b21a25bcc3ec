---
layout: default
route: more
---

{% include global/navbar.html id="More" %}

{% assign current_link_id = page.breadcrumb | last %}
{% assign current_link = site.data.links.by_id[current_link_id] %}

{% assign category_links = site.data.links.more %}

{% for link_id in category_links %}
  {% if link_id == current_link_id %}
    {% unless forloop.first %}
      {% assign previous_index = forloop.index0 | minus: 1 %}
      {% assign previous_link_id = category_links[previous_index] %}
      {% assign previous_link = site.data.links.by_id[previous_link_id] %}
    {% endunless %}

    {% unless forloop.last %}
      {% assign next_index = forloop.index0 | plus: 1 %}
      {% assign next_link_id = category_links[next_index] %}
      {% assign next_link = site.data.links.by_id[next_link_id] %}
    {% endunless %}
  {% endif %}
{% endfor %}

{% capture hero_content %}
{% include elements/sponsor-button.html %}
{% endcapture %}
{%
  include components/hero.html
  color="sponsor"
  icon=page.icon
  title=page.title
  subtitle=page.subtitle
  content=page.hero_content
%}

        <div class="bd-content">
          {{ content }}
        </div>
