---
layout: default
route: blog
breadcrumb:
- home
- blog
---

{% include global/navbar.html id="Blog" %}

{% assign title = page.title | markdownify %}
{% assign introduction = page.introduction | markdownify %}
{% assign m = page.date | date: "%B" %}
{% assign d = page.date | date: "%-d" %}
{% assign y = page.date | date: "%Y" %}

{% capture banner_title %}
<a href="{{ site.url }}/blog">
  The <strong>official</strong> Bulma <strong>blog</strong>
</a>
{% endcapture %}

{% capture banner_more %}
{% include elements/rss-button.html %}
{% endcapture %}

{% include
  elements/banner.html
  title=banner_title
  more=banner_more
%}

<header class="bd-single-header">
  <div class="container is-max-desktop">
    <div class="bd-single-titles">
      <h1 class="title">
        {{ title }}
      </h1>
      <div class="subtitle">
        {{ introduction }}
      </div>
      <div class="subtitle is-date">
        {{ m }}
        {% case d %}{% when "1" or "21" or "31" %}{{ d }}st{% when "2" or "22" %}{{ d }}nd{% when "3" or "23" %}{{ d }}rd{% else %}{{ d }}th{% endcase %},
        {{ y }}
      </div>
    </div>
  </div>
</header>

{% if page.image %}
  <div class="bd-single-image">
    <div>
      <figure>
        <img
             src="https://source.unsplash.com/{{ page.image }}/800x450"
          srcset="https://source.unsplash.com/{{ page.image }}/800x450 800w,
                  https://source.unsplash.com/{{ page.image }}/1600x900 1600w,
                  https://source.unsplash.com/{{ page.image }}/3200x1800 3200w
                  "
           sizes="(max-width: 799px) 100vw, 1600px"
          alt="{{ page.alt }}"
          width="1600"
          height="900"
        >
      </figure>
    </div>
  </div>
{% else %}
  <div class="bd-single-hr">
    <div class="container is-max-desktop">
      <hr>
    </div>
  </div>
{% endif %}

<div class="bd-single-body">
  <div class="container is-max-desktop">
    <div class="bd-single-content content">
      {{ content }}
    </div>
  </div>
</div>

<div class="bd-single-hr">
  <div class="container is-max-desktop">
    <hr>
  </div>
</div>

<nav class="bd-pagination">
  <div class="container is-max-desktop">
    <div class="bd-pagination-body">
      {% if page.previous.url %}
        <a class="button bd-fat-button is-link is-light bd-pagination-previous" href="{{ page.previous.url }}">
          ← {{ page.previous.name }}
        </a>
      {% endif %}

      {% if page.next.url %}
        <a class="button bd-fat-button is-link is-light bd-pagination-next" href="{{ page.next.url }}">
          {{ page.next.name }} →
        </a>
      {% endif %}
    </div>
  </div>
</nav>

<div class="bd-single-carbon">
  {% include elements/carbon.html %}
</div>
