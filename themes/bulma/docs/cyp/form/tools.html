---
layout: cypress
title: Form/Tools
---

{% capture content %}
  <div class="control">
    <input class="input" type="text" placeholder="Find a repository">
  </div>
  <div class="control">
    <a class="button is-info">
      Search
    </a>
  </div>
{% endcapture %}

<label id="label" class="label">
  Form label
</label>

{% for size in site.data.sizes %}
  <label id="label-{{ size }}" class="label is-{{ size }}">
    {{ size | capitalize }} form label
  </label>
{% endfor %}

<p id="help" class="help">This username is available</p>

{% for color in site.data.colors.justColors %}
  <p id="help-{{ color }}" class="help is-{{ color }}">This username is available</p>
{% endfor %}

<div id="field" class="field">
  {{ content }}
</div>

<div id="field-has-addons" class="field has-addons">
  {{ content }}
</div>

<div id="field-has-addons-centered" class="field has-addons has-addons-centered">
  {{ content }}
</div>

<div id="field-has-addons-right" class="field has-addons has-addons-right">
  {{ content }}
</div>

<div id="field-has-addons-fullwidth" class="field has-addons has-addons-fullwidth">
  {{ content }}
</div>

<div id="field-is-grouped" class="field is-grouped">
  {{ content }}
</div>

<div id="field-is-grouped-centered" class="field is-grouped is-grouped-centered">
  {{ content }}
</div>

<div id="field-is-grouped-right" class="field is-grouped is-grouped-right">
  {{ content }}
</div>

<div id="field-is-grouped-multiline" class="field is-grouped is-grouped-multiline">
  {{ content }}
</div>

<div id="field-is-horizontal" class="field is-horizontal">
  {{ content }}
</div>

<div class="field is-horizontal">
  <div id="field-label" class="field-label is-normal">
    <label class="label">From</label>
  </div>

  <div id="field-body" class="field-body">
    <div class="field">
      <p class="control is-expanded has-icons-left">
        <input class="input" type="text" placeholder="Name">
        <span class="icon is-small is-left">
          <i class="fas fa-user"></i>
        </span>
      </p>
    </div>

    <div class="field">
      <p class="control is-expanded has-icons-left has-icons-right">
        <input class="input is-success" type="email" placeholder="Email" value="<EMAIL>">
        <span class="icon is-small is-left">
          <i class="fas fa-envelope"></i>
        </span>
        <span class="icon is-small is-right">
          <i class="fas fa-check"></i>
        </span>
      </p>

      <div class="field"></div>
    </div>
  </div>
</div>

{% for size in site.data.sizes %}
  <div id="field-label-{{ size }}" class="field-label is-{{ size }}">
    {{ size | capitalize }} field label
  </div>
{% endfor %}

<div id="control" class="control">
  <input class="input" type="text" placeholder="Find a repository">
</div>

<div id="control-has-icons-left" class="control has-icons-left">
  <input class="input" type="email" placeholder="Text input">

  <span class="icon is-left">
    <i class="fas fa-envelope fa-sm"></i>
  </span>
</div>

<div id="control-has-icons-right" class="control has-icons-right">
  <input class="input" type="email" placeholder="Text input">

  <span class="icon is-right">
    <i class="fas fa-check fa-lg"></i>
  </span>
</div>

<div id="control-has-icons-left-and-right" class="control has-icons-left has-icons-right">
  <input class="input" type="email" placeholder="Text input">

  <span class="icon is-medium is-left">
    <i class="fas fa-envelope fa-lg"></i>
  </span>

  <span class="icon is-medium is-right">
    <i class="fas fa-check fa-lg"></i>
  </span>
</div>

<div id="control-loading" class="control is-loading"></div>

<div id="control-loading-small" class="control is-loading is-small"></div>

<div id="control-loading-medium" class="control is-loading is-medium"></div>

<div id="control-loading-large" class="control is-loading is-large"></div>

<p>Last element</p>
