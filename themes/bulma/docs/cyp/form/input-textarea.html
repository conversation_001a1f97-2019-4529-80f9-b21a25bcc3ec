---
layout: cypress
title: Form/Input Textarea
---

<input id="input" class="input" type="text" placeholder="Text input">

{% for color in site.data.colors.justColors %}
  <input id="input-{{ color }}" class="input is-{{ color }}" type="text" placeholder="{{ color | capitalize }} input">
{% endfor %}

{% for size in site.data.sizes %}
  <input id="input-{{ size }}" class="input is-{{ size }}" type="text" placeholder="{{ size | capitalize }} input">
{% endfor %}

<input id="input-fullwidth" class="input is-fullwidth" type="text" placeholder="Fullwidth input">

<input id="input-inline" class="input is-inline" type="text" placeholder="Inline input">

<input id="input-rounded" class="input is-rounded" type="text" placeholder="Rounded input">

<input id="input-static" class="input is-static" type="text" placeholder="Static input">

<textarea id="textarea" class="textarea" placeholder="Textarea"></textarea>

<textarea id="textarea-fixed" class="textarea has-fixed-size" placeholder="Textarea fixed"></textarea>
