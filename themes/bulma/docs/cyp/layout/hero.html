---
layout: cypress
title: Layout/Hero
---

{% capture content %}
  <div class="hero-body">
    <p class="title">
      Hero title
    </p>
    <p class="subtitle">
      Hero subtitle
    </p>
  </div>
{% endcapture %}

<section id="hero" class="hero">
  {{ content }}
</section>

{% for color in site.data.colors.justColors %}
  <section id="hero-{{ color }}" class="hero is-{{ color }}">
    {{ content }}
  </section>
{% endfor %}

<section id="hero-small" class="hero is-small">
  {{ content }}
</section>

<section id="hero-medium" class="hero is-medium">
  {{ content }}
</section>

<section id="hero-large" class="hero is-large">
  {{ content }}
</section>

<section id="hero-halfheight" class="hero is-halfheight">
  {{ content }}
</section>

<section id="hero-fullheight" class="hero is-fullheight">
  {{ content }}
</section>

<section id="hero-with-container" class="hero is-halfheight">
  <div class="container">
    {{ content }}
  </div>
</section>

<div id="hero-buttons" class="hero-buttons"></div>
<div id="hero-head" class="hero-head"></div>
<div id="hero-foot" class="hero-foot"></div>
<div id="hero-body" class="hero-body"></div>
