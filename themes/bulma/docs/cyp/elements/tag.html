---
layout: cypress
title: Elements/Tag
---

<div id="tags" class="tags">
  <span id="tag" class="tag">
    Tag
  </span>

  <span id="tag-rounded" class="tag is-rounded">
    Rounded
  </span>
</div>

<div class="block">
  <span id="tag-normal" class="tag is-normal">
    Normal
  </span>

  <span id="tag-medium" class="tag is-medium">
    Medium
  </span>

  <span id="tag-large" class="tag is-large">
    Large
  </span>
</div>

<div id="tags-medium" class="tags are-medium">
  <span class="tag">Tag</span>
  <span class="tag">Tag</span>
  <span class="tag">Tag</span>
</div>

<div id="tags-large" class="tags are-large">
  <span class="tag">Tag</span>
  <span class="tag">Tag</span>
  <span class="tag">Tag</span>
</div>

<div id="tags-centered" class="tags is-centered">
  <span class="tag">Tag</span>
  <span class="tag">Tag</span>
  <span class="tag">Tag</span>
</div>

<div id="tags-right" class="tags is-right">
  <span class="tag">Tag</span>
  <span class="tag">Tag</span>
  <span class="tag">Tag</span>
</div>

<div id="tags-addons" class="tags has-addons">
  <span class="tag">Tag</span>
  <span class="tag">Tag</span>
  <span class="tag">Tag</span>
</div>

<div class="block">
  {% for color in site.data.colors.justColors %}
    <span id="tag-{{ color }}" class="tag is-{{ color }}">
      {{ color | capitalize }}
    </span>
  {% endfor %}
</div>

<div class="block">
  {% for color in site.data.colors.justColors %}
    <span id="tag-{{ color }}-light" class="tag is-{{ color }} is-light">
      {{ color | capitalize }}
    </span>
  {% endfor %}
</div>
