---
layout: cypress
title: Components/Dropdown
---

{% capture dropdownTrigger %}
<div class="dropdown-trigger">
  <button class="button" aria-haspopup="true" aria-controls="dropdown-menu">
    <span>Dropdown button</span>
    <span class="icon is-small">
      <i class="fas fa-angle-down" aria-hidden="true"></i>
    </span>
  </button>
</div>
{% endcapture %}

{% capture dropdownMenu %}
<div class="dropdown-menu">
  <div class="dropdown-content">
    <a href="#" class="dropdown-item">
      Dropdown item
    </a>
    <a class="dropdown-item">
      Other dropdown item
    </a>
    <a href="#" class="dropdown-item is-active">
      Active dropdown item
    </a>
    <div class="dropdown-item">
      <p>You can insert <strong>any type of content</strong> within the dropdown menu.</p>
    </div>
    <a href="#" class="dropdown-item">
      Other dropdown item
    </a>
    <hr class="dropdown-divider">
    <a href="#" class="dropdown-item">
      With a divider
    </a>
  </div>
</div>
{% endcapture %}

<div id="dropdown" class="dropdown">
  {{ dropdownTrigger }}
  {{ dropdownMenu }}
</div>

<div id="dropdown-active" class="dropdown is-active">
  {{ dropdownTrigger }}
  {{ dropdownMenu }}
</div>
