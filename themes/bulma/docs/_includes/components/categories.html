<nav id="categories" class="bd-categories">
  <div class="bd-categories-filter">
    <input id="categoriesFilter" class="input is-small" type="text" name="" placeholder="Filter links" style="border-radius: 0.5em;">
    <span class="bd-key">f</span>
  </div>

  {% for category_id in site.data.links.categoryIds %}
    {% assign category_links = site.data.links.categories[category_id] %}
    {% assign category_link = site.data.links.by_id[category_id] %}

    {% if category_id == 'overview' %}
      <p class="bd-category-group">
        Guides
      </p>
    {% elsif category_id == 'columns' %}
      <p class="bd-category-group">
        CSS Library
      </p>
    {% endif %}

    <div class="bd-category {% if category_id == current_category %}is-current{% endif %}">
      <header class="bd-category-header">
        <a
          class="
            bd-category-name
            {% if category_id == current_link_id %}is-active{% endif %}
          "
          href="{{ site.url }}{{ category_link.path }}"
          data-name="{{ category_link.name }}"
        >
          <strong class="bd-name">{{ category_link.name }}</strong>
        </a>

        <button class="bd-category-toggle icon">
          <i class="fas fa-chevron-down"></i>
        </button>
      </header>

      <ul class="bd-category-list">
        {% for link_id in category_links %}
          {% assign link = site.data.links.by_id[link_id] %}

          <li {% if link_id == current_link_id %}class="is-current"{% endif %}>
            <a
              href="{{ site.url }}{{ link.path }}"
              data-name="{{ link.name }}"
            >
              <span class="bd-name">{{ link.name }}</span>

              {% if link.new %}
                <span class="ml-1 tag bd-mini-tag is-primary">
                  New!
                </span>
              {% endif %}
            </a>
          </li>
        {% endfor %}
      </ul>
    </div>
  {% endfor %}

  <p
    id="categoriesNoResults"
    class="
      bd-categories-no-results
      has-background-primary-light
      is-size-7
      has-text-primary-dark
      has-text-centered
      p-3
    ">
    There are no results.
    <br>
    <button class="button is-primary is-small">Reset search <span class="bd-key">esc</span></button>
  </p>
</nav>
