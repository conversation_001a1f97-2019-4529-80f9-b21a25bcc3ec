{% capture content %}
  {% assign anchor_name = include.anchor_name | default: 'Variables' %}
  {% assign tab         = include.tab         | default: page.doc-tab %}
  {% assign subtab      = include.subtab      | default: page.doc-subtab %}
  {% assign type        = include.type        | default: tab %}
  {% assign data        = include.data        | default: site.data.variables[tab][subtab] %}
  {% assign variables   = include.variables   | default: data.list %}
  {% assign table_class = include.table_class | default: 'is-bordered' %}

  You can use {{ variables_link | strip }} to <strong>customize</strong> this {{ type }}. Simply set one or multiple of these variables <em>before</em> importing Bulma. <a href="{{ site.url }}/documentation/overview/customize/">Learn how</a>.
{% endcapture %}

{% capture variables_size %}{{ variables | size }}{% endcapture %}
{% capture variables_keys_size %}{{ include.variables_keys | size }}{% endcapture %}

{% if variables_size != '0' or variables_keys_size != '0' %}
  <div class="bd-vars">
    {% include elements/anchor.html name=anchor_name %}

    <div class="bd-var bd-is-head">
      <div class="bd-var-name">
        Name
      </div>
      <div class="bd-var-type">
        Type
      </div>
      <div class="bd-var-value">
        Value
      </div>
      <div class="bd-var-computed-value">
        Computed Value
      </div>
      <div class="bd-var-computed-type">
        Computed Type
      </div>
    </div>
    {% if include.variables_keys %}
      {% for key in include.variables_keys %}
        {% assign variable = site.data.variables[include.folder][include.file].by_name[key] %}
        {% include components/variables-row.html variable=variable %}
      {% endfor %}
    {% else %}
      {% for variable_name in variables %}
        {% assign variable = data.by_name[variable_name] %}
        {% include components/variables-row.html variable=variable %}
      {% endfor %}
    {% endif %}
  </div>
{% endif %}
