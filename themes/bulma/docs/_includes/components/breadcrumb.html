<div class="bd-breadcrumb">
  <nav class="breadcrumb" aria-label="breadcrumbs">
    <ul>
      {% for key in page.breadcrumb %}
        {% assign link = site.data.links.by_id[key] %}
        <li{% if forloop.last %} class="is-active"{% endif %}>
          <a href="{{ site.url }}{{ link.path }}">{{ link.name }}</a>
        </li>
      {% endfor %}
    </ul>
  </nav>

  {% if previous_link or next_link %}
    <nav class="bd-prev-next">
      {% if previous_link %}
        <a href="{{ site.url }}{{ previous_link.path }}" title="{{ previous_link.name }}">
          ←
        </a>
      {% else %}
        <span>
          ←
        </span>
      {% endif %}

      {% if next_link %}
        <a href="{{ site.url }}{{ next_link.path }}" title="{{ next_link.name }}">
          →
        </a>
      {% else %}
        <span>
          →
        </span>
      {% endif %}
    </nav>
  {% endif %}
</div>
