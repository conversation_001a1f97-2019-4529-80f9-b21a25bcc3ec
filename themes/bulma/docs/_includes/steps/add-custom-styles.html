{% capture mystyles %}
@charset "utf-8";

// Import a Google Font
@import url('https://fonts.googleapis.com/css?family=Nunito:400,700');

// Set your brand colors
$purple: #8A4D76;
$pink: #FA7C91;
$brown: #757763;
$beige-light: #D0D1CD;
$beige-lighter: #EFF0EB;

// Update Bulma's global variables
$family-sans-serif: "Nunito", sans-serif;
$grey-dark: $brown;
$grey-light: $beige-light;
$primary: $purple;
$link: $pink;
$widescreen-enabled: false;
$fullhd-enabled: false;

// Update some of Bulma's component variables
$body-background-color: $beige-lighter;
$control-border-width: 2px;
$input-border-color: transparent;
$input-shadow: none;

// Import only what you need from Bulma
@import "../node_modules/bulma/sass/utilities/_all.sass";
@import "../node_modules/bulma/sass/base/_all.sass";
@import "../node_modules/bulma/sass/elements/button.sass";
@import "../node_modules/bulma/sass/elements/container.sass";
@import "../node_modules/bulma/sass/elements/title.sass";
@import "../node_modules/bulma/sass/form/_all.sass";
@import "../node_modules/bulma/sass/components/navbar.sass";
@import "../node_modules/bulma/sass/layout/hero.sass";
@import "../node_modules/bulma/sass/layout/section.sass";
{% endcapture %}

{% capture step_6 %}
  <div class="content">
    <p>
      Replace the content of the <code>mystyles.scss</code> file with the following:
    </p>
  </div>

  <div class="bd-highlight-full">
    {% highlight scss %}{{ mystyles }}{% endhighlight %}
  </div>

  <div class="content">
    {% if include.build %}
      <p>
        Rebuild the CSS to see the result:
      </p>
    {% else %}
      <p>
        Since you are watching for changes, simply <strong>save the file</strong> to see the result:
      </p>
    {% endif %}
  </div>

  {%
  include components/figure.html
    style="background-color: #EFF0EB;"
    path="customize/custom-bulma-03-styled"
    extension="png"
    alt="Bulma customized"
    width="600"
    height="300"
    caption="Bulma's customized theme"
  %}

  <div class="content">
    <p>
      And voilà! You've managed to install and customize Bulma.
    </p>
  </div>
{% endcapture %}

{% assign step_title = ". Add your own Bulma styles" | prepend: include.number %}

{% include components/step.html
  title=step_title
  content=step_6
%}
