{% capture init %}
npm init
{% endcapture %}

{% capture step_1 %}
  <div class="content">
    <p>
      In your terminal, create a new folder called <code>mybulma</code>, navigate to it, then type the following command:
    </p>
  </div>

  {% highlight bash %}{{ init }}{% endhighlight %}

  <div class="content">
    <p>
      This will launch an interactive setup to create <code>package.json</code>. When prompted for an <strong>entry point</strong>, enter <code>{{ include.entry }}</code>.
    </p>
  </div>
{% endcapture %}

{% assign step_title = '. Create a <code style="white-space: nowrap;">package.json</code> file' | prepend: include.number %}

{% include components/step.html
  title=step_title
  content=step_1
%}
