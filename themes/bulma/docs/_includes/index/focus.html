{% assign responsiveness_link = site.data.links.by_id['overview-responsiveness'] %}
{% assign modularity_link = site.data.links.by_id['overview-modular'] %}
{% assign columns_link = site.data.links.by_id['columns-basics'] %}

<div class="bd-focus">
  <div class="container">
    <nav class="columns is-multiline">
      <a class="bd-focus-item column is-half-tablet is-one-quarter-widescreen" href="{{ site.url }}{{ responsiveness_link.path }}">
        <div class="bd-focus-graph">
          <div class="bd-focus-device"></div>
        </div>

        <div class="bd-focus-text">
          <p class="title is-4">
            <strong>100% Responsive</strong>
          </p>
          <p class="subtitle is-6">
            Designed for <strong>mobile</strong> first
          </p>
        </div>
      </a>

      <a class="bd-focus-item column is-half-tablet is-one-quarter-widescreen" href="{{ site.url }}{{ modularity_link.path }}">
        <div class="bd-focus-graph">
          <figure class="bd-focus-cubes bd-focus-icon">
            <span class="bd-focus-cube bd-focus-cube-1 icon is-large">
              <i class="fas fa-2x fa-cube"></i>
            </span>
            <span class="bd-focus-cube bd-focus-cube-2 icon is-large">
              <i class="fas fa-2x fa-cube"></i>
            </span>
            <span class="bd-focus-cube bd-focus-cube-3 icon is-large">
              <i class="fas fa-2x fa-cube"></i>
            </span>
          </figure>
        </div>

        <div class="bd-focus-text">
            <p class="title is-4">
            <strong>Modular</strong>
          </p>
          <p class="subtitle is-6">
            Just import what you <strong>need</strong>
          </p>
        </div>
      </a>

      <a class="bd-focus-item column is-half-tablet is-one-quarter-widescreen" href="{{ site.url }}{{ columns_link.path }}">
        <div class="bd-focus-graph">
          <figure class="bd-focus-icon">
            <span class="bd-focus-css3 icon is-large">
              <i class="fab fa-3x fa-css3"></i>
            </span>
          </figure>
        </div>

        <div class="bd-focus-text">
          <p class="title is-4">
            <strong>Modern</strong>
          </p>
          <p class="subtitle is-6">
            Built with <strong>Flexbox</strong>
          </p>
        </div>
      </a>

      <a class="bd-focus-item column is-half-tablet is-one-quarter-widescreen" href="{{ site.data.meta.github }}" target="_blank">
        <div class="bd-focus-graph">
          <figure class="bd-focus-icon">
            <span class="bd-focus-github icon is-large">
              <i class="fab fa-3x fa-github-alt"></i>
            </span>
          </figure>
        </div>

        <div class="bd-focus-text">
          <p class="title is-4">
            <strong>Free</strong>
          </p>
          <p class="subtitle is-6">
            Open source on <strong>GitHub</strong>
          </p>
        </div>
      </a>
    </nav>
  </div>
</div>
