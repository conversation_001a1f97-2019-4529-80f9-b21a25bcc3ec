{% assign hero_link = site.data.links.by_id['layout-hero'] %}

<section class="bd-index-fullscreen hero is-fullheight is-light">
  <div class="hero-head">
    <div class="container">
      <div class="tabs is-centered">
        <ul>
          <li><a>This is always at the top</a></li>
        </ul>
      </div>
    </div>
  </div>

  <div class="hero-body">
    <div class="container">
      <header class="bd-index-header">
        <h3 class="title is-3">
          <a href="{{ site.url }}{{ hero_link.path }}">
            <strong>Fullscreen</strong> vertical centering
          </a>
        </h3>
        <h4 class="subtitle is-4">
          Include any content you want, it's always centered
        </h4>
      </header>

      <nav class="buttons is-centered">
        <a
          class="button is-large is-primary"
          href="{{ site.url }}{{ hero_link.path }}"
        >
          <span>Check out the <strong>Hero component</strong></span>
          <span class="icon">
            <i class="fas fa-arrow-right"></i>
          </span>
        </a>
      </nav>
    </div>
  </div>

  <div class="hero-foot">
    <div class="container">
      <div class="tabs is-centered">
        <ul>
          <li><a>And this at the bottom</a></li>
        </ul>
      </div>
    </div>
  </div>
</section>
