<section class="section is-medium">
  <div class="container">
    <h3 class="title is-2">
      <a href="{{ site.url }}/documentation/layout/level/">
        Flexible <strong>horizontal level</strong>
      </a>
    </h3>
    <h4 class="subtitle is-4">Include any type of element, they will remain vertically centered</h4>
    <nav class="level">
      <div class="level-left">
        <div class="level-item">
          <p class="title is-4">
            <strong>123</strong> posts
          </p>
        </div>
        <p class="level-item">
          <a class="button is-link">
            New
          </a>
        </p>
        <div class="level-item">
          <div class="field has-addons">
            <p class="control">
              <input class="input" type="text" placeholder="Filter">
            </p>
            <p class="control">
              <button class="button">
                Search
              </button>
            </p>
          </div>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          Show:
        </div>
        <p class="level-item">
          <strong>All</strong>
        </p>
        <p class="level-item">
          <a href="#">Published</a>
        </p>
        <p class="level-item">
          <a href="#">Drafts</a>
        </p>
        <div class="level-item">
          Sort:
        </div>
        <div class="level-item">
          <span class="select">
            <select>
              <option>Date created</option>
            </select>
          </span>
        </div>
      </div>
    </nav>
  </div>
</section>
