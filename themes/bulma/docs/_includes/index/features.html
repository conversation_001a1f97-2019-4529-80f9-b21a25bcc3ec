{% capture form %}
<div class="field">
  <label class="label">Form label</label>
  <div class="control">
    <input class="input" type="text" placeholder="Input">
  </div>
</div>
<div class="field">
  <p class="control">
    <span class="select">
      <select>
        <option>Select dropdown</option>
      </select>
    </span>
  </p>
</div>
<div class="field">
  <p class="control">
    <textarea class="textarea" placeholder="Textarea"></textarea>
  </p>
</div>
<div class="field">
  <p class="control">
    <label class="checkbox">
      <input type="checkbox">
      Checkbox
    </label>
  </p>
</div>
<div class="field">
  <p class="control">
    <label class="radio">
      <input type="radio" name="question">
      Radio
    </label>
    <label class="radio">
      <input type="radio" name="question">
      Buttons
    </label>
  </p>
</div>
<div class="field">
  <p class="control">
    <a class="button is-link">Button</a>
  </p>
</div>
{% endcapture %}

{% capture box %}
<div class="box">
  <article class="media">
    <div class="media-left">
      <figure class="image is-64x64">
        <img src="{{site.url}}/images/placeholders/128x128.png" alt="Image">
      </figure>
    </div>
    <div class="media-content">
      <div class="content">
        <p>
          <strong>John Smith</strong> <small>@johnsmith</small> <small>31m</small>
          <br>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean efficitur sit amet massa fringilla egestas. Nullam condimentum luctus turpis.
        </p>
      </div>
      <nav class="level is-mobile">
        <div class="level-left">
          <a class="level-item">
            <span class="icon is-small"><i class="fas fa-reply"></i></span>
          </a>
          <a class="level-item">
            <span class="icon is-small"><i class="fas fa-retweet"></i></span>
          </a>
          <a class="level-item">
            <span class="icon is-small"><i class="fas fa-heart"></i></span>
          </a>
        </div>
      </nav>
    </div>
  </article>
</div>
{% endcapture %}

{% capture button %}
<div class="field is-grouped is-grouped-multiline">
  <div class="control"><a class="button">Button</a></div>
  <div class="control"><a class="button is-white">White</a></div>
  <div class="control"><a class="button is-light">Light</a></div>
  <div class="control"><a class="button is-dark">Dark</a></div>
  <div class="control"><a class="button is-black">Black</a></div>
  <div class="control"><a class="button is-text">Text</a></div>
</div>

<div class="field is-grouped is-grouped-multiline">
  <div class="control"><a class="button is-primary">Primary</a></div>
  <div class="control"><a class="button is-link">Link</a></div>
  <div class="control"><a class="button is-info">Info</a></div>
  <div class="control"><a class="button is-success">Success</a></div>
  <div class="control"><a class="button is-warning">Warning</a></div>
  <div class="control"><a class="button is-danger">Danger</a></div>
</div>
{% endcapture %}

{% capture notification %}
<div class="notification is-primary">
  <button class="delete"></button>
  Lorem ipsum dolor sit amet, consectetur
  adipiscing elit lorem ipsum dolor. <strong>Pellentesque risus mi</strong>, tempus quis placerat ut, porta nec nulla. Vestibulum rhoncus ac ex sit amet fringilla. Nullam gravida purus diam, et dictum <a>felis venenatis</a> efficitur. Sit amet,
  consectetur adipiscing elit
</div>
{% endcapture %}

{% capture progress %}
<progress class="progress is-primary" value="30" max="100">30%</progress>
{% endcapture %}

{% capture tags %}
<span class="tag is-black">Black</span>
<span class="tag is-dark">Dark</span>
<span class="tag is-light">Light</span>
<span class="tag is-white">White</span>
<span class="tag is-primary">Primary</span>
<span class="tag is-link">Link</span>
<span class="tag is-info">Info</span>
<span class="tag is-success">Success</span>
<span class="tag is-warning">Warning</span>
<span class="tag is-danger">Danger</span>
{% endcapture %}

{% capture cards %}
<div class="columns">
  <div class="column is-half">
    <div class="card">
      <div class="card-image">
        <figure class="image is-4by3">
          <img src="{{site.url}}/images/placeholders/1280x960.png" alt="Image">
        </figure>
      </div>
      <div class="card-content">
        <div class="media">
          <div class="media-left">
            <figure class="image is-48x48">
              <img src="{{site.url}}/images/placeholders/96x96.png" alt="Image">
            </figure>
          </div>
          <div class="media-content">
            <p class="title is-4">John Smith</p>
            <p class="subtitle is-6">@johnsmith</p>
          </div>
        </div>

        <div class="content">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit.
          Phasellus nec iaculis mauris. <a>@bulmaio</a>.
          <a>#css</a> <a>#responsive</a>
          <br>
          <small>11:09 PM - 1 Jan 2016</small>
        </div>
      </div>
    </div>
  </div>

  <div class="column is-half">
    <div class="card">
      <div class="card-content">
        <p class="title">
          “There are two hard things in computer science: cache invalidation, naming things, and off-by-one errors.”
        </p>
        <p class="subtitle">
          Jeff Atwood
        </p>
      </div>
      <footer class="card-footer">
        <p class="card-footer-item">
          <span>
            View on <a href="https://twitter.com/codinghorror/status/506010907021828096">Twitter</a>
          </span>
        </p>
        <p class="card-footer-item">
          <span>
            Share on <a href="#">Facebook</a>
          </span>
        </p>
      </footer>
    </div>
  </div>
</div>
{% endcapture %}

{% capture dropdown %}
<div class="dropdown is-active">
  <div class="dropdown-trigger">
    <a class="button">
      <span>Dropdown button</span>
      <span class="icon is-small">
        <i class="fas fa-angle-down"></i>
      </span>
    </a>
  </div>
  <div class="dropdown-menu">
    <div class="dropdown-content">
      <a class="dropdown-item">
        Dropdown item
      </a>
      <a class="dropdown-item">
        Other dropdown item
      </a>
      <a class="dropdown-item is-active">
        Active dropdown item
      </a>
      <a class="dropdown-item">
        Other item
      </a>
      <hr class="dropdown-divider">
      <a class="dropdown-item">
        With a divider
      </a>
    </div>
  </div>
</div>
{% endcapture %}

{% capture message %}
<article class="message is-primary">
  <div class="message-header">
    <p>Primary</p>
    <button class="delete"></button>
  </div>
  <div class="message-body">
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. <strong>Pellentesque risus mi</strong>, tempus quis placerat ut, porta nec nulla. Vestibulum rhoncus ac ex sit amet fringilla. Nullam gravida purus diam, et dictum <a>felis venenatis</a> efficitur. Aenean ac <em>eleifend lacus</em>, in mollis lectus. Donec sodales, arcu et sollicitudin porttitor, tortor urna tempor ligula, id porttitor mi magna a neque. Donec dui urna, vehicula et sem eget, facilisis sodales sem.
  </div>
</article>
{% endcapture %}

{% capture pagination %}
<nav class="pagination">
  <a class="pagination-previous">Previous</a>
  <a class="pagination-next">Next page</a>
  <ul class="pagination-list">
    <li>
      <a class="pagination-link">1</a>
    </li>
    <li>
      <span class="pagination-ellipsis">&hellip;</span>
    </li>
    <li>
      <a class="pagination-link">45</a>
    </li>
    <li>
      <a class="pagination-link is-current">46</a>
    </li>
    <li>
      <a class="pagination-link">47</a>
    </li>
    <li>
      <span class="pagination-ellipsis">&hellip;</span>
    </li>
    <li>
      <a class="pagination-link">86</a>
    </li>
  </ul>
</nav>
{% endcapture %}

{% capture panel %}
<nav class="panel">
  <p class="panel-heading">
    Repositories
  </p>
  <div class="panel-block">
    <p class="control has-icons-left">
      <input class="input is-small" type="text" placeholder="Search">
      <span class="icon is-small is-left">
        <i class="fas fa-search"></i>
      </span>
    </p>
  </div>
  <p class="panel-tabs">
    <a class="is-active">All</a>
    <a>Public</a>
    <a>Private</a>
    <a>Sources</a>
    <a>Forks</a>
  </p>
  <a class="panel-block is-active">
    <span class="panel-icon">
      <i class="fas fa-book"></i>
    </span>
    bulma
  </a>
  <a class="panel-block">
    <span class="panel-icon">
      <i class="fas fa-book"></i>
    </span>
    marksheet
  </a>
  <a class="panel-block">
    <span class="panel-icon">
      <i class="fas fa-book"></i>
    </span>
    minireset.css
  </a>
  <a class="panel-block">
    <span class="panel-icon">
      <i class="fas fa-book"></i>
    </span>
    jgthms.github.io
  </a>
  <a class="panel-block">
    <span class="panel-icon">
      <i class="fas fa-code-branch"></i>
    </span>
    daniellowtw/infBoard
  </a>
  <a class="panel-block">
    <span class="panel-icon">
      <i class="fas fa-code-branch"></i>
    </span>
    mojs
  </a>
  <label class="panel-block">
    <input type="checkbox">
    Remember me
  </label>
  <div class="panel-block">
    <button class="button is-link is-outlined is-fullwidth">
      Reset all filters
    </button>
  </div>
</nav>
{% endcapture %}

{% capture tabs %}
<div class="tabs is-boxed">
  <ul>
    <li class="is-active">
      <a>
        <span class="icon is-small"><i class="fas fa-image"></i></span>
        <span>Pictures</span>
      </a>
    </li>
    <li>
      <a>
        <span class="icon is-small"><i class="fas fa-music"></i></span>
        <span>Music</span>
      </a>
    </li>
    <li>
      <a>
        <span class="icon is-small"><i class="fas fa-film"></i></span>
        <span>Videos</span>
      </a>
    </li>
    <li>
      <a>
        <span class="icon is-small"><i class="fas fa-file-alt"></i></span>
        <span>Documents</span>
      </a>
    </li>
  </ul>
</div>
{% endcapture %}

{% capture media %}
<article class="media">
  <figure class="media-left">
    <p class="image is-64x64">
      <img src="{{ site.url }}/images/placeholders/128x128.png">
    </p>
  </figure>
  <div class="media-content">
    <div class="content">
      <p>
        <strong>John Smith</strong> <small>@johnsmith</small> <small>31m</small>
        <br>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin ornare magna eros, eu pellentesque tortor vestibulum ut. Maecenas non massa sem. Etiam finibus odio quis feugiat facilisis.
      </p>
    </div>
    <nav class="level is-mobile">
      <div class="level-left">
        <a class="level-item">
          <span class="icon is-small"><i class="fas fa-reply"></i></span>
        </a>
        <a class="level-item">
          <span class="icon is-small"><i class="fas fa-retweet"></i></span>
        </a>
        <a class="level-item">
          <span class="icon is-small"><i class="fas fa-heart"></i></span>
        </a>
      </div>
    </nav>
  </div>
  <div class="media-right">
    <button class="delete"></button>
  </div>
</article>
{% endcapture %}

{% capture menu %}
<aside class="menu">
  <p class="menu-label">
    General
  </p>
  <ul class="menu-list">
    <li><a>Dashboard</a></li>
    <li><a>Customers</a></li>
  </ul>
  <p class="menu-label">
    Administration
  </p>
  <ul class="menu-list">
    <li><a>Team Settings</a></li>
    <li>
      <a class="is-active">Manage Your Team</a>
      <ul>
        <li><a>Members</a></li>
        <li><a>Plugins</a></li>
        <li><a>Add a member</a></li>
      </ul>
    </li>
    <li><a>Invitations</a></li>
    <li><a>Cloud Storage Environment Settings</a></li>
    <li><a>Authentication</a></li>
  </ul>
  <p class="menu-label">
    Transactions
  </p>
  <ul class="menu-list">
    <li><a>Payments</a></li>
    <li><a>Transfers</a></li>
    <li><a>Balance</a></li>
  </ul>
</aside>
{% endcapture %}

<section class="section is-medium" style="padding-top: 0;">
  <div class="container">
    <div class="columns">
      <div class="column is-2">
        <h4 class="title is-4"><strong>Elements</strong></h4>
      </div>
      <div class="column is-10">
        <div class="columns">
          <div class="column is-3">
            <h4 class="subtitle is-4 bd-feature-title">
              <a href="{{ site.url }}/documentation/form/general/">
                Form
              </a>
            </h4>
          </div>
          <div class="column is-9">
            {{ form }}
          </div>
        </div>

        <div class="columns">
          <div class="column is-3">
            <h4 class="subtitle is-4 bd-feature-title">
              <a href="{{ site.url }}/documentation/elements/box/">
                Box
              </a>
            </h4>
          </div>
          <div class="column is-9">
            {{ box }}
          </div>
        </div>

        <div class="columns">
          <div class="column is-3">
            <h4 class="subtitle is-4 bd-feature-title">
              <a href="{{ site.url }}/documentation/elements/button/">
                Button
              </a>
            </h4>
          </div>
          <div class="column is-9">
            {{ button }}
          </div>
        </div>

        <div class="columns">
          <div class="column is-3">
            <h4 class="subtitle is-4 bd-feature-title">
              <a href="{{ site.url }}/documentation/elements/notification/">
                Notification
              </a>
            </h4>
          </div>
          <div class="column is-9">
            {{ notification }}
          </div>
        </div>

        <div class="columns">
          <div class="column is-3">
            <h4 class="subtitle is-4 bd-feature-title">
              <a href="{{ site.url }}/documentation/elements/progress/">
                Progress bar
              </a>
            </h4>
          </div>
          <div class="column is-9">
            {{ progress }}
          </div>
        </div>

        <div class="columns">
          <div class="column is-3">
            <h4 class="subtitle is-4 bd-feature-title">
              <a href="{{ site.url }}/documentation/elements/tag/">
                Tags
              </a>
            </h4>
          </div>
          <div class="column is-9">
            {{ tags }}
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<section class="section is-medium" style="padding-top: 0;">
  <div class="container">
    <div class="columns">
      <div class="column is-2">
        <h4 class="title is-4"><strong>Components</strong></h4>
      </div>
      <div class="column is-10">
        <div class="columns">
          <div class="column is-3">
            <h4 class="subtitle is-4 bd-feature-title">
              <a href="{{ site.url }}/documentation/components/card/">
                Card
              </a>
            </h4>
          </div>
          <div class="column is-9">
            {{ cards }}
          </div>
        </div>

        <div class="columns">
          <div class="column is-3">
            <h4 class="subtitle is-4 bd-feature-title">
              <a href="{{ site.url }}/documentation/components/dropdown/">
                Dropdown
              </a>
            </h4>
          </div>
          <div class="column is-9" style="height: 16rem;">
            {{ dropdown }}
          </div>
        </div>

        <div class="columns">
          <div class="column is-3">
            <h4 class="subtitle is-4 bd-feature-title">
              <a href="{{ site.url }}/documentation/components/message/">
                Message
              </a>
            </h4>
          </div>
          <div class="column is-9">
            {{ message }}
          </div>
        </div>

        <div class="columns">
          <div class="column is-3">
            <h4 class="subtitle is-4 bd-feature-title">
              <a href="{{ site.url }}/documentation/components/pagination/">
                Pagination
              </a>
            </h4>
          </div>
          <div class="column is-9">
            {{ pagination }}
          </div>
        </div>

        <div class="columns">
          <div class="column is-3">
            <h4 class="subtitle is-4 bd-feature-title">
              <a href="{{ site.url }}/documentation/components/tabs/">
                Tabs
              </a>
            </h4>
          </div>
          <div class="column is-9">
            {{ tabs }}
          </div>
        </div>

        <div class="columns">
          <div class="column is-3">
            <h4 class="subtitle is-4 bd-feature-title">
              <a href="{{ site.url }}/documentation/layout/media-object/">
                Media object
              </a>
            </h4>
          </div>
          <div class="column is-9">
            {{ media }}
          </div>
        </div>

        <div class="columns">
          <div class="column is-3">
            <h4 class="subtitle is-4 bd-feature-title">
              <a href="{{ site.url }}/documentation/components/menu/">
                Menu
              </a>
              and
              <a href="{{ site.url }}/documentation/components/panel/">
                Panel
              </a>
            </h4>
          </div>
          <div class="column is-9">
            <div class="columns">
              <div class="column is-half">
                {{ menu }}
              </div>
              <div class="column is-half">
                {{ panel }}
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</section>
