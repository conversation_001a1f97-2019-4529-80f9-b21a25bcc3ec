{% assign modifiers_link = site.data.links.by_id['overview-modifiers'] %}
{% assign heading_href = site.url | append: modifiers_link.path %}

<section class="bd-index-section">
  {%
    include components/heading.html
    color="primary"
    icon="fas fa-graduation-cap"
    title="So <strong>easy</strong> to learn"
    subtitle="Get a design <strong>started</strong> within <strong>minutes</strong>"
    href=heading_href
    button_icon_after="fas fa-arrow-right"
    button_label="See <strong>modifiers</strong> syntax"
  %}

  <div class="container">
    <div class="columns is-vcentered">
      <div class="column is-size-5-desktop">
        {%
          include elements/tw.html
          tweet_id="868829487072464897"
          drawing_id='crazy'
          drawing_width=108
          drawing_height=48
        %}
      </div>

      <div class="column">
        <div class="block">
          <div class="field">
            <p class="control">
              <code>button</code>
            </p>
          </div>
          <a class="button">Button</a>
        </div>
        <div class="block">
          <div class="field">
            <p class="control">
              <code>button is-primary</code>
            </p>
          </div>
          <a class="button is-primary">Button</a>
        </div>
        <div class="block">
          <div class="field">
            <p class="control">
              <code>button is-primary is-large</code>
            </p>
          </div>
          <a class="button is-primary is-large">Button</a>
        </div>
        <div class="block">
          <div class="field">
            <p class="control">
              <code>button is-primary is-large is-loading</code>
            </p>
          </div>
          <a class="button is-primary is-large is-loading">Button</a>
        </div>
      </div>
    </div>
  </div>
</section>
