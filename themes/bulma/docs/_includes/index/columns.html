{% capture columns %}
<div class="columns">
  <div class="column">1</div>
  <div class="column">2</div>
  <div class="column">3</div>
  <div class="column">4</div>
  <div class="column">5</div>
</div>
{% endcapture %}

{% assign columns_link = site.data.links.by_id['columns-basics'] %}
{% assign heading_href = site.url | append: columns_link.path %}

<section class="bd-index-section" style="--bd-section-h: 229deg;">
  {%
    include components/heading.html
    color="link"
    icon="fas fa-columns"
    title="The <strong>simplest</strong> grid system"
    subtitle="Just add columns, they will <strong>resize themselves</strong>"
    href=heading_href
    button_icon_after="fas fa-arrow-right"
    button_label="See <strong>columns</strong> docs"
  %}

  <div class="container">
    <div class="message is-warning is-hidden-tablet">
      <p class="message-header">
        Better on desktop
      </p>
      <p class="message-body">
        This interactive tool works better on larger screens! That's because
        Bulma columns are <strong>vertical by default</strong>. I recommend
        revisiting this page later when you're on desktop. 😉
      </p>
    </div>

    <div id="grid" class="columns">
      <div class="column">
        <div class="notification is-link has-text-centered">
          <p class="title">1</p>
        </div>
      </div>
      <div class="column">
        <div class="notification is-link has-text-centered">
          <p class="title">2</p>
        </div>
      </div>
      <div class="column">
        <div class="notification is-link has-text-centered">
          <p class="title">3</p>
        </div>
      </div>
      <div class="column">
        <div class="notification is-link has-text-centered">
          <p class="title">4</p>
        </div>
      </div>
      <div class="column">
        <div class="notification is-link has-text-centered">
          <p class="title">5</p>
        </div>
      </div>
      <div class="column" style="display: none;">
        <div class="notification is-link has-text-centered">
          <p class="title">6</p>
        </div>
      </div>
      <div class="column" style="display: none;">
        <div class="notification is-link has-text-centered">
          <p class="title">7</p>
        </div>
      </div>
      <div class="column" style="display: none;">
        <div class="notification is-link has-text-centered">
          <p class="title">8</p>
        </div>
      </div>
      <div class="column" style="display: none;">
        <div class="notification is-link has-text-centered">
          <p class="title">9</p>
        </div>
      </div>
      <div class="column" style="display: none;">
        <div class="notification is-link has-text-centered">
          <p class="title">10</p>
        </div>
      </div>
      <div class="column" style="display: none;">
        <div class="notification is-link has-text-centered">
          <p class="title">11</p>
        </div>
      </div>
      <div class="column" style="display: none;">
        <div class="notification is-link has-text-centered">
          <p class="title">12</p>
        </div>
      </div>
    </div>

    <div class="bd-columns-tools">
      <div class="bd-columns-tool bd-is-try">
        <div class="buttons bd-has-drawing">
          <a id="add" class="button is-large is-link is-unselectable">
            <strong>Add column</strong>
          </a>
          <a id="remove" class="button is-large is-light is-unselectable">
            <strong>Remove</strong>
          </a>
          {% include elements/drawing.html id='try-it-out' width=150 height=65
          %}
        </div>
      </div>

      <div class="bd-columns-tool bd-is-markup">
        <div id="markup">
          {% highlight html %}{{ columns }}{% endhighlight %}
        </div>
      </div>
    </div>

    <div id="message" class="message is-info">
      <p class="message-header">Info</p>
      <p class="message-body">
        While it's possible to add as many columns as you want, it is
        recommended to stick with <strong>12 columns</strong>.<br />
        If you want smaller divisions, you can always
        <strong>nest</strong> columns.
      </p>
    </div>
  </div>
</section>
