<div class="section bd-partnrs">
  <div class="container">
    <div class="bd-minis">
      <p class="bd-minis-title">
        {% assign mini_item = site.data.global.navbar_items[1] %}
        <a class="bd-minis-link" href="{{ site.url }}/{{ mini_item.id }}">
          <span class="icon {{ mini_item.color }}">
            <i
              class="{{ mini_item.fa_type }} {{ mini_item.fa_icon }}"
            ></i> </span
          >From the expo <strong>#builtwithbulma</strong>
        </a>
      </p>
      <div class="bd-minis-list">
        {% for website_id in site.data.expo.lists.home %} {% assign website =
        site.data.expo.by_id[website_id] %} {% assign imageName = website_id |
        slugify %} {% assign imagePath = "/images/expo/" | prepend: site.url |
        append: imageName %} {% assign size1x = "672x420" %} {% assign size2x =
        "1344x840" %}
        <a class="bd-mini" href="{{ site.url }}/{{ mini_item.id }}">
          <img
            src="{{ imagePath }}-{{ size1x }}.jpg"
            data-src="{{ imagePath }}-{{ size1x }}.jpg"
            data-srcset="{{ imagePath }}-{{ size2x }}.jpg 2x,
                           {{ imagePath }}-{{ size1x }}.jpg 1x"
            width="672"
            height="420"
          />
        </a>
        {% endfor %}
      </div>
    </div>
  </div>
</div>
