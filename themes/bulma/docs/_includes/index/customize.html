{% assign customize_link = site.data.links.by_id['customize'] %}
{% assign heading_href = site.url | append: customize_link.path %}

<section class="bd-index-section" style="--bd-section-h: 330deg;">
  {%
    include components/heading.html
    color="sass"
    icon="fab fa-sass"
    title="So <strong>quick</strong> to customize"
    subtitle="Simply set your own Sass variables before importing Bulma"
    href=heading_href
    button_icon_after="fas fa-arrow-right"
    button_label="See <strong>customization</strong> docs"
  %}

  <div class="container">
    <header class="block is-size-5-desktop mb-6">
      {% include elements/tw.html tweet_id="1070320154452656128" %}
    </header>

    <div class="columns is-variable is-8">
      <div class="column">
        <div class="bd-highlight-full bd-has-drawing">
          {% highlight scss %}{% include snippets/customized.html %}{%
          endhighlight %} {% include elements/drawing.html id='customize'
          width=152 height=76 %}
        </div>
      </div>

      <div class="column">
        <div class="bd-index-custom bd-is-before">
          <p class="bd-index-custom-title">Before</p>
          <div class="bd-index-custom-example">
            <h1 class="title">
              Bulma
            </h1>
            <p class="subtitle">
              Modern CSS framework based on
              <a
                href="https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Flexible_Box_Layout/Basic_Concepts_of_Flexbox"
                >Flexbox</a
              >
            </p>
            <div class="field">
              <div class="control">
                <input class="input" type="text" placeholder="Input" />
              </div>
            </div>
            <div class="field">
              <p class="control">
                <span class="select">
                  <select>
                    <option>Select dropdown</option>
                  </select>
                </span>
              </p>
            </div>
            <div class="buttons">
              <a class="button is-primary">Primary</a>
              <a class="button is-link">Link</a>
            </div>
          </div>
        </div>

        <div class="bd-index-custom bd-is-after">
          <p class="bd-index-custom-title">After</p>
          <div class="bd-index-custom-example">
            <h1 class="title">
              Bulma
            </h1>
            <p class="subtitle">
              Modern CSS framework based on
              <a
                href="https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Flexible_Box_Layout/Basic_Concepts_of_Flexbox"
                >Flexbox</a
              >
            </p>
            <div class="field">
              <div class="control">
                <input class="input" type="text" placeholder="Input" />
              </div>
            </div>
            <div class="field">
              <p class="control">
                <span class="select">
                  <select>
                    <option>Select dropdown</option>
                  </select>
                </span>
              </p>
            </div>
            <div class="buttons">
              <a class="button is-primary">Primary</a>
              <a class="button is-link">Link</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
