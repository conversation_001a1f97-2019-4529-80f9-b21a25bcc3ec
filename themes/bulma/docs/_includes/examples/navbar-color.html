<nav class="navbar is-{{ include.color }}">
  <div class="navbar-brand">
    <a class="navbar-item" href="{{ site.url }}">
      {% if include.light %}
        <img src="{{ site.url }}/images/bulma-logo.png" alt="Bulma: a modern CSS framework based on Flexbox" width="112" height="28">
      {% else %}
        <img src="{{ site.url }}/images/bulma-logo-white.png" alt="Bulma: a modern CSS framework based on Flexbox" width="112" height="28">
      {% endif %}
    </a>
    <div class="navbar-burger" data-target="navMenuColor{{ include.color }}-example">
      <span></span>
      <span></span>
      <span></span>
    </div>
  </div>

  <div id="navMenuColor{{ include.color }}-example" class="navbar-menu">
    <div class="navbar-start">
      <a class="navbar-item" href="{{ site.url }}/">
        Home
      </a>
      <div class="navbar-item has-dropdown is-hoverable">
        <a class="navbar-link" href="{{ site.url }}/documentation/overview/start/">
          Docs
        </a>
        <div class="navbar-dropdown">
          <a class="navbar-item" href="{{ site.url }}/documentation/overview/start/">
            Overview
          </a>
          <a class="navbar-item" href="{{ site.url }}/documentation/overview/modifiers/">
            Modifiers
          </a>
          <a class="navbar-item" href="{{ site.url }}/documentation/columns/basics/">
            Columns
          </a>
          <a class="navbar-item" href="{{ site.url }}/documentation/layout/container/">
            Layout
          </a>
          <a class="navbar-item" href="{{ site.url }}/documentation/form/general/">
            Form
          </a>
          <hr class="navbar-divider">
          <a class="navbar-item" href="{{ site.url }}/documentation/elements/box/">
            Elements
          </a>
          <a class="navbar-item is-active" href="{{ site.url }}/documentation/components/breadcrumb/">
            Components
          </a>
        </div>
      </div>
    </div>

    <div class="navbar-end">
      <div class="navbar-item">
        <div class="field is-grouped">
          <p class="control">
            <a class="bd-tw-button button" data-social-network="Twitter" data-social-action="tweet" data-social-target="https://bulma.io" target="_blank" href="https://twitter.com/intent/tweet?text=Bulma: a modern CSS framework based on Flexbox&amp;hashtags=bulmaio&amp;url=https://bulma.io&amp;via=jgthms">
              <span class="icon">
                <i class="fab fa-twitter"></i>
              </span>
              <span>
                Tweet
              </span>
            </a>
          </p>
          <p class="control">
            <a class="button is-primary" href="{{ site.data.meta.download }}">
              <span class="icon">
                <i class="fas fa-download"></i>
              </span>
              <span>Download</span>
            </a>
          </p>
        </div>
      </div>
    </div>
  </div>
</nav>
