<nav class="navbar has-shadow">
  <div class="container">
    <div class="navbar-tabs">
      <a class="navbar-item is-tab {% if page.doc-subtab == 'breadcrumb' %}is-active{% endif %}" href="{{ site.url }}/documentation/components/breadcrumb/">
        Breadcrumb
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'card' %}is-active{% endif %}" href="{{ site.url }}/documentation/components/card/">
        Card
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'dropdown' %}is-active{% endif %}" href="{{ site.url }}/documentation/components/dropdown/">
        Dropdown
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'menu' %}is-active{% endif %}" href="{{ site.url }}/documentation/components/menu/">
        Menu
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'message' %}is-active{% endif %}" href="{{ site.url }}/documentation/components/message/">
        Message
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'modal' %}is-active{% endif %}" href="{{ site.url }}/documentation/components/modal/">
        Modal
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'navbar' %}is-active{% endif %}" href="{{ site.url }}/documentation/components/navbar/">
        Navbar
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'pagination' %}is-active{% endif %}" href="{{ site.url }}/documentation/components/pagination/">
        Pagination
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'panel' %}is-active{% endif %}" href="{{ site.url }}/documentation/components/panel/">
        Panel
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'tabs' %}is-active{% endif %}" href="{{ site.url }}/documentation/components/tabs/">
        Tabs
      </a>
    </div>
  </div>
</nav>
