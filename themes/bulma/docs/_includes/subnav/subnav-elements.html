<nav class="navbar has-shadow">
  <div class="container">
    <div class="navbar-tabs">
      <a class="navbar-item is-tab {% if page.doc-subtab == 'box' %}is-active{% endif %}" href="{{ site.url }}/documentation/elements/box/">
        Box
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'button' %}is-active{% endif %}" href="{{ site.url }}/documentation/elements/button/">
        Button
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'content' %}is-active{% endif %}" href="{{ site.url }}/documentation/elements/content/">
        Content
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'delete' %}is-active{% endif %}" href="{{ site.url }}/documentation/elements/delete/">
        Delete
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'icon' %}is-active{% endif %}" href="{{ site.url }}/documentation/elements/icon/">
        Icon
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'image' %}is-active{% endif %}" href="{{ site.url }}/documentation/elements/image/">
        Image
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'notification' %}is-active{% endif %}" href="{{ site.url }}/documentation/elements/notification/">
        Notification
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'progress' %}is-active{% endif %}" href="{{ site.url }}/documentation/elements/progress/">
        Progress
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'table' %}is-active{% endif %}" href="{{ site.url }}/documentation/elements/table/">
        Table
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'tag' %}is-active{% endif %}" href="{{ site.url }}/documentation/elements/tag/">
        Tag
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'title' %}is-active{% endif %}" href="{{ site.url }}/documentation/elements/title/">
        Title
      </a>
    </div>
  </div>
</nav>
