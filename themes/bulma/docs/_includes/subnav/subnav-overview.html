<nav class="navbar has-shadow">
  <div class="container">
    <div class="navbar-tabs">
      <a class="navbar-item is-tab {% if page.doc-subtab == 'start' %}is-active{% endif %}" href="{{ site.url }}/documentation/overview/start/">
        Start
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'customize' %}is-active{% endif %}" href="{{ site.url }}/documentation/overview/customize/">
        Customize
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'classes' %}is-active{% endif %}" href="{{ site.url }}/documentation/overview/classes/">
        Classes
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'modular' %}is-active{% endif %}" href="{{ site.url }}/documentation/overview/modular/">
        Modular
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'responsiveness' %}is-active{% endif %}" href="{{ site.url }}/documentation/overview/responsiveness/">
        Responsiveness
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'variables' %}is-active{% endif %}" href="{{ site.url }}/documentation/overview/variables/">
        Variables
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'colors' %}is-active{% endif %}" href="{{ site.url }}/documentation/overview/colors/">
        Colors
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'functions' %}is-active{% endif %}" href="{{ site.url }}/documentation/overview/functions/">
        Functions
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'mixins' %}is-active{% endif %}" href="{{ site.url }}/documentation/overview/mixins/">
        Mixins
      </a>
    </div>
  </div>
</nav>
