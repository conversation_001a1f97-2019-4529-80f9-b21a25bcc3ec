<nav class="navbar has-shadow">
  <div class="container">
    <div class="navbar-tabs">
      <a class="navbar-item is-tab {% if page.doc-subtab == 'container' %}is-active{% endif %}" href="{{ site.url }}/documentation/layout/container/">
        Container
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'level' %}is-active{% endif %}" href="{{ site.url }}/documentation/layout/level/">
        Level
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'media-object' %}is-active{% endif %}" href="{{ site.url }}/documentation/layout/media-object/">
        Media Object
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'hero' %}is-active{% endif %}" href="{{ site.url }}/documentation/layout/hero/">
        Hero
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'section' %}is-active{% endif %}" href="{{ site.url }}/documentation/layout/section/">
        Section
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'footer' %}is-active{% endif %}" href="{{ site.url }}/documentation/layout/footer/">
        Footer
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'tiles' %}is-active{% endif %}" href="{{ site.url }}/documentation/layout/tiles/">
        Tiles
      </a>
    </div>
  </div>
</nav>
