<nav class="navbar has-shadow">
  <div class="container">
    <div class="navbar-tabs">
      <a class="navbar-item is-tab {% if page.doc-subtab == 'basics' %}is-active{% endif %}" href="{{ site.url }}/documentation/columns/basics/">
        Basics
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'sizes' %}is-active{% endif %}" href="{{ site.url }}/documentation/columns/sizes/">
        Sizes
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'responsiveness' %}is-active{% endif %}" href="{{ site.url }}/documentation/columns/responsiveness/">
        Responsiveness
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'nesting' %}is-active{% endif %}" href="{{ site.url }}/documentation/columns/nesting/">
        Nesting
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'gap' %}is-active{% endif %}" href="{{ site.url }}/documentation/columns/gap/">
        Gap
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'options' %}is-active{% endif %}" href="{{ site.url }}/documentation/columns/options/">
        Options
      </a>
    </div>
  </div>
</nav>
