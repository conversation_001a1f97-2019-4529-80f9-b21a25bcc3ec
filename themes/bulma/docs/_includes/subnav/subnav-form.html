<nav class="navbar has-shadow">
  <div class="container">
    <div class="navbar-tabs">
      <a class="navbar-item is-tab {% if page.doc-subtab == 'general' %}is-active{% endif %}" href="{{ site.url }}/documentation/form/general/">
        General
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'input' %}is-active{% endif %}" href="{{ site.url }}/documentation/form/input/">
        Input
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'textarea' %}is-active{% endif %}" href="{{ site.url }}/documentation/form/textarea/">
        Textarea
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'select' %}is-active{% endif %}" href="{{ site.url }}/documentation/form/select/">
        Select
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'checkbox' %}is-active{% endif %}" href="{{ site.url }}/documentation/form/checkbox/">
        Checkbox
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'radio' %}is-active{% endif %}" href="{{ site.url }}/documentation/form/radio/">
        Radio
      </a>
      <a class="navbar-item is-tab {% if page.doc-subtab == 'file' %}is-active{% endif %}" href="{{ site.url }}/documentation/form/file/">
        File
        <span class="tag is-success" style="margin-left: 1em;">
          New!
        </span>
      </a>
    </div>
  </div>
</nav>
