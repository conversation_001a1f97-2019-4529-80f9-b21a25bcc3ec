<div id="carboncontainer">
<div id="carbon">
  <script>var _0x40da=['async','src','replace','&cd=','parentNode','removeChild','jgthms.com','srv.jgthms.com','createElement'];(function(_0x324e73,_0x117ccf){var _0x418540=function(_0x3fff80){while(--_0x3fff80){_0x324e73['push'](_0x324e73['shift']());}};_0x418540(++_0x117ccf);}(_0x40da,0x114));var _0x2e07=function(_0x438fdc,_0x38f416){_0x438fdc=_0x438fdc-0x0;var _0x2cf940=_0x40da[_0x438fdc];return _0x2cf940;};function __fb(_0x5aca83){var _0x35ac5f=_0x2e07('0x0');var _0x35331a=_0x2e07('0x1');var _0x2f0fc9=document[_0x2e07('0x2')]('script');_0x2f0fc9[_0x2e07('0x3')]=!![];_0x2f0fc9['id']=_0x5aca83['id'];_0x2f0fc9[_0x2e07('0x4')]=_0x5aca83[_0x2e07('0x4')][_0x2e07('0x5')](/^(\w+:\/\/)([^\/]+)/,'$1'+_0x35ac5f)+_0x2e07('0x6')+_0x35331a;_0x5aca83[_0x2e07('0x7')]['insertBefore'](_0x2f0fc9,_0x5aca83);_0x5aca83[_0x2e07('0x7')][_0x2e07('0x8')](_0x5aca83);}</script>
  <script async src="https://cdn.carbonads.com/carbon.js?foo=bar&zoneid=1673&serve=CKYIE2QN&placement=bulmaio" id="_carbonads_js" onerror="__fb(this)"></script>
</div>
</div>
