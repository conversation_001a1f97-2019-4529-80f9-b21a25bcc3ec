{% assign current_version_value = site.data.meta.version | remove: "." | plus: 0 %}
{% assign tag_version_value = include.version | remove: "." | plus: 0 %}

<div class="tags has-addons">
  {% if tag_version_value > current_version_value %}
    <span class="tag is-warning is-light">Coming soon!</span>
  {% elsif tag_version_value == current_version_value %}
    <span class="tag">New!</span>
    <span class="tag is-success">{{ include.version }}</span>
  {% else %}
    <span class="tag">Since</span>
    <span class="tag is-info">{{ include.version }}</span>
  {% endif %}
</div>
