{% assign tweet = site.data.love.tweets_by_id[include.tweet_id] %}
{% assign twUrl = "https://twitter.com/" | append: tweet.username | append: "/status/" | append: tweet.id %}

<article
  data-likes="{{ tweet.hearts }}"
  data-id="{{ tweet.id }}"
  class="bd-tw {{ include.modifier }} {% if include.drawing_id %}bd-has-drawing{% endif %}">
  <header class="bd-tw-header">
    <a class="bd-tw-author" href="{{ twUrl }}" target="_blank">
      <figure class="bd-tw-avatar">
        <img class="lazyload" src="{{ tweet.avatar }}">
      </figure>
      <div class="bd-tw-name">
        <strong class="bd-tw-fullname">
          {{ tweet.fullname }}
        </strong>
        <span class="bd-tw-username">
          @{{ tweet.username }}
        </span>
      </div>
    </a>
  </header>

  <div class="bd-tw-content">
    {{ tweet.content }}
  </div>

  <p class="bd-tw-date">
    <a href="{{ twUrl }}" target="_blank">
      {{ tweet.date }}
    </a>
  </p>

  <ul class="bd-tw-actions">
    <li class="bd-tw-action is-reply">
      <a class="bd-tw-action-link" href="https://twitter.com/intent/tweet?in_reply_to={{ tweet.id }}">
        <div class="bd-tw-icon"></div>
      </a>
    </li>

    <li class="bd-tw-action is-retweet">
      <a class="bd-tw-action-link" href="https://twitter.com/intent/retweet?tweet_id={{ tweet.id }}">
        <div class="bd-tw-icon"></div>
        {% if tweet.retweets != 0 %}
          <span class="bd-tw-action-stat">
            {{ tweet.retweets }}
          </span>
        {% endif %}
      </a>
    </li>

    <li class="bd-tw-action is-heart">
      <a class="bd-tw-action-link" href="https://twitter.com/intent/like?tweet_id={{ tweet.id }}&amp;ref_src=twsrc%5Etfw&amp;ref_url=http%3A%2F%2Fbulma.io%2F&amp;original_referer=http%3A%2F%2Fbulma.io%2F&amp;tw_i={{ tweet.id }}&amp;tw_p=tweetembed" target="_blank">
        <div class="bd-tw-icon"></div>
        {% if tweet.hearts != 0 %}
          <span class="bd-tw-action-stat">
            {{ tweet.hearts }}
          </span>
        {% endif %}
      </a>
    </li>
  </ul>

  {% if include.drawing_id %}
    {% include elements/drawing.html
      id=include.drawing_id
      width=include.drawing_width
      height=include.drawing_height
    %}
  {% endif %}
</article>
