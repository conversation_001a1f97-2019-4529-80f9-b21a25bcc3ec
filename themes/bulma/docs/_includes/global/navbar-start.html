<div id="{{ include.id }}" class="navbar-start bd-navbar-start bd-is-{{ include.version }}">
  <a class="
      navbar-item bd-navbar-item bd-navbar-item-base bd-navbar-item-documentation
      {% if page.route == 'documentation' %}is-active{% endif %}
      {% if page.layout == 'documentation' %}is-active{% endif %}
    "
    href="{{ site.url }}{{ site.data.meta.documentation }}">
    <span class="icon has-text-primary">
      <i class="fas fa-book"></i>
    </span>
    <span>
      Docs
    </span>
  </a>

  {% for link_id in site.data.links.navbar %}
    {%
      include global/navbar-item.html
      link_id=link_id
      class="bd-navbar-item-base"
    %}
  {% endfor %}

  {% for link_id in site.data.links.navbarMore %}
    {%
      include global/navbar-item.html
      link_id=link_id
      class="bd-navbar-item-more"
    %}
  {% endfor %}

  <div class="navbar-item bd-navbar-item bd-navbar-item-base has-dropdown is-hoverable">
    {% assign link = site.data.links.by_id['more'] %}

    <a class="navbar-link bd-navbar-ellipsis" href="{{ site.url }}{{ link.path }}">
      <span class="icon">
        <i class="fas fa-ellipsis-h"></i>
      </span>
    </a>

    <div class="navbar-dropdown bd-navbar-dropdown is-boxed">
      {% for link_id in site.data.links.more %}
        {% assign link = site.data.links.by_id[link_id] %}
        {% assign cleanpath = link.path | remove_first: "/" %}
        <a class="
            navbar-item
            {% if page.route == cleanpath %}is-active{% endif %}
          "
          data-route="{{ page.route }}"
          href="{{ site.url }}{{ link.path }}">
            <div>
              <div class="icon-text">
                <span class="icon has-text-{{ link.color }}">
                  <i class="{% if link.icon_brand %}fab{% elsif link.icon_regular %}far{% else %}fas{% endif %} fa-{{ link.icon }}"></i>
                </span>
                <span>
                  <strong>{{ link.name }}</strong>
                </span>
              </div>
              {{ link.subtitle }}
            </div>
        </a>
        {% unless forloop.last %}
          <hr class="navbar-divider {% if forloop.first %}{% endif %}">
        {% endunless %}
      {% endfor %}
    </div>
  </div>
</div>
