<script src="{{ site.url }}/vendor/native.js"></script>
<script>
  _native.init("CVAIKK3E", {
    targetClass: 'native-js',
    fallback: `<a href="https://jgthms.com/css-in-44-minutes-ebook" class="native-flex" target="_blank">
      <style>
        .native-js {
          background: linear-gradient(-30deg, #4f38a4, #a244bc 45%, #4f38a4 45%) #4f38a4;
        }

        .native-details {
          color: #FFFFFF !important;
        }

        .native-details:hover {
          color: #FFFFFF !important;
        }

        .native-cta {
          color: #FFFFFF;
          background-color: #3bb76d;
        }

        .native-cta:hover {
          color: #native_cta_color_hover;
          background-color: #3bb76d;
        }
      </style>
      <div class="native-main">
        <img class="native-img" src="{{ site.url }}/images/fortyfour-native.png">
        <div class="native-details">
          <span class="native-company">CSS in 44 minutes</span>
          <span class="native-desc">Learn how to build your own webpage from scratch!</span>
        </div>
      </div>
      <span class="native-cta">Buy the book</span>
    </a>`
  });
</script>

<div class="native-js">
  <a href="#native_link#" class="native-flex">
    <style>
      .native-js {
        background: linear-gradient(-30deg, #native_bg_color#E5, #native_bg_color#E5 45%, #native_bg_color# 45%) #fff;
      }

      .native-details {
        color: #native_color# !important;
      }

      .native-details:hover {
        color: #native_color_hover# !important;
      }

      .native-cta {
        color: #native_cta_color#;
        background-color: #native_cta_bg_color#;
      }

      .native-cta:hover {
        color: #native_cta_color_hover#;
        background-color: #native_cta_bg_color_hover#;
      }
    </style>
    <div class="native-main">
      <img class="native-img" src="#native_logo#">
      <div class="native-details">
        <span class="native-company">#native_company#</span>
        <span class="native-desc">#native_desc#</span>
      </div>
    </div>
    <span class="button is-medium native-cta">#native_cta#</span>
  </a>
</div>
