<script src="{{ site.url }}/vendor/clipboard-1.7.1.min.js"></script>
<script src="{{ site.url }}/vendor/js.cookie-2.1.4.min.js"></script>
<script src="{{ site.url }}/vendor/cupcakes-3.1.0.min.js"></script>

<script src="{{ site.url }}/lib/main.js?v={{ site.time | date: '%Y%m%d%H%M' }}"></script>

<script async src="https://www.googletagmanager.com/gtag/js?id=UA-82634666-2"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'UA-82634666-2');
</script>

{% if page.route == 'index' %}
  <script src="https://player.vimeo.com/api/player.js" ></script>
  <script type="text/javascript" src="{{ site.url }}/lib/index.js?v={{ site.time | date: '%Y%m%d%H%M' }}"></script>
  <script>
    WebFontConfig = {
      google: {
        families: ['Nunito:400,700']
      }
    };
    (function(d) {
      var wf = d.createElement('script'), s = d.scripts[0];
      wf.src = 'https://ajax.googleapis.com/ajax/libs/webfont/1.6.26/webfont.js';
      wf.async = true;
      s.parentNode.insertBefore(wf, s);
   })(document);
  </script>
{% endif %}

{% if page.layout == 'documentation' %}
  <script src="{{ site.url }}/lib/docs.js?v={{ site.time | date: '%Y%m%d%H%M' }}"></script>
{% endif %}

{% if page.doc-subtab == 'start' %}
  <script src="{{ site.url }}/lib/start.js?v={{ site.time | date: '%Y%m%d%H%M' }}"></script>
{% endif %}

<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/docsearch.js@2/dist/cdn/docsearch.min.js"></script>
<script type="text/javascript"> docsearch({
  apiKey: 'cb93c14bebd90678e789c946d95ea94d',
  indexName: 'bulma',
  inputSelector: '#algoliaSearch',
  debug: false // Set debug to true if you want to inspect the dropdown
});
</script>

<script>
window.addEventListener("load", function(){
window.cupcakeconsent.initialise({
  "content": {
    "message": "Just so you know, this website uses cookies.",
    "dismiss": "<span>👍</span> Got it!",
    "link": "What are cookies?"
  }
})});
</script>
