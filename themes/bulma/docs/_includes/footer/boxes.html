{% assign bootstrap_link = site.data.links.by_id['bootstrap'] %}
{% assign expo_link = site.data.links.by_id['expo'] %}
{% assign love_link = site.data.links.by_id['love'] %}

<div class="bd-footer-stars">
  <a class="bd-footer-star bd-is-bootstrap" href="{{ site.url }}{{ bootstrap_link.path }}">
    <header class="bd-footer-star-header">
      <h4 class="bd-footer-title">
        <strong>{{ bootstrap_link.name }}</strong>
      </h4>
      <p class="bd-footer-subtitle">
        An alternative to Bootstrap
      </p>
    </header>

    <figure class="bd-footer-star-figure">
      <img src="{{ site.url }}/images/footer/bootstrap-to-bulma.png" width="160" height="48">
    </figure>
  </a>

  <a class="bd-footer-star bd-is-expo" href="{{ site.url }}{{ expo_link.path }}">
    <header class="bd-footer-star-header">
      <h4 class="bd-footer-title">
        <span class="icon has-text-{{ expo_link.color }}">
          <i class="fas fa-{{ expo_link.icon }}"></i>
        </span>
        <strong>{{ expo_link.name }}</strong>
      </h4>
      <p class="bd-footer-subtitle">
        See what you can build with Bulma
      </p>
    </header>
  </a>

  <a class="bd-footer-star bd-is-love" href="{{ site.url }}{{ love_link.path }}">
    <header class="bd-footer-star-header">
      <h4 class="bd-footer-title">
        <span class="icon has-text-{{ love_link.color }}">
          <i class="fas fa-{{ love_link.icon }}"></i>
        </span>
        <strong>{{ love_link.name }}</strong>
      </h4>
      <p class="bd-footer-subtitle">
        Fans of Bulma
      </p>
    </header>
  </a>
</div>
