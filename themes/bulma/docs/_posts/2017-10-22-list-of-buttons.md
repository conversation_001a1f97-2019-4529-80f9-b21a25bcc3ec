---
layout: post
title: "New feature: list of buttons"
introduction: "What's better than one button? Multiple buttons!"
color: "danger"
name: "List of buttons"
icon: "hand-pointer"
---

Similarly to the [list of tags](/2017/08/03/list-of-tags/) launched a few months ago, the [button](/documentation/elements/button/) has its own **list of buttons**:

<figure>
  <a href="{{ site.url }}/documentation/elements/button/#list-of-buttons">
    <img src="{{ site.url }}/images/blog/list-of-buttons.png" alt="List of buttons in CSS" width="660" height="550">
  </a>
</figure>

Although you could already **group** buttons by using [from groups](/documentation/form/general/#form-group), this new `buttons` class makes everything easier.
