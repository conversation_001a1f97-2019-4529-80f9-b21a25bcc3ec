---
layout: post
title: "Metro UI CSS grid with Bulma tiles"
introduction: "Build a **Metro-UI-like grid in CSS** with the new `tile` element"
long_introduction: "Have you ever wanted to build a **Metro-UI-like grid in CSS**?<br>Thanks to Flexbox and the new [Bulma tiles](https://bulma.io/documentation/layout/tiles/), you now can! And it only requires 1 HTML element: the `tile` element."
color: "info"
name: "Metro UI"
icon: "th-large"
---

Have you ever wanted to build a **Metro-UI-like grid in CSS**?
Thanks to Flexbox and the new [Bulma tiles](https://bulma.io/documentation/layout/tiles/), you now can! And it only requires 1 HTML element: the `tile` element.

[![Metro UI grid tiles in CSS](/images/blog/metro-ui-css-grid-tiles.png)](https://bulma.io/documentation/layout/tiles/)

Check out the [documentation](https://bulma.io/documentation/layout/tiles/)!
