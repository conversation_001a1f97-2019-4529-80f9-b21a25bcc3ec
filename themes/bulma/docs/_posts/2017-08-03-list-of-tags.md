---
layout: post
title: "New feature: list of tags"
introduction: "What's better than one tag? Multiple tags!"
color: "success"
name: "List of tags"
icon: "tag"
---

The [tag](/documentation/elements/tag/) is a small but useful element that can be used in many contexts, to **enhance** the meaning of the text it's attached to.

A tag rarely comes on its own though, so <PERSON>ul<PERSON> now supports [list of tags](/documentation/elements/tag/#list-of-tags). The same way you can [group controls](together), you can now **group tags together** to form a list that can span multiple lines.

<figure>
  <a href="{{ site.url }}/documentation/elements/tag/#list-of-tags">
    <img src="{{ site.url }}/images/blog/list-of-tags.png" alt="List of tags in CSS" width="660" height="401">
  </a>
</figure>

As a **bonus**, there is also a [delete tag](/documentation/elements/tag/#combinations) available!
