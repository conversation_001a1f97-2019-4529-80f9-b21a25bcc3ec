// Index common

+mobile
  .bd-index-buttons
    margin-top: 3rem

+tablet
  .bd-index-buttons
    margin-top: 6rem

.bd-index-header
  text-align: center
  a,
  strong
    transition-duration: $speed
    transition-property: color
  a
    color: currentColor
    &:hover
      color: $blue
      strong
        color: $link
  strong
    font-weight: $weight-semibold
  .title
    strong
      color: $primary
  .subtitle
    color: $border-hover !important
    strong
      color: currentColor
  &:not(:last-child)
    margin-bottom: 3rem
  &.bd-is-left
    text-align: left

// JS

.bd-index-js-tweet
  .bd-tw
    margin-left: auto
    margin-right: auto

// Customize

.bd-index-custom-tweet
  .bd-tw
    margin-left: auto
    margin-right: auto

.bd-index-custom-title
  color: $border-hover

.bd-index-custom-example
  padding: 1rem
  .subtitle
    margin-bottom: 0.5rem

.bd-index-custom.bd-is-after
  color: $brown
  font-family: "Nunito", serif
  margin-top: 0.5rem
  a
    color: $pink
    &:hover
      color: #363636
  .subtitle
    color: $brown
  .input,
  .select select
    background-color: $beige-lighter
    border-color: transparent
    border-width: 2px
    box-shadow: none
    font-family: "Nunito", serif
    &:hover
      border-color: $beige-light
    &:focus
      border-color: $pink
      box-shadow: 0 0 0 0.125em rgba($pink, 0.25)
  .select
    &:not(.is-multiple):not(:hover)
      &::after
        border-color: $pink
  .button
    &.is-primary
      background-color: $mauve
      color: $scheme-main
      &:hover
        background-color: darken($mauve, 2.5%)
      &:active
        background-color: darken($mauve, 5%)
    &.is-link
      background-color: $pink
      color: $scheme-main
      &:hover
        background-color: darken($pink, 2.5%)
      &:active
        background-color: darken($pink, 5%)
  +selection
    background-color: $pink
    color: $scheme-main

// Fullscreen

.bd-index-fullscreen
  .tabs
    a
      color: $border-hover !important

// Columns

#grid
  .notification
    padding-left: 0
    padding-right: 0

.bd-columns-tools
  margin-top: 3rem

.bd-columns-tool
  @extend %center
  &.bd-is-try
    .buttons
      justify-content: center
    .button
      strong
        font-weight: $weight-semibold

#markup
  width: 100%
  .highlight pre
    max-height: none

#message
  display: none
  margin-top: 3rem

+mobile
  .bd-columns-tool
    &.bd-is-markup
      margin-top: 3rem

+tablet
  .bd-columns-tools
    align-items: flex-start
    display: flex
  .bd-columns-tool
    width: 50%
    &.bd-is-try
      padding-top: 60px

// Focus

.bd-focus
  margin: 6rem auto 0
  max-width: $intro-width

.bd-focus-item
  @extend %link-before-background
  border-radius: $radius-large
  padding-top: 1.25rem
  position: relative
  .title a,
  .subtitle
    transition-duration: $speed * 2
    transition-property: color
  .title
    position: relative
    transition-duration: $speed * 2
    transition-property: color
  .subtitle
    color: $border-hover
    position: relative
    strong
      color: currentColor
    .subtitle
      color: $text
  &:nth-child(1):hover
    .title
      color: $orange
  &:nth-child(2):hover
    .title
      color: $success
  &:nth-child(3):hover
    .title
      color: $link
  &:nth-child(4):hover
    .title
      color: $scheme-invert

.bd-focus-icon
  position: relative

.bd-focus-mobile
  color: $purple
  margin-right: -20px

.bd-focus-tablet
  color: $red

.bd-focus-desktop
  color: $orange
  position: relative
  top: 2px

.bd-focus-cubes
  position: relative
  height: 3rem
  margin: 0 auto
  top: -0.5rem
  width: 3rem

.bd-focus-cube
  color: $green
  position: absolute

.bd-focus-cube-1
  left: 0
  top: 0

.bd-focus-cube-2
  left: -1rem
  top: 23px

.bd-focus-cube-3
  left: 1rem
  top: 23px

.bd-focus-css3
  color: $blue

.bd-focus-github
  color: $github

// Intro

.intro-content
  margin-left: auto
  margin-right: auto
  max-width: 640px

.intro-title
  font-weight: $weight-normal
  line-height: 1.375
  strong
    font-weight: $weight-semibold

.intro-ghbtns
  margin-bottom: 16px
  min-height: 30px
  iframe,
  a,
  img
    height: 30px
  a
    display: inline-block
    min-width: 100px
    vertical-align: top
  img
    display: block

.intro-npm
  background: $black-ter
  border-radius: $radius
  color: $white
  display: flex
  font-size: 15px
  justify-content: space-between
  line-height: 20px
  padding: 15px 25px
  position: relative
  code
    font-size: inherit
    -moz-osx-font-smoothing: grayscale
    -webkit-font-smoothing: antialiased
  .intro-npm-copy
    border-radius: $radius
    color: $yellow
    cursor: pointer
    margin: -2px -7px -3px
    padding: 2px 7px 3px
    &:hover
      background-color: $yellow
      color: $black-ter
    &.is-success,
    &.is-error
      color: $scheme-main
      pointer-events: none
      text-decoration: none
    &.is-success
      background-color: $green
    &.is-error
      background-color: $red
  \::-moz-selection
    background: $yellow
    color: $scheme-invert-ter
  \::selection
    background: $yellow
    color: $scheme-invert-ter

.intro-buttons
  margin-top: 1.5rem
  .button
    padding-left: 1.375em
    padding-right: 1.375em

.intro-video
  background-color: $scheme-main
  margin-left: auto
  margin-right: auto
  max-width: 640px
  position: relative
  &.has-loaded
    .intro-spinner
      display: none
    .intro-iframe
      opacity: 1

@keyframes introSpinner
  from
    opacity: 0
    transform: scale(1.14)
  to
    opacity: 1
    transform: scale(1)

.intro-spinner,
.intro-shadow
  animation-duration: 500ms
  animation-easing-function: ease-out
  animation-fill-mode: both
  transform-origin: center

.intro-spinner
  +overlay
  animation-name: introSpinner
  &::before
    +loader
    border-bottom-color: $primary
    border-left-color: $primary
    height: 1.5em
    left: calc(50% - 0.75em)
    position: absolute
    top: calc(50% - 0.75em)
    width: 1.5em

@keyframes introShadow
  from
    opacity: 0
    transform: scale(0.86)
  to
    opacity: 1
    transform: scale(1)

.intro-shadow
  +overlay
  background-color: #776e70
  background-position: center center
  background-repeat: no-repeat
  background-size: cover
  box-shadow: 0 1.5rem 3rem rgba(#000, 0.2)
  animation-name: introShadow

.intro-iframe
  opacity: 0
  // padding-top: 52.8125%
  padding-top: 56.25%
  position: relative
  transition-duration: 500ms
  transition-property: opacity
  iframe
    height: 100%
    left: 0
    position: absolute
    top: 0
    width: 100%

.intro-author
  color: $text-light
  font-size: $size-small
  margin-top: 40px
  text-align: center
  a
    color: $text-strong
    &:hover
      text-decoration: underline
  span
    opacity: 0.5
    transition: 100ms opacity
    &:hover
      opacity: 1

.intro-carbon
  margin-top: 1.5rem

+mobile
  .intro-buttons
    .button
      display: flex
      width: 100%
      &.is-light
        margin-top: 0.5rem

+tablet
  .intro-title
    font-size: 2.25rem
  .intro-buttons
    align-items: center
    display: flex
    justify-content: space-between

+touch
  .intro-column.is-video
    margin-top: 3rem

+desktop
  .intro-columns
    display: flex
    justify-content: center
  .intro-column
    width: calc(50% - 1.5rem)
    &.is-content
      margin-right: 1.5rem
    &.is-video
      margin-left: 1.5rem
  .intro-content
    max-width: 440px
  .intro-title
    margin-top: -11px
    &:not(:last-child)
      margin-bottom: 10px
