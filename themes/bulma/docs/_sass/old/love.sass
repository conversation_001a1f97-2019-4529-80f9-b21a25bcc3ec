.bd-testimonials
  background-color: $background

.bd-testimonial
  align-items: flex-start
  display: flex
  justify-content: center

.bd-testimonial-tweet
  background-color: $scheme-main

.bd-more-loves
  align-items: center
  display: flex
  justify-content: center
  margin-top: 1.5rem
  text-align: center
  .button
    height: auto
    padding: 0.75em 1.5em
    span
      transform-origin: center center
      transition: transform $speed $easing
    &:hover
      span
        transform: scale(1.04)

+mobile
  .bd-testimonials
    padding: 1.5rem
  .bd-testimonial
    margin-bottom: 1.5rem

+tablet
  .bd-testimonials
    padding: 3rem
  .bd-testimonial + .bd-testimonial
    margin-top: 1.5rem

+desktop
  .bd-testimonials
    min-height: 595px

+widescreen
  .bd-testimonials
    min-height: 653px

+fullhd
  .bd-testimonials
    min-height: 632px

.bd-rainbow
  animation: rainbow 8s ease infinite
  background-image: linear-gradient(124deg, $orange, $red, $purple, $blue)
  background-size: 800% 800%

.hero.bd-is-love
  .title,
  .subtitle
    color: $scheme-main

@keyframes rainbow
    0%
      background-position: 1% 80%
    50%
      background-position: 99% 20%
    100%
      background-position: 1% 80%

.bd-hug
  align-items: flex-start
  display: flex
  justify-content: center

+mobile
  .bd-hug
    margin: 1.5rem
  .bd-embrace
    text-align: center
    &:not(:first-child)
      margin-top: 1.5rem
    &:not(:last-child)
      margin-bottom: 1.5rem
  .bd-embrace-button
    margin-top: 0.75rem

+tablet
  .bd-embrace
    align-items: center
    display: flex
    justify-content: center
    &:not(:first-child)
      margin-top: 3rem
    &:not(:last-child)
      margin-bottom: 3rem
  .bd-embrace-button
    margin-left: 1.5rem
  .bd-hugs
    display: flex
    flex-wrap: wrap
    padding-bottom: 3rem
  .bd-hug
    margin-top: 1.5rem
    width: calc(33.3333% - 1rem)
    &:nth-child(1),
    &:nth-child(2),
    &:nth-child(3)
      margin-top: 0
    &:nth-child(3n-1),
    &:nth-child(3n)
      margin-left: 1.5rem

.bd-shoutout
  background-color: $background
  padding: 2rem
  text-align: center
  &:not(:last-child)
    margin-bottom: 3rem

.bd-shoutout-text
  &.title
    opacity: 0.8
  &.subtitle
    opacity: 0.5

.bd-shoutout-button
  margin-top: -0.75rem
