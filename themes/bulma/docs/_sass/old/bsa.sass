.bsa
  padding: 2rem

.bsa-cpc
  min-height: 1px

#_default_
  .default-ad
    background-color: rgba(black, 0.3)
    border-radius: 2px
    color: $scheme-main
    display: inline-block
    font-size: 10px
    font-weight: bold
    padding: 0 4px
    text-transform: uppercase
    vertical-align: top
  & > a
    background-color: $scheme-main
    border-radius: $radius-large
    box-shadow: 0 2px 3px rgba($scheme-invert, 0.1), 0 0 0 1px rgba($scheme-invert, 0.1)
    color: $text
    display: block
    line-height: 1.375
    margin-top: 15px
    min-height: 70px
    padding: 15px
    padding-left: 70px
    position: relative
    &:hover,
    &:focus
      box-shadow: 0 2px 3px rgba($scheme-invert, 0.1), 0 0 0 1px $link
    &:active
      box-shadow: inset 0 1px 2px rgba($scheme-invert, 0.2), 0 0 0 1px $link
    span
      display: block
    .default-image
      display: block
      left: 15px
      height: 40px
      position: absolute
      top: 15px
      width: 40px
      img
        display: block
        height: 40px
        width: 40px
    .default-title
      color: $text-strong
      display: inline
      font-weight: $weight-bold
      &::after
        content: " — "
    .default-description
      display: inline

+tablet
  .bsa
    .columns
      min-height: 120px
  #_default_
    display: flex
    justify-content: center
    position: relative
    .default-ad
      left: 100%
      margin-left: 2rem
      position: absolute
      top: 0
    & > a
      margin: 0
      width: calc(50% - 1rem)
      &:not(:nth-child(2))
        margin-left: 2rem
