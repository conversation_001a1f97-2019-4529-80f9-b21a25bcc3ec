$book-beige: #FFEDD7

@keyframes fadeIn
  from
    opacity: 0
  to
    opacity: 1

@keyframes zoomIn
  from
    transform: scale(0.8)
  to
    transform: scale(1)

.bd-book-banner
  background-color: $primary
  position: relative

.bd-book-pattern,
.bd-book-modal-column.bd-is-cover
  background-image: url("/images/hab/<EMAIL>")
  background-repeat: repeat
  background-size: 250px 150px
  &::before
    +overlay
    background-color: rgba($book-beige, 0.1)
    content: ""
    display: block

.bd-book-pattern
  +overlay

.bd-book-header
  position: relative

.bd-book-cover
  padding-top: 0.75rem
  position: relative
  text-align: center
  a
    display: inline-block
    vertical-align: top
  img
    display: block

.bd-book-content
  @extend %bd-box
  box-shadow: 0 3rem 3rem -1rem rgba($scheme-invert, 0.2)
  max-width: 520px
  padding: 3rem

.bd-book-description
  max-width: 340px

.bd-book-tags
  margin-bottom: 1.5rem
  .tags
    .tag
      margin-right: 1px

.bd-book-buttons
  .button
    height: auto
    padding-bottom: calc(1em - 1px)
    padding-top: calc(0.5em - 1px)
    & > span
      font-size: 0.875em
      & > em
        display: block
        font-size: 0.5em
        font-style: normal
        line-height: 1em
        margin-bottom: 0.5em
        opacity: 0.5
    img
      display: block
      max-height: 24px
    &.bd-is-bleeding
      background-color: $bleeding-green
      border-color: transparent
      &:hover
        background-color: darken($bleeding-green, 5%)
    &.bd-is-amazon
      background-color: $amazon
      border-color: transparent
      &:hover
        background-color: darken($amazon, 5%)
      img
        margin-bottom: -11px

.bd-book-columns
  align-items: center
  display: flex
  justify-content: center
  margin-left: auto
  margin-right: auto
  max-width: 1080px

.bd-book-modal-background
  +overlay
  background-color: rgba($scheme-invert, 0.86)

.bd-book-modal
  .bd-book-modal-background,
  .modal-content
    animation-duration: 250ms
    animation-easing-function: ease-out
    animation-fill-mode: both
  .bd-book-modal-background
    animation-name: fadeIn
  .modal-content
    animation-name: zoomIn
    transform-origin: center
.bd-book-modal-cover
  padding: 2rem
  position: relative

.bd-book-modal-columns
  align-items: stretch
  display: flex
  justify-content: center

+mobile
  .bd-book-columns
    flex-direction: column
  .bd-book-buttons
    .button
      width: 100%
      &:not(:last-child)
        margin-right: 0
        margin-bottom: 1rem
  .bd-book-modal
    .bd-book-content
      padding: 2rem
  .bd-book-modal-column.bd-is-cover
    display: none

+tablet
  .bd-book-columns
    justify-content: space-around
  .bd-book-header
    .tag
      position: absolute
      right: calc(100% + 1.25rem)
      top: 0.5rem
  .bd-book-buttons
    .button
      width: calc(50% - 0.5rem)
  // Modal
  .bd-book-modal
    .modal-content
      width: 960px
  .bd-book-inline-cover
    display: none
  .bd-book-modal-column
    background-color: $scheme-main
    position: relative
    &.bd-is-cover
      align-items: center
      display: flex
      justify-content: center
      padding: 2rem
    &.bd-is-content
      .bd-book-content
        box-shadow: none
