.cpck-window {
  background-color: $white;
  box-shadow: 0 0 1em rgba(black, 0.1);
  padding: 1em 1.5em;
  position: fixed;
  z-index: 60;
}

.cpck-invisible {
  display: none !important;
}

.cpck-link {
  border-bottom: 1px solid $link;
  padding-bottom: 2px;

  &:hover {
    border-bottom-width: 2px;
  }

  &:active {
    background-color: $link-light;
  }
}

.cpck-compliance {
  margin-top: 1em;
}

.cpck-btn {
  border: 2px solid $success;
  border-radius: $radius;
  color: $success;
  display: inline-block;
  font-weight: $weight-bold;
  padding: 0.5em 1em;
  vertical-align: top;

  span {
    margin-left: -0.125em;
    margin-right: 0.5em;
  }

  &:hover {
    background-color: $success-light;
    color: $success-dark;
  }

  &:active,
  &:focus {
    background-color: darken($success-light, 5%);
    border-color: $success-dark;
    color: darken($success-dark, 5%);
    outline: none;
  }
}

@include mobile {
  .cpck-window {
    bottom: 0;
    left: 0;
    right: 0;
  }
}

@include tablet {
  .cpck-window {
    border-radius: $radius;
    bottom: 1em;
    right: 1em;
    width: 20rem;
  }
}
