$bootstrap: #6f5499;
$bootstrap-invert: #fff;

.bd-bootstrap-table {
  margin: 3em 0;
}

.bd-pros-heading {
  padding: 0 2em;
  text-align: center;
}

.bd-pros-icon {
  margin-bottom: 3em;
  text-align: center;

  svg {
    height: 3em;
    width: auto;
  }
}

.bd-pros-list {
  margin: 0 auto;
  max-width: 540px;
}

.bd-pro {
  .icon {
    font-size: 1.25em;
    height: 1.125em;
    position: relative;
    top: -1px;
    width: 1.125em;
  }

  .title {
    font-size: 1.25em;
    margin-bottom: 0.5em;
  }
}

.bd-pro + .bd-pro {
  margin-top: 2em;
  padding-top: 2em;
}

.bd-pro-content {
  p:not(:last-child) {
    margin-bottom: 0.5em;
  }
}

.bd-pro.bd-is-bulma {
  .icon {
    color: $primary;
  }
}

.bd-pro.bd-is-bootstrap {
  .icon {
    color: $bootstrap;
  }
}

.bd-bootstrap-comparison {
  margin: 0 auto;
  max-width: 42em;

  .table {
    color: $red;

    thead,
    tfoot {
      th {
        font-size: 1.5em;
        text-align: center;
      }

      svg {
        height: 1.5em;
        margin-right: 1em;
        position: relative;
        top: 0.25em;
        width: auto;
      }
    }

    tbody {
      th {
        font-size: 1.25em;
        text-align: center;
      }

      td {
        font-family: $family-monospace;
        width: 50%;
      }

      a {
        color: currentColor;

        &:hover {
          text-decoration: underline;
        }
      }
    }

    .bd-is-empty {
      background-color: $background;
      color: $text-light;
    }

    .bd-is-unique {
      background-color: rgba($green, 0.25);
      color: $text-strong;
      font-weight: $weight-bold;
    }
  }
}

.bd-bootstrap-comparison-header {
  margin-bottom: 3em;
}

@include desktop {
  .bd-bootstrap-table {
    font-size: 1.125rem;
  }
}

@include widescreen {
  .bd-bootstrap-table {
    font-size: 1.25rem;
  }
}
