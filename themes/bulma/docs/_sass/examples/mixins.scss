.bulma-arrow-mixin {
  @include arrow(purple);
}

.bulma-hamburger-mixin {
  @include hamburger(2.5rem);
}

.bulma-delete-mixin {
  @include delete;
}

.bulma-loader-mixin {
  @include loader;
}

.bulma-block-mixin {
  @include block(1rem);
}

.bulma-fa-mixin {
  @include fa(1rem, 2rem);
  background-color: lavender;
}

.bulma-overlay-mixin-parent {
  background-image: url(https://source.unsplash.com/La2kOu2dmH4/640x320);
  background-size: cover;
  border-radius: 0.5em;
  position: relative;
  height: 11.25rem;
  width: 20rem;
}

.bulma-overlay-mixin {
  @include overlay(1.5rem);
  background-color: darkorange;
  border-radius: 0.25em;
  color: white;
  opacity: 0.9;
  padding: 1em;
}

.bulma-placeholder-mixin {
  @include placeholder {
    color: lightblue;
  }

  border-color: hsl(195deg, 53%, 79%);

  &:hover {
    border-color: hsl(195deg, 83%, 66%);
  }

  &:focus,
  &:active {
    border-color: hsl(195deg, 100%, 56%) !important;
    box-shadow: 0 0 0 0.125em hsla(195deg, 100%, 56%, 0.25) !important;
  }
}

.bulma-clearfix-mixin {
  @include clearfix;

  img {
    border-radius: 0.25em;
    margin-right: 0.5em;
  }
}

.bulma-center-mixin-parent {
  background-image: url(https://source.unsplash.com/xo3Bd2tYeqg/640x320);
  background-size: cover;
  border-radius: 0.5em;
  position: relative;
  height: 11.25rem;
  width: 20rem;
}

.bulma-center-mixin {
  @include center(96px);
  border-radius: 8px;
  height: 96px;
  width: 96px;
}

.bulma-reset-mixin {
  @include reset;
}

.bulma-unselectable-mixin {
  @include unselectable;
}

.bulma-ltr-rtl-mixin {
  background-color: lightgreen;
  padding: 0.5em 1em;
  border: 1px solid seagreen;
  margin-right: -1px;

  &:first-child {
    @include ltr {
      border-bottom-left-radius: 0.5em;
      border-top-left-radius: 0.5em;
    }

    @include rtl {
      border-bottom-right-radius: 0.5em;
      border-top-right-radius: 0.5em;
    }
  }

  &:last-child {
    @include ltr {
      border-bottom-right-radius: 0.5em;
      border-top-right-radius: 0.5em;
    }

    @include rtl {
      border-bottom-left-radius: 0.5em;
      border-top-left-radius: 0.5em;
    }
  }
}

.bulma-ltr-position-mixin-parent {
  background-color: beige;
  border-radius: 0.5em;
  padding: 1rem;
  padding-left: 5rem;
  position: relative;

  p {
    margin-top: -0.25rem;
  }
}

.bulma-ltr-position-mixin {
  @include ltr-position(1rem, false);
  border-radius: 0.25em;
  position: absolute;
  top: 1rem;
}

.bulma-ltr-property-mixin-parent {
  align-items: flex-start;
  background-color: midnightblue;
  border-radius: 0.5em;
  color: lightskyblue;
  display: flex;
  padding: 1.5rem;
  position: relative;

  p {
    margin-top: -0.25rem;
  }
}

.bulma-ltr-property-mixin {
  @include ltr-property("margin", 1rem, false);
  border-radius: 0.25em;
}

.bulma-control-mixin {
  @include control;
  background: deeppink;
  color: white;

  &.is-small {
    @include control-small;
  }

  &.is-medium {
    @include control-medium;
  }

  &.is-large {
    @include control-large;
  }
}

.bulma-control-extend {
  @extend %control;
  background: mediumblue;
  color: white;
}
