.bd-vars {
  --bd-var-name-width: 100%;
  --bd-var-computed-type-width: 50%;
  --bd-var-type-width: 50%;
  --bd-var-computed-value-width: 50%;
  --bd-var-value-width: 50%;

  background-color: var(--hl-background);
  border-radius: $radius-large;
  color: var(--hl-color);
  padding: 3.25em;
  padding-top: 2em;

  .bd-anchor-title {
    color: $white;
  }
}

.bd-vars + .bd-vars {
  margin-top: var(--docs-inner);
}

.bd-var {
  display: flex;
  flex-wrap: wrap;
  font-family: $family-monospace;
  font-size: 0.75em;
  line-height: 1.375;

  .highlight {
    background: none;
    font-size: 1em;
    overflow: visible;

    pre {
      overflow: visible;
      padding: 0.25em 0.5em 0.375em;
      white-space: pre-wrap;
      white-space: break-spaces;
    }
  }

  .bd-copy {
    font-size: 0.75em !important;
    right: 0;
    top: 0;
  }

  &.bd-is-body {
    border-radius: $radius;

    &:hover {
      background-color: rgba(white, 0.1);
    }
  }

  &.bd-is-head {
    border-bottom: 2px solid rgba(white, 0.1);
    border-radius: 0;
    margin-bottom: 0.5em;
    padding-bottom: 0.5em;

    %bd-var-item {
      color: var(--hl-gray-02) !important;
    }
  }
}

.bd-var-name {
  @extend %bd-var-item;
  color: var(--hl-gray-01);
  width: var(--bd-var-name-width);
}

.bd-var-type {
  @extend %bd-var-item;
  color: var(--bd-var-type-color, var(--hl-gray-02));
  width: var(--bd-var-type-width);
}

.bd-var-value {
  @extend %bd-var-item;
  color: var(--hl-green);
  width: var(--bd-var-value-width);
}

.bd-var-computed-value {
  @extend %bd-var-item;
  color: var(--hl-yellow);
  width: var(--bd-var-computed-value-width);
}

.bd-var-computed-type {
  @extend %bd-var-item;
  color: var(--bd-var-type-color, var(--hl-purple));
  width: var(--bd-var-computed-type-width);
}

.bd-var {
  .bd-is-boolean {
    --bd-var-type-color: var(--hl-lavender);
  }

  .bd-is-color {
    --bd-var-type-color: var(--hl-orange);
  }

  .bd-is-compound {
    --bd-var-type-color: var(--hl-lavender);
  }

  .bd-is-font-weight {
    --bd-var-type-color: var(--hl-lavender);
  }

  .bd-is-function {
    --bd-var-type-color: var(--hl-pink);
  }

  .bd-is-size {
    --bd-var-type-color: var(--hl-pink);
  }

  .bd-is-shadow {
    --bd-var-type-color: var(--hl-lavender);
  }

  .bd-is-string {
    --bd-var-type-color: var(--hl-yellow);
  }

  .bd-is-variable {
    --bd-var-type-color: var(--hl-green);
  }
}

@include touch {
  %bd-var-item {
    padding: 0 1em;
  }

  .bd-vars {
    padding: 1em;
  }

  .bd-var {
    padding: 0.5em 0;
  }
}

@include desktop {
  %bd-var-item {
    padding: 0.25em 1em;
  }

  .bd-var.bd-is-head {
    .bd-var-name {
      padding-left: 0;
    }

    .bd-var-computed-type {
      padding-left: 2em;
      padding-right: 0;
    }
  }

  .bd-var.bd-is-body {
    margin-left: -1em;
    margin-right: -1em;
  }

  .bd-vars {
    --bd-var-name-width: 40%;
    --bd-var-computed-type-width: 10%;
    --bd-var-type-width: 10%;
    --bd-var-computed-value-width: 20%;
    --bd-var-value-width: 20%;
  }
}
