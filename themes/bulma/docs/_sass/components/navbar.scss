.bd-navbar {
  z-index: 50;

  .navbar-brand img {
    height: 28px;
    max-height: 2em;
    width: 112px;
  }

  .navbar-start {
    flex-grow: 1;
  }

  .navbar-item,
  .navbar-link {
    padding: 0.5em 0.75em;
  }

  .button.is-sponsor.is-light {
    color: var(--sponsor);
  }

  .bd-navbar-search-icon {
    justify-content: center;
    padding: 0;
    width: 3.25rem;
  }

  .navbar-item.has-dropdown {
    padding: 0;
  }

  .navbar-burger {
    margin-left: 0;
  }
}

.bd-navbar-item {
  transition-duration: $speed;
  transition-property: background-color, box-shadow, color;

  > .icon {
    margin-left: -0.25em;
    margin-right: 0.25em;
  }

  &:hover,
  &.is-active {
    border-radius: $radius;
    color: var(--color-dark) !important;
  }

  &:hover:not(.is-active) {
    background-color: var(--color-light) !important;
  }

  &.is-active {
    background-color: transparent !important;
    box-shadow: inset 0 0 0 2px var(--color-light) !important;
  }

  &.bd-navbar-item-documentation {
    --color-light: var(--primary-light);
    --color-dark: var(--primary-dark);
  }

  &.bd-navbar-item-videos {
    --color-light: var(--videos-light);
    --color-dark: var(--videos-dark);
  }

  &.bd-navbar-item-expo {
    --color-light: var(--expo-light);
    --color-dark: var(--expo-dark);
  }

  &.bd-navbar-item-love {
    --color-light: var(--love-light);
    --color-dark: var(--love-dark);
  }

  &.bd-navbar-item-backers {
    --color-light: var(--patreon-light);
    --color-dark: var(--patreon-dark);
  }

  &.bd-navbar-item-bulma-book {
    --color-light: var(--bleeding-light);
    --color-dark: var(--bleeding-dark);
  }

  &.bd-navbar-item-blog {
    --color-light: var(--rss-light);
    --color-dark: var(--rss-dark);
  }

  &.bd-navbar-item-brand {
    --color-light: var(--primary-light);
    --color-dark: var(--primary-dark);
  }

  &.bd-navbar-item-extensions {
    --color-light: var(--extensions-light);
    --color-dark: var(--extensions-dark);
  }

  &.bd-navbar-item-bulma-start {
    --color-light: var(--success-light);
    --color-dark: var(--success-dark);
  }

  &.bd-navbar-item-made-with-bulma {
    --color-light: var(--expo-light);
    --color-dark: var(--expo-dark);
  }
}

.bd-navbar-ellipsis {
  align-items: center;
  border-radius: $radius;
  color: $link;
  height: 2.25em;
  justify-content: center;
  padding: 0 !important;
  width: 2.25em;

  &::after {
    display: none !important;
  }
}

.bd-navbar-search-icon {
  @extend %reset;
  color: var(--search);
  cursor: pointer;
  margin-left: auto;

  &:hover {
    background-color: bulmaRgba(black, 0.05);
  }
}

.bd-search {
  .algolia-autocomplete,
  .control {
    display: flex;
    flex-grow: 1;
    flex-shrink: 1;
    width: 100%;
  }

  .control {
    > .icon.is-left {
      color: var(--search) !important;
    }
  }

  .input {
    background-color: $white-ter;
    border-color: transparent;
    box-shadow: none;

    &:hover {
      background-color: $white-bis;
      border-color: $border;
      box-shadow: 0 0 0 1px $border;
    }

    &:focus {
      background-color: transparent;
      border-color: var(--search);
      box-shadow: 0 0 0 1px var(--search);

      & ~ .icon {
        color: var(--search) !important;
      }
    }
  }

  .algolia-docsearch-suggestion--category-header {
    color: var(--search-dark);
    border-bottom-color: $border;
  }

  /* Category (eg. Downloads) */
  .algolia-docsearch-suggestion--subcategory-column {
    color: $text-light;
  }

  /* Title (eg. Bootstrap CDN) */
  .algolia-docsearch-suggestion--title {
    color: $text-strong;
  }

  /* Description description (eg. Bootstrap currently works...) */
  .algolia-docsearch-suggestion--text {
    color: $text;
  }

  /* Highlighted text */
  .algolia-docsearch-suggestion--highlight {
    background-color: var(--search-light);
    color: var(--search-dark);
  }

  .algolia-autocomplete
    .ds-dropdown-menu
    .ds-suggestion.ds-cursor
    .algolia-docsearch-suggestion.suggestion-layout-simple,
  .algolia-autocomplete
    .ds-dropdown-menu
    .ds-suggestion.ds-cursor
    .algolia-docsearch-suggestion:not(.suggestion-layout-simple)
    .algolia-docsearch-suggestion--content {
    background-color: var(--search-light);
  }

  .algolia-autocomplete
    .algolia-docsearch-suggestion--category-header
    .algolia-docsearch-suggestion--category-header-lvl0
    .algolia-docsearch-suggestion--highlight,
  .algolia-autocomplete
    .algolia-docsearch-suggestion--category-header
    .algolia-docsearch-suggestion--category-header-lvl1
    .algolia-docsearch-suggestion--highlight,
  .algolia-autocomplete
    .algolia-docsearch-suggestion--text
    .algolia-docsearch-suggestion--highlight {
    box-shadow: inset 0 -2px 0 0 var(--search);
  }
}

.bd-navbar-mobile-download-icon,
.bd-navbar-mobile-sponsor-icon {
  display: none !important;
}

.bd-navbar-start.bd-is-original {
  .bd-navbar-item-more:not(.bd-is-visible) {
    display: none;
  }

  .bd-navbar-dropdown .navbar-item.bd-is-hidden {
    &,
    & + .navbar-divider {
      display: none;
    }
  }
}

.bd-navbar.bd-is-clone {
  bottom: 0;
  pointer-events: none;
  position: fixed;
  visibility: hidden;
  z-index: -1;
}

@media screen and (min-width: 450px) and (max-width: $tablet - 1px) {
  .bd-navbar-mobile-download-icon,
  .bd-navbar-mobile-sponsor-icon {
    display: flex !important;
  }
}

@include until($navbar-breakpoint) {
  .bd-navbar {
    .navbar-start {
      > .bd-navbar-item:not(.has-dropdown) {
        display: flex;
      }
    }

    .navbar-end {
      display: none;
    }
  }

  .bd-navbar-item {
    .navbar-link:not(.is-arrowless) {
      display: none;
    }
  }

  .bd-navbar-item-more {
    display: none !important;
  }
}

@include from($navbar-breakpoint) {
  .bd-navbar {
    .navbar-menu,
    .navbar-start,
    .navbar-end {
      align-items: center;
    }
  }

  .bd-navbar-mobile-icon {
    display: none;
  }

  .bd-navbar-dropdown {
    left: auto;
    right: 0;
    width: 16rem;
    z-index: 200;

    .navbar-item {
      white-space: normal;
    }
  }
}

@include touch() {
  .bd-search {
    background-color: $scheme-main;
    box-shadow: 0 0.5em 1em rgba(black, 0.05);
    display: none;
    left: 0;
    padding: $navbar-padding-vertical;
    position: fixed;
    right: 0;
    top: $navbar-height;
    z-index: 100;

    &.bd-is-visible {
      display: block;
    }

    .algolia-autocomplete {
      .ds-dropdown-menu {
        left: 0px !important;
        min-width: 0px;
        right: auto;
      }
    }
  }

  .bd-navbar-search-desktop-icon {
    display: none;
  }
}

@include desktop() {
  .bd-navbar {
    padding: 1rem 2rem;

    .navbar-brand .navbar-item:first-child {
      margin-left: -0.75em;
    }

    .navbar-menu {
      flex-shrink: 1;
    }

    .navbar-start {
      flex-shrink: 1;
    }
  }

  .bd-search {
    align-items: center;
    display: flex;
    flex-shrink: 0;
    // margin-left: 0.5rem;
    width: 12rem;
  }

  .bd-navbar-search-mobile-icon {
    display: none;
  }
}

@include widescreen() {
  .bd-navbar {
    font-size: 1.125rem;
    // padding: 2rem 4rem;

    .navbar-brand img {
      height: 36px;
      width: 144px;
    }
  }

  .bd-navbar-dropdown {
    left: 0;
    right: auto;
  }

  .bd-search {
    width: 16rem;
  }
}

@include fullhd() {
  .bd-search {
    width: 16rem;
  }
}
