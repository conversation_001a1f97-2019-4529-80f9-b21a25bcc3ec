// .bd-highlight-full {
//   @extend %block;
// }

.highlight {
  background-color: var(--hl-gray-09);
  border-radius: $radius-large;
  color: var(--hl-gray-01);
  font-size: 0.75em;
  position: relative;

  &.bd-is-hovering {
    box-shadow: inset 0 0 0 2px var(--hl-orange);
  }

  pre {
    background-color: transparent;
    color: currentColor;
    font-size: 1em;
    line-height: 1.375;
    margin: 0 !important;
    padding: 1.25em 1.5em;
    // white-space: pre-wrap;
    // white-space: break-spaces;
    white-space: pre;
    word-break: break-word;
  }

  .c {
    color: var(--hl-gray-01);
  }

  .err,
  .g {
    color: var(--hl-gray-02);
  }

  .k {
    color: var(--hl-cyan);
  }

  .l,
  .n {
    color: var(--hl-gray-02);
  }

  .o {
    color: var(--hl-cyan);
  }

  .x {
    color: var(--hl-pink);
  }

  .p {
    color: var(--hl-gray-02);
  }

  .cm {
    color: var(--hl-gray-01);
  }

  .cp {
    color: var(--hl-cyan);
  }

  .c1 {
    color: var(--hl-gray-02);
  }

  .cs {
    color: var(--hl-cyan);
  }

  .gd {
    color: var(--hl-green);
  }

  .ge {
    color: var(--hl-gray-02);
    font-style: italic;
  }

  .gr {
    color: var(--hl-orange);
  }

  .gh {
    color: var(--hl-pink);
  }

  .gi {
    color: var(--hl-cyan);
  }

  .go,
  .gp {
    color: var(--hl-gray-02);
  }

  .gs {
    color: var(--hl-gray-02);
    font-weight: bold;
  }

  .gu {
    color: var(--hl-pink);
  }

  .gt {
    color: var(--hl-gray-02);
  }

  .kc {
    color: var(--hl-pink);
  }

  .kd {
    color: var(--hl-blue);
  }

  .kn,
  .kp {
    color: var(--hl-cyan);
  }

  .kr {
    color: var(--hl-blue);
  }

  .kt {
    color: var(--hl-orange);
  }

  .ld {
    color: var(--hl-gray-02);
  }

  .m,
  .s {
    color: var(--hl-green);
  }

  .na {
    color: var(--hl-yellow);
  }

  .nb {
    color: var(--hl-orange);
  }

  .nc {
    color: var(--hl-yellow);
  }

  .no {
    color: var(--hl-pink);
  }

  .nd {
    color: var(--hl-blue);
  }

  .ni,
  .ne {
    color: var(--hl-pink);
  }

  .nf {
    color: var(--hl-blue);
  }

  .nl,
  .nn,
  .nx,
  .py {
    color: var(--hl-rose);
  }

  .nt,
  .nv {
    color: var(--hl-blue);
  }

  .ow {
    color: var(--hl-cyan);
  }

  .w {
    color: var(--hl-gray-02);
  }

  .mf,
  .mh,
  .mi,
  .mo {
    color: var(--hl-green);
  }

  .sb {
    color: var(--hl-gray-01);
  }

  .sc {
    color: var(--hl-green);
  }

  .sd {
    color: var(--hl-gray-02);
  }

  .s2 {
    color: var(--hl-green);
  }

  .se {
    color: var(--hl-pink);
  }

  .sh {
    color: var(--hl-gray-02);
  }

  .si,
  .sx {
    color: var(--hl-yellow);
  }

  .sr {
    color: var(--hl-orange);
  }

  .s1,
  .ss {
    color: var(--hl-green);
  }

  .bp,
  .vc,
  .vg,
  .vi {
    color: var(--hl-blue);
  }

  .il {
    color: var(--hl-green);
  }
}

.content .highlight {
  margin-left: 0;
  margin-right: 0;
  text-align: left;

  &:not(:first-child) {
    margin-top: 1em;
  }

  &:not(:last-child) {
    margin-bottom: 1em;
  }
}

.content li .highlight {
  margin-top: 0.5em;
}
