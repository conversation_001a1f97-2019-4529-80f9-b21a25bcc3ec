.bd-klmn {
  margin-bottom: 1.5rem;
}

.bd-klmn-gaps {
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  margin-bottom: 1.25rem;
}

.bd-klmn-label {
  margin: 0.25rem auto 0.25rem 0;
}

.bd-klmn-value,
.bd-klmn-gap {
  background-color: var(--hl-gray-09);
  border-radius: $radius;
  color: var(--hl-orange);
  font-family: $family-monospace;
  font-size: 0.75rem;
  margin: 0.25rem 0 0.25rem 0.5rem;
  padding: 0.375em 0.75em;
  text-align: center;
  vertical-align: middle !important;
  white-space: nowrap;

  &.bd-is-selected {
    background-color: var(--hl-orange);
    color: var(--hl-gray-09);
  }
}

.bd-klmn-value {
  background-color: var(--hl-orange);
  color: var(--hl-gray-09);
  font-size: 1rem;
}

.bd-klmn-columns:last-child {
  .bd-notification {
    font-size: 0.75rem;
    white-space: nowrap;
  }
}
