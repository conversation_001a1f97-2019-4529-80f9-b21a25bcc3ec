.bd-links {
  display: grid;
  grid-gap: 0;
  grid-template-columns: repeat(auto-fill, minmax(20em, 1fr));
}

.bd-link {
  border-radius: $radius-large;
  color: $text-light;
  display: flex;
  font-size: 1.25em;
  padding: 1em;

  &:hover {
    background-color: var(--primary-light);
    color: var(--primary-dark);
  }
}

.bd-link-title {
}

.bd-link-icon {
  color: var(--primary);
  flex-grow: 0;
  flex-shrink: 0;
  height: 1.5em;
  margin-right: 0.5em;
  width: 1.5em;
}

.bd-link-title {
  color: $text-strong;
  display: block;
  font-weight: $weight-semibold;
  line-height: 1.5;
}

.bd-link-subtitle {
  font-size: 0.75em;
}
