.bd-spacing-table {
  &:hover {
    color: $border;
    code {
      background: none;
      color: $border;
    }
  }
  .bd-current-row {
    background-color: $background;
    &:first-child {
      background-color: $danger-light;
      color: $text-strong;
      code {
        background-color: $danger;
        color: $danger-invert;
      }
    }
    &.bd-current-column {
      background-color: $primary-light;
      color: $text-strong;
      code {
        background-color: $primary;
        color: $primary-invert;
      }
    }
  }
  .bd-current-column {
    background-color: $background;
  }
  .bd-current-value {
    background-color: $danger-light;
    code {
      background-color: $danger;
      color: $danger-invert;
    }
  }
}
