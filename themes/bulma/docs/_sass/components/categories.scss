%bd-category-link {
  --highlight-background-color: var(--primary);
  --highlight-color: var(--primary-invert);

  align-items: center;
  border-radius: $radius;
  color: $text-strong;
  cursor: pointer;
  display: flex;
  line-height: var(--height);
  padding: 0.375em 0.75em;
  transition-duration: $speed;
  transition-property: background-color, color;

  &:hover {
    background-color: var(--primary-invert);
    color: var(--primary);
  }

  &.bd-is-focused {
    box-shadow: inset 0 0 0 2px $scheme-main-bis, 0 0 0 2px var(--primary);
  }
}

.bd-highlight {
  background-color: var(--highlight-background-color);
  border-radius: $radius-small;
  box-shadow: 0 0 0 2px var(--highlight-background-color);
  color: var(--highlight-color);
}

.bd-categories {
  @include overflow-touch;
  --height: 1.25;

  max-height: 100vh;
  overflow-y: auto;
  padding: var(--docs-side-padding);
  position: sticky;
  top: 0;

  &.bd-has-query {
    .bd-category:not(.bd-has-match) {
      display: none;
    }

    .bd-category-list {
      display: block;

      a:not(.bd-is-match) {
        display: none;
      }
    }

    &.bd-has-no-results {
      .bd-category-group {
        display: none;
      }

      .bd-categories-no-results {
        display: block;
      }
    }
  }
}

.bd-category {
  &.is-current {
    .bd-category-name {
      --highlight-background-color: var(--primary-dark);
      --highlight-color: var(--primary-light);

      background-color: var(--primary-light);
      color: var(--primary-dark);

      &.is-active {
        background-color: var(--primary);
        color: var(--primary-invert);
      }
    }

    .bd-category-list {
      display: block;
    }
  }

  &.is-open {
    .bd-category-list {
      display: block;
    }
  }
}

.bd-category-group {
  color: $grey-light;
  font-size: 0.625em;
  font-weight: $weight-semibold;
  letter-spacing: 0.2em;
  margin-bottom: calc(var(--docs-side-padding) / 2);
  margin-top: var(--docs-side-padding);
  text-transform: uppercase;
}

.bd-category-header {
  position: relative;
}

.bd-category-name {
  @extend %bd-category-link;
  font-weight: $weight-semibold;
}

.bd-category-toggle {
  @extend %reset;

  border-radius: $radius;
  color: var(--primary);
  cursor: pointer;
  display: none;
  height: calc(1em * var(--height));
  margin-left: auto;
  position: absolute;
  right: 0.5em;
  top: 0.5em;
  width: calc(1em * var(--height));

  &:hover {
    background-color: var(--primary);
    color: var(--primary-invert);
  }
}

.bd-category-list {
  display: none;
  padding: 0.25em 0.75em 0.75em;

  li {
    align-items: flex-start;
    display: flex;
    font-size: 0.875em;

    a {
      @extend %bd-category-link;
    }

    &.is-current {
      a {
        --highlight-background-color: var(--primary-invert);
        --highlight-color: var(--primary-dark);

        background-color: var(--primary);
        color: var(--primary-invert);
      }
    }
  }
}

.bd-categories-no-results {
  display: none;

  .bd-key {
    margin-left: 0.5em;
  }
}

.bd-categories-filter {
  margin-bottom: 1em;
  position: relative;

  .input:focus {
    border-color: $primary;
    box-shadow: 0 0 0 0.125em rgba($primary, 0.25);
  }

  .bd-key {
    position: absolute;
    right: 6px;
    top: 4px;
  }
}

.bd-key {
  align-items: center;
  background: rgba(black, 0.1);
  border-radius: 0.25em;
  color: rgba(black, 0.5);
  display: none;
  font-family: monospace;
  font-size: 0.625rem;
  height: 2em;
  justify-content: center;
  min-width: 2em;
  padding: 0 0.25em;
  pointer-events: none;
  text-align: center;
  z-index: 2;
}
