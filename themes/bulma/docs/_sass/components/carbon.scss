$carbon-spacing: 1rem;
$carbon-shadow-size: 0.75rem;
$carbon-image-height: 100px;
$carbon-image-width: 130px;

#carboncontainer {
  align-items: center;
  display: flex;
  justify-content: center;
  max-width: $carbon-width;
  min-height: $carbon-min-height;
  min-width: 280px;
}

#carbon {
  border-radius: 0.125em;
  flex-grow: 1;
  min-height: $carbon-height;
  padding: 0;
  position: relative;

  &:hover {
    background-color: rgba(black, 0.02);
    box-shadow: 0 0 0 $carbon-shadow-size rgba(black, 0.02);
  }

  @include tablet() {
    width: $carbon-width;
  }
}

#carbonads {
  font-size: 14px;
  text-align: left;

  a,
  span {
    display: block;
  }

  .carbon-wrap {
    min-height: $carbon-height;
    position: relative;

    &:hover {
      .carbon-img::after {
        background-color: rgba(#000, 0.05);
        content: '';
        display: block;
        height: $carbon-height;
        left: 0;
        position: absolute;
        right: $carbon-spacing;
        top: 0;
      }
    }
  }

  .carbon-img {
    bottom: 0;
    float: left;
    left: 0;
    min-height: $carbon-height;
    padding: 0;
    position: absolute;
    top: 0;
    width: calc(#{$carbon-image-width} + #{$carbon-spacing});

    img {
      display: block;
      height: $carbon-image-height;
      width: $carbon-image-width;
    }

    &:hover {
      & + .carbon-text {
        color: $link;
      }
    }

    &:active {
      opacity: 0.8;
    }
  }

  .carbon-text {
    display: block;
    color: $text-strong;
    line-height: $carbon-line-height;
    min-height: $carbon-height;
    padding: 0 0 $carbon-poweredby-height
      calc(#{$carbon-image-width} + #{$carbon-spacing});

    &:hover {
      color: $link;
    }
  }

  .carbon-poweredby {
    bottom: 0;
    color: $border-hover;
    display: inline;
    font-size: $size-small;
    line-height: $carbon-poweredby-height;
    position: absolute;
    right: 0;

    &:hover {
      text-decoration: underline;
    }
  }
}
