.bd-features {
  padding: var(--docs-side-padding);
  padding-bottom: 0;
}

.bd-features-title {
  color: $text-light;
  font-size: 0.75em;
  opacity: 0.5;
}

.bd-features-list {
  padding: 0.25em 0 0;

  li {
    display: flex;
    font-size: 0.875em;
    justify-content: space-between;
    // padding: 0 0.75em;
  }
}

.bd-feature-label {
  color: $text-light;
  // padding: 0.375em 0;
}

.bd-feature-since,
.bd-feature-yes,
.bd-feature-no {
  // padding: 0.375em 0.75em;
}

.bd-feature-no {
  color: $text-light;
}

.bd-feature-yes {
  color: var(--primary);
  font-weight: $weight-bold;
}
