#grid {
  .notification {
    padding-left: 0;
    padding-right: 0;
  }
}

.bd-columns-tools {
  margin-top: 3rem;
}

.bd-columns-tool {
  @extend %center;

  .highlight {
    font-size: 0.875em;
  }

  &.bd-is-try {
    .buttons {
      justify-content: center;
    }
    .button {
      strong {
        font-weight: $weight-semibold;
      }
    }
  }
}

#markup {
  width: 100%;

  .highlight pre {
    max-height: none;
  }
}

#message {
  display: none;
  margin-top: 3rem;
}

@include mobile {
  .bd-columns-tool {
    &.bd-is-markup {
      margin-top: 3rem;
    }
  }
}

@include tablet {
  .bd-columns-tools {
    align-items: flex-start;
    display: flex;
  }

  .bd-columns-tool {
    width: 50%;

    &.bd-is-try {
      padding-top: 60px;
    }
  }
}
